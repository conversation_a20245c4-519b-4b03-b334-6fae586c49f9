"use strict";(()=>{var e={};e.id=5989,e.ids=[5989],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},15506:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>g,serverHooks:()=>N,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>h,PUT:()=>f});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(19854),l=t(41098),c=t(79464),p=t(99326),d=t(96330),x=t(45697);let b=x.Ik({name:x.Yj().min(1).optional(),number:x.Yj().min(1).optional(),capacity:x.ai().min(1).max(100).optional(),floor:x.ai().optional(),building:x.Yj().optional(),branch:x.Yj().optional(),equipment:x.Yj().optional(),notes:x.Yj().optional(),isActive:x.zM().optional()});async function h(e,{params:r}){try{let e=await (0,u.getServerSession)(l.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r,s=await c.z.cabinet.findUnique({where:{id:t},include:{groups:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}},_count:{select:{enrollments:!0}}}},schedules:{include:{group:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}},orderBy:[{dayOfWeek:"asc"},{startTime:"asc"}]},_count:{select:{groups:!0,schedules:!0}}}});if(!s)return i.NextResponse.json({error:"Cabinet not found"},{status:404});return i.NextResponse.json(s)}catch(e){return console.error("Error fetching cabinet:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:r}){try{let t=await (0,u.getServerSession)(l.N);if(!t?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER"].includes(t.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await e.json(),o=b.parse(n),a=await c.z.cabinet.findUnique({where:{id:s}});if(!a)return i.NextResponse.json({error:"Cabinet not found"},{status:404});if(o.number||o.branch){let e=o.number||a.number,r=o.branch||a.branch;if(await c.z.cabinet.findFirst({where:{number:e,branch:r,id:{not:s}}}))return i.NextResponse.json({error:"Cabinet number already exists in this branch"},{status:400})}let x=await c.z.cabinet.update({where:{id:s},data:o,include:{groups:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},_count:{select:{groups:!0,schedules:!0}}}});return await p._.log({userId:t.user.id,userRole:t.user.role||d.Role.ADMIN,action:"UPDATE",resource:"CABINET",resourceId:x.id,details:`Updated cabinet ${x.name} (${x.number})`}),i.NextResponse.json(x)}catch(e){if(e instanceof x.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating cabinet:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let e=await (0,u.getServerSession)(l.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!e.user.role||!["ADMIN"].includes(e.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let{id:t}=await r,s=await c.z.cabinet.findUnique({where:{id:t},include:{groups:!0,schedules:!0}});if(!s)return i.NextResponse.json({error:"Cabinet not found"},{status:404});if(s.groups.length>0)return i.NextResponse.json({error:"Cannot delete cabinet with assigned groups",details:`Cabinet has ${s.groups.length} group(s). Please reassign groups first.`},{status:400});return await c.z.cabinet.delete({where:{id:t}}),await p._.log({userId:e.user.id,userRole:e.user.role||d.Role.ADMIN,action:"DELETE",resource:"CABINET",resourceId:t,details:`Deleted cabinet ${s.name} (${s.number})`}),i.NextResponse.json({message:"Cabinet deleted successfully"})}catch(e){return console.error("Error deleting cabinet:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/cabinets/[id]/route",pathname:"/api/cabinets/[id]",filename:"route",bundlePath:"app/api/cabinets/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\cabinets\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:j,workUnitAsyncStorage:w,serverHooks:N}=g;function R(){return(0,a.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:w})}},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(15506));module.exports=s})();