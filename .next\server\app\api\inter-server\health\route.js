(()=>{var e={};e.id=2309,e.ids=[2309],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=a.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},o=await fetch(s,{method:r.method,headers:n,body:r.data?JSON.stringify(r.data):void 0}),i=await o.json();return{success:o.ok,data:i,status:o.status,error:o.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>a,cU:()=>o,g2:()=>s});class o{static async authenticateUser(e,r){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class a{static logRequest(e,r,t,s){let n=new Date().toISOString(),o=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:o,success:t,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77550:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>u});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),c=t(7786);async function p(e){try{if(!(0,c.g2)(e))return i.NextResponse.json({error:"Unauthorized inter-server request"},{status:401});c.LQ.logRequest("incoming","/api/inter-server/health",!0);let r=c.LQ.getServerConfig();return i.NextResponse.json({status:"healthy",server:r.serverType,timestamp:new Date().toISOString(),version:process.env.npm_package_version||"1.0.0",environment:process.env.APP_ENV||"development"})}catch(e){return console.error("Inter-server health check error:",e),i.NextResponse.json({error:"Health check failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}async function u(e){try{if(!(0,c.g2)(e))return i.NextResponse.json({error:"Unauthorized inter-server request"},{status:401});let{includeDetails:r=!1}=await e.json(),t=c.LQ.getServerConfig(),s={status:"healthy",server:t.serverType,timestamp:new Date().toISOString()};return r&&(s.details={database:"connected",memory:process.memoryUsage(),uptime:process.uptime(),nodeVersion:process.version,environment:"production"}),c.LQ.logRequest("incoming","/api/inter-server/health",!0,{includeDetails:r}),i.NextResponse.json(s)}catch(e){return console.error("Detailed health check error:",e),i.NextResponse.json({error:"Detailed health check failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/inter-server/health/route",pathname:"/api/inter-server/health",filename:"route",bundlePath:"app/api/inter-server/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\inter-server\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:h,serverHooks:v}=d;function R(){return(0,a.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:h})}},78335:()=>{},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(77550));module.exports=s})();