"use strict";(()=>{var e={};e.id=1803,e.ids=[1803],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},88374:(e,s,r)=>{r.r(s),r.d(s,{patchFetch:()=>h,routeModule:()=>w,serverHooks:()=>N,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>R});var t={};r.r(t),r.d(t,{DELETE:()=>f,GET:()=>x,PUT:()=>A});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(19854),d=r(41098),p=r(79464),l=r(99326),c=r(45697);let m=c.z.object({testName:c.z.string().optional(),type:c.z.enum(["LEVEL_TEST","PROGRESS_TEST","FINAL_EXAM","GROUP_TEST"]).optional(),level:c.z.enum(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]).optional(),score:c.z.number().optional(),maxScore:c.z.number().optional(),passed:c.z.boolean().optional(),questions:c.z.any().optional(),results:c.z.any().optional(),completedAt:c.z.string().optional()});async function x(e,{params:s}){try{let e,r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await s;try{e=await p.z.assessment.findUnique({where:{id:t},include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}}})}catch(s){e=await p.z.assessment.findUnique({where:{id:t},include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}}})}if(!e)return i.NextResponse.json({error:"Assessment not found"},{status:404});return i.NextResponse.json(e)}catch(e){return console.error("Error fetching assessment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function A(e,{params:s}){try{let r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER","TEACHER"].includes(r.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let{id:t}=await s,n=await e.json(),o=m.parse(n);if(!await p.z.assessment.findUnique({where:{id:t},include:{student:{include:{user:{select:{id:!0,name:!0}}}}}}))return i.NextResponse.json({error:"Assessment not found"},{status:404});let a={...o};o.completedAt&&(a.completedAt=new Date(o.completedAt));let c=await p.z.assessment.update({where:{id:t},data:a,include:{student:{include:{user:{select:{id:!0,name:!0,email:!0}}}}}});return await l._.log({userId:r.user.id,userRole:r.user.role,action:"UPDATE",resource:"assessment",resourceId:c.id,details:{changes:o,studentName:c.student?.user.name||"Unknown"},ipAddress:l._.getIpAddress(e),userAgent:l._.getUserAgent(e)}),i.NextResponse.json(c)}catch(e){if(e instanceof c.z.ZodError)return i.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating assessment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function f(e,{params:s}){try{let r=await (0,u.getServerSession)(d.N);if(!r?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||"ADMIN"!==r.user.role)return i.NextResponse.json({error:"Forbidden"},{status:403});let{id:t}=await s,n=await p.z.assessment.findUnique({where:{id:t},include:{student:{include:{user:{select:{name:!0}}}}}});if(!n)return i.NextResponse.json({error:"Assessment not found"},{status:404});return await p.z.assessment.delete({where:{id:t}}),await l._.log({userId:r.user.id,userRole:r.user.role,action:"DELETE",resource:"assessment",resourceId:t,details:{studentName:n.student?.user.name||"Unknown",type:n.type},ipAddress:l._.getIpAddress(e),userAgent:l._.getUserAgent(e)}),i.NextResponse.json({message:"Assessment deleted successfully"})}catch(e){return console.error("Error deleting assessment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/assessments/[id]/route",pathname:"/api/assessments/[id]",filename:"route",bundlePath:"app/api/assessments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\assessments\\[id]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:g,workUnitAsyncStorage:R,serverHooks:N}=w;function h(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:R})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,580,5697,3412,1971],()=>r(88374));module.exports=t})();