(()=>{var e={};e.id=299,e.ids=[299],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},75311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>m});var a={};r.r(a),r.d(a,{GET:()=>p,POST:()=>d});var s=r(96559),n=r(48088),o=r(37719),i=r(32190),c=r(79464),l=r(45697);let u=l.Ik({name:l.Yj().min(2),phone:l.Yj().min(9),coursePreference:l.Yj().min(1),branch:l.Yj().optional().default("main")});async function d(e){try{let t=await e.json(),r=u.parse(t),a=await c.z.lead.create({data:{name:r.name,phone:r.phone,coursePreference:r.coursePreference,branch:r.branch,source:"Website",status:"NEW"}});return i.NextResponse.json(a,{status:201})}catch(e){if(e instanceof l.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),a=parseInt(t.get("limit")||"10"),s=t.get("status"),n=t.get("dateFilter"),o=t.get("startDate"),l=t.get("endDate"),u="true"===t.get("archived"),d=t.get("branch")||"main",p={branch:"main"===d?"Main Branch":"Branch"};if(s&&"ALL"!==s&&(p.status=s),u?p.archivedAt={not:null}:p.archivedAt=null,n||o&&l){let e,t=new Date,r=t;if(n)switch(n){case"today":e=new Date(t.getFullYear(),t.getMonth(),t.getDate());break;case"yesterday":let a=new Date(t);a.setDate(a.getDate()-1),e=new Date(a.getFullYear(),a.getMonth(),a.getDate()),r=new Date(a.getFullYear(),a.getMonth(),a.getDate(),23,59,59);break;case"last7days":(e=new Date(t)).setDate(e.getDate()-7);break;case"last30days":(e=new Date(t)).setDate(e.getDate()-30);break;default:e=new Date(0)}else e=new Date(o),r=new Date(l);p.createdAt={gte:e,lte:r}}let[g,h]=await Promise.all([c.z.lead.findMany({where:p,include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},assignedTeacher:{include:{user:{select:{name:!0}}}},callRecords:{orderBy:{createdAt:"desc"},select:{id:!0,startedAt:!0,endedAt:!0,duration:!0,notes:!0,recordingUrl:!0}}},orderBy:{createdAt:"desc"},skip:(r-1)*a,take:a}),c.z.lead.count({where:p})]);return i.NextResponse.json({leads:g,pagination:{page:r,limit:a,total:h,pages:Math.ceil(h/a)}})}catch(e){return console.error("Error fetching leads:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new s.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/leads/route",pathname:"/api/leads",filename:"route",bundlePath:"app/api/leads/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\leads\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:h,workUnitAsyncStorage:m,serverHooks:w}=g;function x(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:m})}},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var a=r(96330);let s=globalThis.prisma??new a.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[4243,580,5697],()=>r(75311));module.exports=a})();