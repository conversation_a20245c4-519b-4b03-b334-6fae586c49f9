(()=>{var e={};e.id=6028,e.ids=[6028],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var n=r(96330);let s=globalThis.prisma??new n.PrismaClient},81020:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>j,routeModule:()=>h,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>x});var n={};r.r(n),r.d(n,{DELETE:()=>m,GET:()=>c,PUT:()=>p});var s=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(79464),d=r(45697);let l=d.Ik({level:d.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]).optional(),branch:d.Yj().optional(),emergencyContact:d.Yj().optional(),dateOfBirth:d.Yj().optional(),address:d.Yj().optional(),photoUrl:d.Yj().optional()});async function c(e,{params:t}){try{let{id:e}=await t,r=await u.z.student.findUnique({where:{id:e},include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0}},enrollments:{include:{group:{include:{course:{select:{name:!0,level:!0,duration:!0,price:!0}},teacher:{include:{user:{select:{name:!0}}}}}}}},payments:{orderBy:{createdAt:"desc"},take:10},attendances:{include:{class:{include:{group:{select:{name:!0}}}}},orderBy:{createdAt:"desc"},take:20}}});if(!r)return i.NextResponse.json({error:"Student not found"},{status:404});return i.NextResponse.json(r)}catch(e){return console.error("Error fetching student:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:t}){try{let{id:r}=await t,n=await e.json(),s=l.parse(n);if(!await u.z.student.findUnique({where:{id:r}}))return i.NextResponse.json({error:"Student not found"},{status:404});let a=await u.z.student.update({where:{id:r},data:{...s,dateOfBirth:s.dateOfBirth?new Date(s.dateOfBirth):void 0,updatedAt:new Date},include:{user:{select:{id:!0,name:!0,phone:!0,email:!0,role:!0}},enrollments:{include:{group:{include:{course:{select:{name:!0,level:!0}}}}}}}});return i.NextResponse.json(a)}catch(e){if(e instanceof d.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating student:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:t}){try{let{id:e}=await t,r=await u.z.student.findUnique({where:{id:e},include:{enrollments:!0,payments:!0,attendances:!0}});if(!r)return i.NextResponse.json({error:"Student not found"},{status:404});let n=r.enrollments.filter(e=>"ACTIVE"===e.status);if(n.length>0)return i.NextResponse.json({error:"Cannot delete student with active enrollments",details:`Student has ${n.length} active enrollment(s)`},{status:400});return await u.z.student.delete({where:{id:e}}),i.NextResponse.json({message:"Student deleted successfully",deletedId:e})}catch(e){return console.error("Error deleting student:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let h=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/[id]/route",pathname:"/api/students/[id]",filename:"route",bundlePath:"app/api/students/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:f,workUnitAsyncStorage:x,serverHooks:w}=h;function j(){return(0,o.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:x})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,580,5697],()=>r(81020));module.exports=n})();