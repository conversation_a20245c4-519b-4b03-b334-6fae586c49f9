exports.id=2900,exports.ids=[2900],exports.modules={21321:(e,t,a)=>{"use strict";a.d(t,{d:()=>m});class n{constructor(e){this.config=e}async sendSMS(e){try{switch(this.config.provider){case"eskiz":return await this.sendEskizSMS(e);case"sms_uz":return await this.sendSMSUzSMS(e);case"playmobile":return await this.sendPlaymobileSMS(e);default:throw Error(`Unsupported SMS provider: ${this.config.provider}`)}}catch(e){return console.error("SMS sending failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async sendEskizSMS(e){let t=await fetch(`${this.config.apiUrl}/api/message/sms/send`,{method:"POST",headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({mobile_phone:this.formatPhoneNumber(e.to),message:e.message,from:e.from||this.config.from||"4546"})}),a=await t.json();return t.ok&&"success"===a.status?{success:!0,messageId:a.data.id,cost:a.data.cost}:{success:!1,error:a.message||"Failed to send SMS via Eskiz"}}async sendSMSUzSMS(e){let t=await fetch(`${this.config.apiUrl}/api/v1/send`,{method:"POST",headers:{Authorization:`Bearer ${this.config.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({phone:this.formatPhoneNumber(e.to),text:e.message,sender:e.from||this.config.from||"SMS.UZ"})}),a=await t.json();return t.ok&&a.success?{success:!0,messageId:a.data.message_id}:{success:!1,error:a.error||"Failed to send SMS via SMS.uz"}}async sendPlaymobileSMS(e){let t=await fetch(`${this.config.apiUrl}/send`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:new URLSearchParams({login:this.config.apiKey.split(":")[0],password:this.config.apiKey.split(":")[1],data:JSON.stringify([{phone:this.formatPhoneNumber(e.to),text:e.message}])})}),a=await t.json();return t.ok&&"OK"===a.result?{success:!0,messageId:a.data[0]?.id}:{success:!1,error:a.error||"Failed to send SMS via Playmobile"}}formatPhoneNumber(e){let t=e.replace(/\D/g,"");if(t.startsWith("998"))return t;if(9===t.length)return"998"+t;if(12===t.length&&t.startsWith("998"))return t;throw Error(`Invalid phone number format: ${e}`)}async getBalance(){try{switch(this.config.provider){case"eskiz":return await this.getEskizBalance();case"sms_uz":return await this.getSMSUzBalance();default:return null}}catch(e){return console.error("Failed to get SMS balance:",e),null}}async getEskizBalance(){let e=await fetch(`${this.config.apiUrl}/api/user/get-limit`,{headers:{Authorization:`Bearer ${this.config.apiKey}`}}),t=await e.json();if(e.ok&&"success"===t.status)return{balance:t.data.balance,currency:"UZS"};throw Error("Failed to get balance")}async getSMSUzBalance(){let e=await fetch(`${this.config.apiUrl}/api/v1/balance`,{headers:{Authorization:`Bearer ${this.config.apiKey}`}}),t=await e.json();if(e.ok&&t.success)return{balance:t.data.balance,currency:"UZS"};throw Error("Failed to get balance")}}let i={ENROLLMENT_CONFIRMATION:(e,t,a)=>`Assalomu alaykum ${e}! Siz ${t} kursiga muvaffaqiyatli ro'yxatdan o'tdingiz. Darslar ${a} dan boshlanadi. Innovative Centre`,PAYMENT_CONFIRMATION:(e,t,a)=>`${e}, ${t.toLocaleString()} so'm to'lov qabul qilindi. Kurs: ${a}. Rahmat! Innovative Centre`,CLASS_REMINDER:(e,t,a)=>`${e}, bugun ${a} da ${t} darsi bor. Kechikmaslik uchun iltimos! Innovative Centre`,PAYMENT_REMINDER:(e,t,a)=>`${e}, ${t.toLocaleString()} so'm to'lov muddati ${a} gacha. Iltimos, o'z vaqtida to'lang. Innovative Centre`,ATTENDANCE_ALERT:(e,t,a)=>`Hurmatli ${e}, ${t} bugun ${a} darsida qatnashmadi. Innovative Centre`,COURSE_COMPLETION:(e,t,a)=>`Tabriklaymiz ${e}! ${t} kursini muvaffaqiyatli yakunladingiz. Keyingi bosqich: ${a}. Innovative Centre`};var r=a(49526);class s{constructor(e){this.config=e,this.transporter=this.createTransporter()}createTransporter(){let e={auth:{user:this.config.user,pass:this.config.password}};switch(this.config.provider){case"gmail":e.service="gmail";break;case"outlook":e.service="hotmail";break;case"smtp":e.host=this.config.host,e.port=this.config.port||587,e.secure=this.config.secure||!1}return r.createTransport(e)}async sendEmail(e){try{let t={from:this.config.from||this.config.user,to:Array.isArray(e.to)?e.to.join(", "):e.to,subject:e.subject,html:e.html,text:e.text,attachments:e.attachments},a=await this.transporter.sendMail(t);return{success:!0,messageId:a.messageId}}catch(e){return console.error("Email sending failed:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async verifyConnection(){try{return await this.transporter.verify(),!0}catch(e){return console.error("Email connection verification failed:",e),!1}}}let o={ENROLLMENT_CONFIRMATION:(e,t,a,n)=>({subject:`Welcome to ${t} - Enrollment Confirmation`,html:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Innovative Centre</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">English Language Learning</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Enrollment Confirmation</h2>
          
          <p>Dear ${e},</p>
          
          <p>Congratulations! You have been successfully enrolled in our <strong>${t}</strong> course.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #667eea;">
            <h3 style="margin-top: 0; color: #667eea;">Course Details</h3>
            <p><strong>Course:</strong> ${t}</p>
            <p><strong>Group:</strong> ${n}</p>
            <p><strong>Start Date:</strong> ${a}</p>
          </div>
          
          <p>Please make sure to:</p>
          <ul>
            <li>Arrive 10 minutes before class starts</li>
            <li>Bring necessary materials (notebook, pen)</li>
            <li>Complete your payment if not already done</li>
          </ul>
          
          <p>If you have any questions, please don't hesitate to contact us.</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `}),PAYMENT_CONFIRMATION:(e,t,a,n,i)=>({subject:`Payment Confirmation - ${t.toLocaleString()} UZS`,html:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Payment Confirmed</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Innovative Centre</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Payment Receipt</h2>
          
          <p>Dear ${e},</p>
          
          <p>Thank you for your payment. We have successfully received your payment for the ${a} course.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #28a745;">
            <h3 style="margin-top: 0; color: #28a745;">Payment Details</h3>
            <p><strong>Amount:</strong> ${t.toLocaleString()} UZS</p>
            <p><strong>Course:</strong> ${a}</p>
            <p><strong>Payment Method:</strong> ${n}</p>
            ${i?`<p><strong>Transaction ID:</strong> ${i}</p>`:""}
            <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
          </div>
          
          <p>This email serves as your payment receipt. Please keep it for your records.</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `}),COURSE_COMPLETION:(e,t,a,n)=>({subject:`Congratulations! Course Completion Certificate - ${t}`,html:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">🎉 Congratulations!</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Course Completion</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Course Completion Certificate</h2>
          
          <p>Dear ${e},</p>
          
          <p>Congratulations on successfully completing the <strong>${t}</strong> course!</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #ffc107;">
            <h3 style="margin-top: 0; color: #ffc107;">Achievement Details</h3>
            <p><strong>Course:</strong> ${t}</p>
            <p><strong>Completion Date:</strong> ${a}</p>
            <p><strong>Student:</strong> ${e}</p>
          </div>
          
          ${n?`
            <div style="background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="margin-top: 0; color: #1976d2;">Next Step</h3>
              <p>Ready for the next challenge? Consider enrolling in <strong>${n}</strong> to continue your English learning journey!</p>
            </div>
          `:""}
          
          <p>Your certificate will be available for download from your student portal, or you can collect a printed copy from our office.</p>
          
          <p>Thank you for choosing Innovative Centre for your English language learning journey!</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `}),PAYMENT_REMINDER:(e,t,a,n)=>({subject:`Payment Reminder - ${t.toLocaleString()} UZS Due`,html:`
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
        <div style="background: linear-gradient(135deg, #dc3545 0%, #c82333 100%); color: white; padding: 30px; text-align: center;">
          <h1 style="margin: 0; font-size: 28px;">Payment Reminder</h1>
          <p style="margin: 10px 0 0 0; opacity: 0.9;">Innovative Centre</p>
        </div>
        
        <div style="padding: 30px; background: #f8f9fa;">
          <h2 style="color: #333; margin-bottom: 20px;">Payment Due Notice</h2>
          
          <p>Dear ${e},</p>
          
          <p>This is a friendly reminder that your payment for the ${n} course is due.</p>
          
          <div style="background: white; padding: 20px; border-radius: 8px; margin: 20px 0; border-left: 4px solid #dc3545;">
            <h3 style="margin-top: 0; color: #dc3545;">Payment Details</h3>
            <p><strong>Amount Due:</strong> ${t.toLocaleString()} UZS</p>
            <p><strong>Course:</strong> ${n}</p>
            <p><strong>Due Date:</strong> ${a}</p>
          </div>
          
          <p>Please make your payment as soon as possible to avoid any interruption to your classes.</p>
          
          <p>Payment methods available:</p>
          <ul>
            <li>Cash at our office</li>
            <li>UzCard/Humo</li>
            <li>PayMe/Click</li>
            <li>Bank transfer</li>
          </ul>
          
          <p>If you have already made the payment, please ignore this reminder.</p>
          
          <p>Best regards,<br>Innovative Centre Team</p>
        </div>
        
        <div style="background: #333; color: white; padding: 20px; text-align: center; font-size: 14px;">
          <p>Innovative Centre | English Language Learning</p>
          <p>Phone: +998 90 123 45 67 | Email: <EMAIL></p>
        </div>
      </div>
    `})};class l{async sendNotification(e,t){let a={success:!1},n=t.channels||this.getDefaultChannels(t.type,t.priority),i=e.preferences||{sms:!0,email:!0,push:!1};if(n.includes("sms")&&i.sms&&e.phone)try{let n=this.generateSMSMessage(t.type,t.data);a.sms=await this.smsService.sendSMS({to:e.phone,message:n})}catch(e){a.sms={success:!1,error:e instanceof Error?e.message:"SMS failed"}}if(n.includes("email")&&i.email&&e.email)try{let n=this.generateEmailMessage(t.type,t.data);a.email=await this.emailService.sendEmail({to:e.email,subject:n.subject,html:n.html})}catch(e){a.email={success:!1,error:e instanceof Error?e.message:"Email failed"}}return await this.logNotification(e.id,t,a),a.success=a.sms?.success||a.email?.success||!1,a}async sendBulkNotification(e,t){let a=await Promise.all(e.map(e=>this.sendNotification(e,t))),n=a.filter(e=>e.success).length;return console.log(`Bulk notification sent: ${n}/${e.length} successful`),a}getDefaultChannels(e,t){switch(t){case"urgent":return["sms","email","push"];case"high":return["sms","email"];case"medium":return["email","sms"];default:return["email"]}}generateSMSMessage(e,t){switch(e){case"enrollment":return i.ENROLLMENT_CONFIRMATION(t.studentName,t.courseName,t.startDate);case"payment":if(t.isConfirmation)return i.PAYMENT_CONFIRMATION(t.studentName,t.amount,t.courseName);return i.PAYMENT_REMINDER(t.studentName,t.amount,t.dueDate);case"reminder":return i.CLASS_REMINDER(t.studentName,t.courseName,t.time);case"completion":return i.COURSE_COMPLETION(t.studentName,t.courseName,t.nextLevel);case"attendance":return i.ATTENDANCE_ALERT(t.parentName,t.studentName,t.courseName);default:return t.message||"Notification from Innovative Centre"}}generateEmailMessage(e,t){switch(e){case"enrollment":return o.ENROLLMENT_CONFIRMATION(t.studentName,t.courseName,t.startDate,t.groupName);case"payment":if(t.isConfirmation)return o.PAYMENT_CONFIRMATION(t.studentName,t.amount,t.courseName,t.paymentMethod,t.transactionId);return o.PAYMENT_REMINDER(t.studentName,t.amount,t.dueDate,t.courseName);case"completion":return o.COURSE_COMPLETION(t.studentName,t.courseName,t.completionDate,t.nextLevel);default:return{subject:t.subject||"Notification from Innovative Centre",html:t.html||t.message||"You have a new notification."}}}async logNotification(e,t,a){try{console.log("Notification Log:",{recipientId:e,type:t.type,priority:t.priority,success:a.success,timestamp:new Date().toISOString(),channels:{sms:a.sms?.success||!1,email:a.email?.success||!1}})}catch(e){console.error("Failed to log notification:",e)}}async sendEnrollmentConfirmation(e,t,a){return this.sendNotification({id:"student",name:e.name,phone:e.phone,email:e.email},{type:"enrollment",priority:"high",data:{studentName:e.name,courseName:t.name,startDate:t.startDate,groupName:a.name}})}async sendPaymentConfirmation(e,t,a){return this.sendNotification({id:"student",name:e.name,phone:e.phone,email:e.email},{type:"payment",priority:"medium",data:{studentName:e.name,amount:t.amount,courseName:a.name,paymentMethod:t.method,transactionId:t.transactionId,isConfirmation:!0}})}async sendPaymentReminder(e,t,a){return this.sendNotification({id:"student",name:e.name,phone:e.phone,email:e.email},{type:"payment",priority:"high",data:{studentName:e.name,amount:t.amount,dueDate:t.dueDate,courseName:a.name,isConfirmation:!1}})}async sendClassReminder(e,t){let a=e.map((e,t)=>({id:`student-${t}`,name:e.name,phone:e.phone,email:e.email}));return this.sendBulkNotification(a,{type:"reminder",priority:"medium",channels:["sms"],data:{courseName:t.courseName,time:t.time}})}async sendAttendanceAlert(e,t,a){return this.sendNotification({id:"parent",name:e.name,phone:e.phone,email:e.email},{type:"attendance",priority:"high",channels:["sms"],data:{parentName:e.name,studentName:t.name,courseName:a.name}})}constructor(){this.smsService=function(){let e=process.env.SMS_PROVIDER||"eskiz",t=process.env.SMS_API_KEY||"";return new n({eskiz:{provider:"eskiz",apiKey:t,apiUrl:"https://notify.eskiz.uz",from:"4546"},sms_uz:{provider:"sms_uz",apiKey:t,apiUrl:"https://api.sms.uz",from:"SMS.UZ"},playmobile:{provider:"playmobile",apiKey:t,apiUrl:"https://send.playmobile.uz"}}[e])}(),this.emailService=function(){let e=process.env.EMAIL_PROVIDER||"gmail",t={provider:e,user:process.env.EMAIL_USER||"",password:process.env.EMAIL_PASSWORD||"",from:process.env.EMAIL_FROM||process.env.EMAIL_USER};return"smtp"===e&&(t.host=process.env.SMTP_HOST,t.port=parseInt(process.env.SMTP_PORT||"587"),t.secure="true"===process.env.SMTP_SECURE),new s(t)}()}}let c=null;function m(){return c||(c=new l),c}},78335:()=>{},79464:(e,t,a)=>{"use strict";a.d(t,{z:()=>i});var n=a(96330);let i=globalThis.prisma??new n.PrismaClient},96487:()=>{}};