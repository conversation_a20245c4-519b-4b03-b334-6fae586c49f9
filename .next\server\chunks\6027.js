"use strict";exports.id=6027,exports.ids=[6027],exports.modules={26134:(e,t,r)=>{r.d(t,{G$:()=>V,Hs:()=>D,UC:()=>er,VY:()=>eo,ZL:()=>ee,bL:()=>Y,bm:()=>ea,hE:()=>en,hJ:()=>et,l9:()=>Q});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),l=r(96963),i=r(65551),d=r(31355),u=r(32547),c=r(25028),p=r(46059),f=r(14163),g=r(1359),h=r(42247),m=r(63376),y=r(8730),v=r(60687),b="Dialog",[x,D]=(0,s.A)(b),[j,w]=x(b),R=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:s,modal:d=!0}=e,u=n.useRef(null),c=n.useRef(null),[p,f]=(0,i.i)({prop:o,defaultProp:a??!1,onChange:s,caller:b});return(0,v.jsx)(j,{scope:t,triggerRef:u,contentRef:c,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:d,children:r})};R.displayName=b;var C="DialogTrigger",I=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,s=w(C,r),l=(0,a.s)(t,s.triggerRef);return(0,v.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":s.open,"aria-controls":s.contentId,"data-state":z(s.open),...n,ref:l,onClick:(0,o.m)(e.onClick,s.onOpenToggle)})});I.displayName=C;var k="DialogPortal",[E,O]=x(k,{forceMount:void 0}),N=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,s=w(k,t);return(0,v.jsx)(E,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,v.jsx)(p.C,{present:r||s.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:a,children:e})}))})};N.displayName=k;var A="DialogOverlay",P=n.forwardRef((e,t)=>{let r=O(A,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(A,e.__scopeDialog);return a.modal?(0,v.jsx)(p.C,{present:n||a.open,children:(0,v.jsx)(_,{...o,ref:t})}):null});P.displayName=A;var F=(0,y.TL)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(A,r);return(0,v.jsx)(h.A,{as:F,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(f.sG.div,{"data-state":z(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),M="DialogContent",G=n.forwardRef((e,t)=>{let r=O(M,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=w(M,e.__scopeDialog);return(0,v.jsx)(p.C,{present:n||a.open,children:a.modal?(0,v.jsx)(S,{...o,ref:t}):(0,v.jsx)(T,{...o,ref:t})})});G.displayName=M;var S=n.forwardRef((e,t)=>{let r=w(M,e.__scopeDialog),s=n.useRef(null),l=(0,a.s)(t,r.contentRef,s);return n.useEffect(()=>{let e=s.current;if(e)return(0,m.Eq)(e)},[]),(0,v.jsx)(B,{...e,ref:l,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),r.triggerRef.current?.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),T=n.forwardRef((e,t)=>{let r=w(M,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,v.jsx)(B,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(o.current||r.triggerRef.current?.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let n=t.target;r.triggerRef.current?.contains(n)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),B=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:s,onCloseAutoFocus:l,...i}=e,c=w(M,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,g.Oh)(),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(u.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:s,onUnmountAutoFocus:l,children:(0,v.jsx)(d.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":z(c.open),...i,ref:f,onDismiss:()=>c.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(J,{titleId:c.titleId}),(0,v.jsx)(K,{contentRef:p,descriptionId:c.descriptionId})]})]})}),L="DialogTitle",$=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(L,r);return(0,v.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});$.displayName=L;var q="DialogDescription",W=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=w(q,r);return(0,v.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});W.displayName=q;var Z="DialogClose",H=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=w(Z,r);return(0,v.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function z(e){return e?"open":"closed"}H.displayName=Z;var U="DialogTitleWarning",[V,X]=(0,s.q)(U,{contentName:M,titleName:L,docsSlug:"dialog"}),J=({titleId:e})=>{let t=X(U),r=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return n.useEffect(()=>{e&&(document.getElementById(e)||console.error(r))},[r,e]),null},K=({contentRef:e,descriptionId:t})=>{let r=X("DialogDescriptionWarning"),o=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${r.contentName}}.`;return n.useEffect(()=>{let r=e.current?.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(o))},[o,e,t]),null},Y=R,Q=I,ee=N,et=P,er=G,en=$,eo=W,ea=H},72730:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},90270:(e,t,r)=>{r.d(t,{bL:()=>j,zi:()=>w});var n=r(43210),o=r(70569),a=r(98599),s=r(11273),l=r(65551),i=r(83721),d=r(18853),u=r(14163),c=r(60687),p="Switch",[f,g]=(0,s.A)(p),[h,m]=f(p),y=n.forwardRef((e,t)=>{let{__scopeSwitch:r,name:s,checked:i,defaultChecked:d,required:f,disabled:g,value:m="on",onCheckedChange:y,form:v,...b}=e,[j,w]=n.useState(null),R=(0,a.s)(t,e=>w(e)),C=n.useRef(!1),I=!j||v||!!j.closest("form"),[k,E]=(0,l.i)({prop:i,defaultProp:d??!1,onChange:y,caller:p});return(0,c.jsxs)(h,{scope:r,checked:k,disabled:g,children:[(0,c.jsx)(u.sG.button,{type:"button",role:"switch","aria-checked":k,"aria-required":f,"data-state":D(k),"data-disabled":g?"":void 0,disabled:g,value:m,...b,ref:R,onClick:(0,o.m)(e.onClick,e=>{E(e=>!e),I&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),I&&(0,c.jsx)(x,{control:j,bubbles:!C.current,name:s,value:m,checked:k,required:f,disabled:g,form:v,style:{transform:"translateX(-100%)"}})]})});y.displayName=p;var v="SwitchThumb",b=n.forwardRef((e,t)=>{let{__scopeSwitch:r,...n}=e,o=m(v,r);return(0,c.jsx)(u.sG.span,{"data-state":D(o.checked),"data-disabled":o.disabled?"":void 0,...n,ref:t})});b.displayName=v;var x=n.forwardRef(({__scopeSwitch:e,control:t,checked:r,bubbles:o=!0,...s},l)=>{let u=n.useRef(null),p=(0,a.s)(u,l),f=(0,i.Z)(r),g=(0,d.X)(t);return n.useEffect(()=>{let e=u.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(f!==r&&t){let n=new Event("click",{bubbles:o});t.call(e,r),e.dispatchEvent(n)}},[f,r,o]),(0,c.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:r,...s,tabIndex:-1,ref:p,style:{...s.style,...g,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function D(e){return e?"checked":"unchecked"}x.displayName="SwitchBubbleInput";var j=y,w=b},96474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(18962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};