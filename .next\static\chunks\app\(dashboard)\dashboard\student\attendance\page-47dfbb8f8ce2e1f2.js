(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5513],{311:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},332:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>N});var s=r(5155),a=r(2115),l=r(8482),n=r(8145),c=r(9852),d=r(8524),i=r(9026),o=r(3999),u=r(2915),x=r(311),m=r(4186),f=r(8533),h=r(7624),p=r(3109),g=r(7924),y=r(9074),j=r(2108);function N(){var e,t;let{data:r}=(0,j.useSession)(),[N,v]=(0,a.useState)([]),[b,w]=(0,a.useState)(!0),[A,k]=(0,a.useState)(""),[C,E]=(0,a.useState)("ALL"),[T,L]=(0,a.useState)(null),R=(0,a.useCallback)(async()=>{try{var e;w(!0);let t="/api/students/".concat(null==r||null==(e=r.user)?void 0:e.id,"/attendance?limit=100");"ALL"!==C&&(t+="&status=".concat(C));let s=await fetch(t),a=await s.json();v(a.attendances||[]),L(null)}catch(e){console.error("Error fetching attendances:",e),L("Failed to fetch attendance records")}finally{w(!1)}},[null==r||null==(e=r.user)?void 0:e.id,C]);(0,a.useEffect)(()=>{var e;(null==r||null==(e=r.user)?void 0:e.id)&&R()},[null==r||null==(t=r.user)?void 0:t.id,C,R]);let S=N.filter(e=>e.class.group.name.toLowerCase().includes(A.toLowerCase())||e.class.group.course.name.toLowerCase().includes(A.toLowerCase())||e.class.teacher.user.name.toLowerCase().includes(A.toLowerCase())||e.class.topic&&e.class.topic.toLowerCase().includes(A.toLowerCase())),F=e=>{switch(e){case"PRESENT":return(0,s.jsx)(u.A,{className:"h-4 w-4 text-green-600"});case"ABSENT":return(0,s.jsx)(x.A,{className:"h-4 w-4 text-red-600"});case"LATE":return(0,s.jsx)(m.A,{className:"h-4 w-4 text-yellow-600"});case"EXCUSED":return(0,s.jsx)(f.A,{className:"h-4 w-4 text-blue-600"});default:return null}},Z=e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"EXCUSED":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},B=N.length,D=N.filter(e=>"PRESENT"===e.status).length,W=N.filter(e=>"LATE"===e.status).length,U=N.filter(e=>"ABSENT"===e.status).length,_=N.filter(e=>"EXCUSED"===e.status).length,z=B>0?Math.round(D/B*100):0;return b?(0,s.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,s.jsx)(h.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading attendance records..."})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[T&&(0,s.jsx)(i.Fc,{variant:"destructive",children:(0,s.jsx)(i.TN,{children:T})}),(0,s.jsx)("div",{className:"flex justify-between items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Attendance"}),(0,s.jsx)("p",{className:"text-gray-600",children:"View your class attendance history"})]})}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Attendance Rate"}),(0,s.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[z,"%"]})]})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 text-green-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Present"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D})]})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(m.A,{className:"h-8 w-8 text-yellow-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Late"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:W})]})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-8 w-8 text-red-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Absent"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:U})]})]})})}),(0,s.jsx)(l.Zp,{children:(0,s.jsx)(l.Wu,{className:"p-6",children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,s.jsxs)("div",{className:"ml-4",children:[(0,s.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Excused"}),(0,s.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:_})]})]})})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Filter Attendance"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,s.jsx)("div",{className:"flex-1",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.p,{placeholder:"Search by group, course, teacher, or topic...",value:A,onChange:e=>k(e.target.value),className:"pl-10"})]})}),(0,s.jsx)("div",{className:"flex gap-2",children:["ALL","PRESENT","ABSENT","LATE","EXCUSED"].map(e=>(0,s.jsx)("button",{onClick:()=>E(e),className:"px-3 py-2 text-sm rounded-md transition-colors ".concat(C===e?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 hover:bg-gray-200"),children:"ALL"===e?"All":e.charAt(0)+e.slice(1).toLowerCase()},e))})]})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{children:["Attendance History (",S.length,")"]}),(0,s.jsx)(l.BT,{children:"Your complete attendance record"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)(d.XI,{children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nd,{children:"Date"}),(0,s.jsx)(d.nd,{children:"Group"}),(0,s.jsx)(d.nd,{children:"Course"}),(0,s.jsx)(d.nd,{children:"Teacher"}),(0,s.jsx)(d.nd,{children:"Topic"}),(0,s.jsx)(d.nd,{children:"Status"}),(0,s.jsx)(d.nd,{children:"Notes"})]})}),(0,s.jsx)(d.BF,{children:0===S.length?(0,s.jsx)(d.Hj,{children:(0,s.jsx)(d.nA,{colSpan:7,className:"text-center py-8",children:"No attendance records found"})}):S.map(e=>(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(y.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,o.Yq)(new Date(e.class.date))]})}),(0,s.jsx)(d.nA,{children:(0,s.jsx)(n.E,{variant:"outline",children:e.class.group.name})}),(0,s.jsx)(d.nA,{children:e.class.group.course.name}),(0,s.jsx)(d.nA,{children:e.class.teacher.user.name}),(0,s.jsx)(d.nA,{children:e.class.topic||"N/A"}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[F(e.status),(0,s.jsx)(n.E,{className:Z(e.status),children:e.status})]})}),(0,s.jsx)(d.nA,{children:e.notes||"N/A"})]},e.id))})]})})]})]})}},2895:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),n=(e,t)=>{let r=(0,s.forwardRef)((r,n)=>{let{color:c="currentColor",size:d=24,strokeWidth:i=2,absoluteStrokeWidth:o,className:u="",children:x,...m}=r;return(0,s.createElement)("svg",{ref:n,...a,width:d,height:d,stroke:c,strokeWidth:o?24*Number(i)/Number(d):i,className:["lucide","lucide-".concat(l(e)),u].join(" "),...m},[...t.map(e=>{let[t,r]=e;return(0,s.createElement)(t,r)}),...Array.isArray(x)?x:[x]])});return r.displayName="".concat(e),r}},2915:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3109:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3999:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>c,cn:()=>l,r6:()=>d,vv:()=>n});var s=r(2596),a=r(9688);function l(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7624:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7924:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8145:(e,t,r)=>{"use strict";r.d(t,{E:()=>c});var s=r(5155);r(2115);var a=r(2085),l=r(3999);let n=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:r,...a}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:r}),t),...a})}},8482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>i,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>c});var s=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",r),...a})});n.displayName="Card";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",r),...a})});c.displayName="CardHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});d.displayName="CardTitle";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",r),...a})});i.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},8524:(e,t,r)=>{"use strict";r.d(t,{A0:()=>c,BF:()=>d,Hj:()=>i,XI:()=>n,nA:()=>u,nd:()=>o});var s=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",r),...a})})});n.displayName="Table";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",r),...a})});c.displayName="TableHeader";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",r),...a})});d.displayName="TableBody",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});i.displayName="TableRow";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});o.displayName="TableHead";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});u.displayName="TableCell",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},8533:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},9026:(e,t,r)=>{"use strict";r.d(t,{Fc:()=>d,TN:()=>i});var s=r(5155),a=r(2115),l=r(2085),n=r(3999);let c=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=a.forwardRef((e,t)=>{let{className:r,variant:a,...l}=e;return(0,s.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(c({variant:a}),r),...l})});d.displayName="Alert",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",r),...a})}).displayName="AlertTitle";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",r),...a})});i.displayName="AlertDescription"},9074:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9546:(e,t,r)=>{Promise.resolve().then(r.bind(r,332))},9852:(e,t,r)=>{"use strict";r.d(t,{p:()=>n});var s=r(5155),a=r(2115),l=r(3999);let n=a.forwardRef((e,t)=>{let{className:r,type:a,...n}=e;return(0,s.jsx)("input",{type:a,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...n})});n.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,2108,8441,1684,7358],()=>t(9546)),_N_E=e.O()}]);