"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1071],{4416:(e,n,t)=>{t.d(n,{A:()=>r});let r=(0,t(2895).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5452:(e,n,t)=>{t.d(n,{G$:()=>K,Hs:()=>O,UC:()=>et,VY:()=>eo,ZL:()=>ee,bL:()=>z,bm:()=>el,hE:()=>er,hJ:()=>en,l9:()=>Q});var r=t(2115),o=t(5185),l=t(6101),a=t(6081),i=t(1285),s=t(5845),u=t(9178),d=t(7900),c=t(4378),f=t(8905),p=t(3655),m=t(2293),g=t(3795),v=t(8168),N=t(9708),y=t(5155),D="Dialog",[h,O]=(0,a.A)(D),[R,b]=h(D),w=e=>{let{__scopeDialog:n,children:t,open:o,defaultOpen:l,onOpenChange:a,modal:u=!0}=e,d=r.useRef(null),c=r.useRef(null),[f,p]=(0,s.i)({prop:o,defaultProp:null!=l&&l,onChange:a,caller:D});return(0,y.jsx)(R,{scope:n,triggerRef:d,contentRef:c,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:f,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:u,children:t})};w.displayName=D;var C="DialogTrigger",I=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,a=b(C,t),i=(0,l.s)(n,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":H(a.open),...r,ref:i,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});I.displayName=C;var E="DialogPortal",[j,x]=h(E,{forceMount:void 0}),A=e=>{let{__scopeDialog:n,forceMount:t,children:o,container:l}=e,a=b(E,n);return(0,y.jsx)(j,{scope:n,forceMount:t,children:r.Children.map(o,e=>(0,y.jsx)(f.C,{present:t||a.open,children:(0,y.jsx)(c.Z,{asChild:!0,container:l,children:e})}))})};A.displayName=E;var M="DialogOverlay",T=r.forwardRef((e,n)=>{let t=x(M,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=b(M,e.__scopeDialog);return l.modal?(0,y.jsx)(f.C,{present:r||l.open,children:(0,y.jsx)(F,{...o,ref:n})}):null});T.displayName=M;var _=(0,N.TL)("DialogOverlay.RemoveScroll"),F=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(M,t);return(0,y.jsx)(g.A,{as:_,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":H(o.open),...r,ref:n,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",k=r.forwardRef((e,n)=>{let t=x(P,e.__scopeDialog),{forceMount:r=t.forceMount,...o}=e,l=b(P,e.__scopeDialog);return(0,y.jsx)(f.C,{present:r||l.open,children:l.modal?(0,y.jsx)(U,{...o,ref:n}):(0,y.jsx)(L,{...o,ref:n})})});k.displayName=P;var U=r.forwardRef((e,n)=>{let t=b(P,e.__scopeDialog),a=r.useRef(null),i=(0,l.s)(n,t.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(S,{...e,ref:i,trapFocus:t.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var n;e.preventDefault(),null==(n=t.triggerRef.current)||n.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let n=e.detail.originalEvent,t=0===n.button&&!0===n.ctrlKey;(2===n.button||t)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),L=r.forwardRef((e,n)=>{let t=b(P,e.__scopeDialog),o=r.useRef(!1),l=r.useRef(!1);return(0,y.jsx)(S,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:n=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,n),n.defaultPrevented||(o.current||null==(a=t.triggerRef.current)||a.focus(),n.preventDefault()),o.current=!1,l.current=!1},onInteractOutside:n=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,n),n.defaultPrevented||(o.current=!0,"pointerdown"===n.detail.originalEvent.type&&(l.current=!0));let i=n.target;(null==(a=t.triggerRef.current)?void 0:a.contains(i))&&n.preventDefault(),"focusin"===n.detail.originalEvent.type&&l.current&&n.preventDefault()}})}),S=r.forwardRef((e,n)=>{let{__scopeDialog:t,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:i,...s}=e,c=b(P,t),f=r.useRef(null),p=(0,l.s)(n,f);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:i,children:(0,y.jsx)(u.qW,{role:"dialog",id:c.contentId,"aria-describedby":c.descriptionId,"aria-labelledby":c.titleId,"data-state":H(c.open),...s,ref:p,onDismiss:()=>c.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(Y,{titleId:c.titleId}),(0,y.jsx)($,{contentRef:f,descriptionId:c.descriptionId})]})]})}),W="DialogTitle",G=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(W,t);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:n})});G.displayName=W;var B="DialogDescription",q=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,o=b(B,t);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:n})});q.displayName=B;var V="DialogClose",Z=r.forwardRef((e,n)=>{let{__scopeDialog:t,...r}=e,l=b(V,t);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:n,onClick:(0,o.m)(e.onClick,()=>l.onOpenChange(!1))})});function H(e){return e?"open":"closed"}Z.displayName=V;var J="DialogTitleWarning",[K,X]=(0,a.q)(J,{contentName:P,titleName:W,docsSlug:"dialog"}),Y=e=>{let{titleId:n}=e,t=X(J),o="`".concat(t.contentName,"` requires a `").concat(t.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(t.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(t.docsSlug);return r.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},$=e=>{let{contentRef:n,descriptionId:t}=e,o=X("DialogDescriptionWarning"),l="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=n.current)?void 0:e.getAttribute("aria-describedby");t&&r&&(document.getElementById(t)||console.warn(l))},[l,n,t]),null},z=w,Q=I,ee=A,en=T,et=k,er=G,eo=q,el=Z},8905:(e,n,t)=>{t.d(n,{C:()=>a});var r=t(2115),o=t(6101),l=t(2712),a=e=>{let{present:n,children:t}=e,a=function(e){var n,t;let[o,a]=r.useState(),s=r.useRef(null),u=r.useRef(e),d=r.useRef("none"),[c,f]=(n=e?"mounted":"unmounted",t={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,n)=>{let r=t[e][n];return null!=r?r:e},n));return r.useEffect(()=>{let e=i(s.current);d.current="mounted"===c?e:"none"},[c]),(0,l.N)(()=>{let n=s.current,t=u.current;if(t!==e){let r=d.current,o=i(n);e?f("MOUNT"):"none"===o||(null==n?void 0:n.display)==="none"?f("UNMOUNT"):t&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),u.current=e}},[e,f]),(0,l.N)(()=>{if(o){var e;let n,t=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=i(s.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!u.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",n=t.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},l=e=>{e.target===o&&(d.current=i(s.current))};return o.addEventListener("animationstart",l),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{t.clearTimeout(n),o.removeEventListener("animationstart",l),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(c),ref:r.useCallback(e=>{s.current=e?getComputedStyle(e):null,a(e)},[])}}(n),s="function"==typeof t?t({present:a.isPresent}):r.Children.only(t),u=(0,o.s)(a.ref,function(e){var n,t;let r=null==(n=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(t=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:t.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(s));return"function"==typeof t||a.isPresent?r.cloneElement(s,{ref:u}):null};function i(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"}}]);