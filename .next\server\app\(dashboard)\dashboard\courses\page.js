(()=>{var e={};e.id=980,e.ids=[980],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26978:(e,s,r)=>{Promise.resolve().then(r.bind(r,48089))},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},42353:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,pages:()=>d,routeModule:()=>h,tree:()=>o});var t=r(65239),n=r(48088),a=r(88170),i=r.n(a),l=r(30893),c={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>l[e]);r.d(s,c);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["courses",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,72271)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\courses\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\courses\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/courses/page",pathname:"/dashboard/courses",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},48089:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>w});var t=r(60687),n=r(43210),a=r(55192),i=r(24934),l=r(59821),c=r(68988),o=r(96752),d=r(37826),x=r(3018),h=r(96241),u=r(72730),m=r(96474),p=r(99270),j=r(82080),f=r(48730),g=r(23928),v=r(41312),b=r(9923),N=r(88233),y=r(94053);function w(){let[e,s]=(0,n.useState)([]),[r,w]=(0,n.useState)(!0),[A,C]=(0,n.useState)(""),[P,k]=(0,n.useState)(!1),[E,S]=(0,n.useState)(!1),[_,T]=(0,n.useState)(null),[L,W]=(0,n.useState)(!1),[D,q]=(0,n.useState)(null),G=async()=>{try{w(!0);let e=await fetch("/api/courses"),r=await e.json();s(r.courses||[]),q(null)}catch(e){console.error("Error fetching courses:",e),q("Failed to fetch courses")}finally{w(!1)}},U=e.filter(e=>e.name.toLowerCase().includes(A.toLowerCase())||e.level.toLowerCase().includes(A.toLowerCase())||e.description?.toLowerCase().includes(A.toLowerCase())),Z=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800",z=e=>e?"bg-green-100 text-green-800":"bg-red-100 text-red-800",M=e=>e.groups.reduce((e,s)=>e+s._count.enrollments,0),B=async e=>{W(!0),q(null);try{let s=await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create course")}k(!1),G()}catch(e){q(e instanceof Error?e.message:"An error occurred")}finally{W(!1)}},F=async e=>{if(_){W(!0),q(null);try{let s=await fetch(`/api/courses/${_.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update course")}S(!1),T(null),G()}catch(e){q(e instanceof Error?e.message:"An error occurred")}finally{W(!1)}}},O=async e=>{if(confirm("Are you sure you want to delete this course? This action cannot be undone."))try{let s=await fetch(`/api/courses/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete course")}G()}catch(e){q(e instanceof Error?e.message:"An error occurred")}};return r?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading courses..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[D&&(0,t.jsx)(x.Fc,{variant:"destructive",children:(0,t.jsx)(x.TN,{children:D})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Courses Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage course catalog and pricing"})]}),(0,t.jsxs)(d.lG,{open:P,onOpenChange:k,children:[(0,t.jsx)(d.zM,{asChild:!0,children:(0,t.jsxs)(i.$,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Add Course"]})}),(0,t.jsxs)(d.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsx)(d.L3,{children:"Add New Course"}),(0,t.jsx)(d.rr,{children:"Create a new course offering with pricing and duration details."})]}),(0,t.jsx)(y.A,{onSubmit:B,onCancel:()=>k(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsx)(a.aR,{children:(0,t.jsx)(a.ZB,{children:"Search Courses"})}),(0,t.jsx)(a.Wu,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{placeholder:"Search by course name, level, or description...",value:A,onChange:e=>C(e.target.value),className:"pl-10"})]})})]}),(0,t.jsxs)(a.Zp,{children:[(0,t.jsxs)(a.aR,{children:[(0,t.jsxs)(a.ZB,{children:["Courses (",U.length,")"]}),(0,t.jsx)(a.BT,{children:"Complete list of available courses"})]}),(0,t.jsxs)(a.Wu,{children:[(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"Course"}),(0,t.jsx)(o.nd,{children:"Level"}),(0,t.jsx)(o.nd,{children:"Duration"}),(0,t.jsx)(o.nd,{children:"Price"}),(0,t.jsx)(o.nd,{children:"Groups"}),(0,t.jsx)(o.nd,{children:"Students"}),(0,t.jsx)(o.nd,{children:"Status"}),(0,t.jsx)(o.nd,{children:"Actions"})]})}),(0,t.jsx)(o.BF,{children:U.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(j.A,{className:"h-5 w-5 text-blue-600"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsx)(l.E,{className:Z(e.level),children:e.level.replace("_"," ")})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 mr-1 text-gray-500"}),e.duration," weeks"]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center text-sm font-medium",children:[(0,t.jsx)(g.A,{className:"h-3 w-3 mr-1 text-gray-500"}),(0,h.vv)(Number(e.price))]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(v.A,{className:"h-3 w-3 mr-1 text-gray-500"}),e._count.groups," groups"]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center text-sm",children:[(0,t.jsx)(v.A,{className:"h-3 w-3 mr-1 text-gray-500"}),M(e)," students"]})}),(0,t.jsx)(o.nA,{children:(0,t.jsx)(l.E,{className:z(e.isActive),children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{T(e),S(!0)},children:(0,t.jsx)(b.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>O(e.id),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===U.length&&(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No courses found matching your search."})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(a.Zp,{children:(0,t.jsx)(a.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Courses"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length})]})]})})}),(0,t.jsx)(a.Zp,{children:(0,t.jsx)(a.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(j.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Courses"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>e.isActive).length})]})]})})}),(0,t.jsx)(a.Zp,{children:(0,t.jsx)(a.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 text-yellow-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.reduce((e,s)=>e+s._count.groups,0)})]})]})})}),(0,t.jsx)(a.Zp,{children:(0,t.jsx)(a.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg. Price"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length>0?(0,h.vv)(e.reduce((e,s)=>e+Number(s.price),0)/e.length):(0,h.vv)(0)})]})]})})})]}),(0,t.jsx)(d.lG,{open:E,onOpenChange:S,children:(0,t.jsxs)(d.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(d.c7,{children:[(0,t.jsx)(d.L3,{children:"Edit Course"}),(0,t.jsx)(d.rr,{children:"Update course information, pricing, and availability."})]}),_&&(0,t.jsx)(y.A,{initialData:{name:_.name,level:_.level,description:_.description||"",duration:_.duration,price:_.price,isActive:_.isActive},onSubmit:F,onCancel:()=>{S(!1),T(null)},isEditing:!0})]})})]})}},58522:(e,s,r)=>{Promise.resolve().then(r.bind(r,72271))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72271:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\courses\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\courses\\page.tsx","default")}};var s=require("../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,8706,7825,6027,3039,9251],()=>r(42353));module.exports=t})();