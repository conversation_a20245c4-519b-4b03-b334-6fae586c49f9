(()=>{var e={};e.id=5311,e.ids=[5311],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31049:(e,t,r)=>{Promise.resolve().then(r.bind(r,84786))},33873:e=>{"use strict";e.exports=require("path")},55192:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>a});var s=r(60687),o=r(43210),n=r(96241);let i=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...t}));i.displayName="Card";let a=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...t}));a.displayName="CardHeader";let d=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));d.displayName="CardTitle";let c=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let l=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent",o.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},58776:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\test-notifications\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\test-notifications\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},84786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(60687),o=r(55192),n=r(24934),i=r(71702);function a(){let{toast:e}=(0,i.dj)();return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Test Notifications"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Test the notification system"})]}),(0,s.jsxs)(o.Zp,{children:[(0,s.jsxs)(o.aR,{children:[(0,s.jsx)(o.ZB,{children:"Toast Notifications"}),(0,s.jsx)(o.BT,{children:"Test different types of toast notifications"})]}),(0,s.jsx)(o.Wu,{children:(0,s.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,s.jsx)(n.$,{onClick:()=>{e({title:"Success!",description:"This is a success notification."})},variant:"default",children:"Success Toast"}),(0,s.jsx)(n.$,{onClick:()=>{e({variant:"destructive",title:"Error!",description:"This is an error notification."})},variant:"destructive",children:"Error Toast"}),(0,s.jsx)(n.$,{onClick:()=>{e({title:"Information",description:"This is an informational notification."})},variant:"outline",children:"Info Toast"}),(0,s.jsx)(n.$,{onClick:()=>{e({title:"Warning",description:"This is a warning notification."})},variant:"secondary",children:"Warning Toast"})]})})]})]})}},89193:(e,t,r)=>{Promise.resolve().then(r.bind(r,58776))},94795:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,pages:()=>l,routeModule:()=>f,tree:()=>c});var s=r(65239),o=r(48088),n=r(88170),i=r.n(n),a=r(30893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["test-notifications",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,58776)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\test-notifications\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,l=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\test-notifications\\page.tsx"],p={require:r,loadChunk:()=>Promise.resolve()},f=new s.AppPageRouteModule({definition:{kind:o.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/test-notifications/page",pathname:"/dashboard/test-notifications",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,7615,2918,8887,3039],()=>r(94795));module.exports=s})();