(()=>{var e={};e.id=7898,e.ids=[7898],e.modules={3018:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>o,TN:()=>d});var t=a(60687),r=a(43210),l=a(24224),n=a(96241);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=r.forwardRef(({className:e,variant:s,...a},r)=>(0,t.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(i({variant:s}),e),...a}));o.displayName="Alert",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...s}));d.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12597:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},13861:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19947:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},21825:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>eh});var t=a(60687),r=a(43210),l=a(24934),n=a(68988),i=a(59821),o=a(96752),d=a(37826),c=a(55192),m=a(31158),p=a(23026),u=a(41312),x=a(99270),f=a(9923),h=a(88233),g=a(27605),j=a(63442),N=a(9275),v=a(39390),b=a(63974),y=a(3018),w=a(58869),A=a(99891),R=a(12597),C=a(13861),E=a(23689),D=a(71702);let P=N.z.object({name:N.z.string().min(2,"Name must be at least 2 characters"),phone:N.z.string().min(9,"Phone number must be at least 9 characters"),email:N.z.string().email("Invalid email address").optional().or(N.z.literal("")),role:N.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]),password:N.z.string().min(6,"Password must be at least 6 characters"),confirmPassword:N.z.string().min(6,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don&apos;t match",path:["confirmPassword"]}),T=N.z.object({name:N.z.string().min(2,"Name must be at least 2 characters"),phone:N.z.string().min(9,"Phone number must be at least 9 characters"),email:N.z.string().email("Invalid email address").optional().or(N.z.literal("")),role:N.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]),password:N.z.string().optional(),confirmPassword:N.z.string().optional()}).refine(e=>!e.password&&!e.confirmPassword||e.password===e.confirmPassword&&(e.password?.length||0)>=6,{message:"Passwords don&apos;t match or are too short",path:["confirmPassword"]}),k={ADMIN:"Full system access including financial data and user management",MANAGER:"Management access to operations and staff oversight",TEACHER:"Access to classes, students, and assessment system",RECEPTION:"Student enrollment, leads management, and front desk operations",CASHIER:"Limited access to student lookup and payment recording only",STUDENT:"Student portal access for personal information and progress",ACADEMIC_MANAGER:"Academic management access to assign tests and view test statistics"},I={ADMIN:"text-red-600 bg-red-50 border-red-200",MANAGER:"text-blue-600 bg-blue-50 border-blue-200",TEACHER:"text-green-600 bg-green-50 border-green-200",RECEPTION:"text-purple-600 bg-purple-50 border-purple-200",CASHIER:"text-orange-600 bg-orange-50 border-orange-200",STUDENT:"text-gray-600 bg-gray-50 border-gray-200",PARENT:"text-indigo-600 bg-indigo-50 border-indigo-200"};function S({user:e,onSuccess:s,onCancel:a}){let[i,o]=(0,r.useState)(!1),[d,m]=(0,r.useState)(!1),[p,u]=(0,r.useState)(!1),[x,f]=(0,r.useState)(""),{toast:h}=(0,D.dj)(),N=!!e,{register:S,handleSubmit:M,formState:{errors:U},setValue:F,watch:O,reset:z}=(0,g.mN)({resolver:(0,j.u)(N?T:P),defaultValues:{name:e?.name||"",phone:e?.phone||"",email:e?.email||"",role:e?.role||"",password:"",confirmPassword:""}});O("role");let H=async a=>{u(!0);try{let t={name:a.name,phone:a.phone,email:a.email||null,role:a.role,...a.password&&{password:a.password}};N&&(t.id=e.id);let r=await fetch("/api/users",{method:N?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),l=await r.json();if(!r.ok)throw Error(l.error||"Failed to save user");h({title:"Success",description:`User ${N?"updated":"created"} successfully`}),s()}catch(e){h({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"An error occurred"})}finally{u(!1)}};return(0,t.jsxs)("form",{onSubmit:M(H),className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(w.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Basic Information"})]}),(0,t.jsx)(c.BT,{children:"Enter the user's personal details"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"name",children:"Full Name *"}),(0,t.jsx)(n.p,{id:"name",...S("name"),placeholder:"Enter full name",className:U.name?"border-red-500":""}),U.name&&(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"phone",children:"Phone Number *"}),(0,t.jsx)(n.p,{id:"phone",...S("phone"),placeholder:"+998901234567",className:U.phone?"border-red-500":""}),U.phone&&(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.phone.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"email",children:"Email Address"}),(0,t.jsx)(n.p,{id:"email",type:"email",...S("email"),placeholder:"<EMAIL>",className:U.email?"border-red-500":""}),U.email&&(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.email.message}),(0,t.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Optional - used for notifications"})]})]})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsxs)(c.ZB,{className:"flex items-center space-x-2",children:[(0,t.jsx)(A.A,{className:"h-5 w-5"}),(0,t.jsx)("span",{children:"Role & Security"})]}),(0,t.jsx)(c.BT,{children:"Set user role and access credentials"})]}),(0,t.jsxs)(c.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(v.J,{htmlFor:"role",children:"User Role *"}),(0,t.jsxs)(b.l6,{onValueChange:e=>F("role",e),defaultValue:e?.role,children:[(0,t.jsx)(b.bq,{className:U.role?"border-red-500":"",children:(0,t.jsx)(b.yv,{placeholder:"Select user role"})}),(0,t.jsxs)(b.gC,{children:[(0,t.jsx)(b.eb,{value:"ADMIN",children:"Admin"}),(0,t.jsx)(b.eb,{value:"MANAGER",children:"Manager"}),(0,t.jsx)(b.eb,{value:"TEACHER",children:"Teacher"}),(0,t.jsx)(b.eb,{value:"RECEPTION",children:"Reception"}),(0,t.jsx)(b.eb,{value:"CASHIER",children:"Cashier"}),(0,t.jsx)(b.eb,{value:"STUDENT",children:"Student"}),(0,t.jsx)(b.eb,{value:"PARENT",children:"Parent"})]})]}),U.role&&(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.role.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(v.J,{htmlFor:"password",children:["Password ",N?"(leave blank to keep current)":"*"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{id:"password",type:i?"text":"password",...S("password"),placeholder:N?"Enter new password":"Enter password",className:U.password?"border-red-500":""}),(0,t.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>o(!i),children:i?(0,t.jsx)(R.A,{className:"h-4 w-4"}):(0,t.jsx)(C.A,{className:"h-4 w-4"})})]}),U.password&&(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.password.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(v.J,{htmlFor:"confirmPassword",children:["Confirm Password ",N?"(if changing)":"*"]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(n.p,{id:"confirmPassword",type:d?"text":"password",...S("confirmPassword"),placeholder:"Confirm password",className:U.confirmPassword?"border-red-500":""}),(0,t.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>m(!d),children:d?(0,t.jsx)(R.A,{className:"h-4 w-4"}):(0,t.jsx)(C.A,{className:"h-4 w-4"})})]}),U.confirmPassword&&(0,t.jsx)("p",{className:"text-sm text-red-500 mt-1",children:U.confirmPassword.message})]})]})]})]}),x&&(0,t.jsxs)(y.Fc,{className:I[x],children:[(0,t.jsx)(A.A,{className:"h-4 w-4"}),(0,t.jsxs)(y.TN,{children:[(0,t.jsxs)("strong",{children:[x," Role:"]})," ",k[x]]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-4 border-t",children:[(0,t.jsx)(l.$,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,t.jsx)(l.$,{type:"submit",disabled:p,children:p?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),N?"Updating...":"Creating..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),N?"Update User":"Create User"]})})]})]})}var M=a(11273),U=a(98599),F=a(26134),O=a(70569),z=a(8730),H="AlertDialog",[_,G]=(0,M.A)(H,[F.Hs]),$=(0,F.Hs)(),L=e=>{let{__scopeAlertDialog:s,...a}=e,r=$(s);return(0,t.jsx)(F.bL,{...r,...a,modal:!0})};L.displayName=H,r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,...r}=e,l=$(a);return(0,t.jsx)(F.l9,{...l,...r,ref:s})}).displayName="AlertDialogTrigger";var q=e=>{let{__scopeAlertDialog:s,...a}=e,r=$(s);return(0,t.jsx)(F.ZL,{...r,...a})};q.displayName="AlertDialogPortal";var B=r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,...r}=e,l=$(a);return(0,t.jsx)(F.hJ,{...l,...r,ref:s})});B.displayName="AlertDialogOverlay";var Z="AlertDialogContent",[W,J]=_(Z),V=(0,z.Dc)("AlertDialogContent"),Y=r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,children:l,...n}=e,i=$(a),o=r.useRef(null),d=(0,U.s)(s,o),c=r.useRef(null);return(0,t.jsx)(F.G$,{contentName:Z,titleName:X,docsSlug:"alert-dialog",children:(0,t.jsx)(W,{scope:a,cancelRef:c,children:(0,t.jsxs)(F.UC,{role:"alertdialog",...i,...n,ref:d,onOpenAutoFocus:(0,O.m)(n.onOpenAutoFocus,e=>{e.preventDefault(),c.current?.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,t.jsx)(V,{children:l}),(0,t.jsx)(er,{contentRef:o})]})})})});Y.displayName=Z;var X="AlertDialogTitle",K=r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,...r}=e,l=$(a);return(0,t.jsx)(F.hE,{...l,...r,ref:s})});K.displayName=X;var Q="AlertDialogDescription",ee=r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,...r}=e,l=$(a);return(0,t.jsx)(F.VY,{...l,...r,ref:s})});ee.displayName=Q;var es=r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,...r}=e,l=$(a);return(0,t.jsx)(F.bm,{...l,...r,ref:s})});es.displayName="AlertDialogAction";var ea="AlertDialogCancel",et=r.forwardRef((e,s)=>{let{__scopeAlertDialog:a,...r}=e,{cancelRef:l}=J(ea,a),n=$(a),i=(0,U.s)(s,l);return(0,t.jsx)(F.bm,{...n,...r,ref:i})});et.displayName=ea;var er=({contentRef:e})=>{let s=`\`${Z}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${Z}\` by passing a \`${Q}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${Z}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return r.useEffect(()=>{document.getElementById(e.current?.getAttribute("aria-describedby"))||console.warn(s)},[s,e]),null},el=a(96241);let en=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(B,{className:(0,el.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s,ref:a}));en.displayName=B.displayName;let ei=r.forwardRef(({className:e,...s},a)=>(0,t.jsxs)(q,{children:[(0,t.jsx)(en,{}),(0,t.jsx)(Y,{ref:a,className:(0,el.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s})]}));ei.displayName=Y.displayName;let eo=({className:e,...s})=>(0,t.jsx)("div",{className:(0,el.cn)("flex flex-col space-y-2 text-center sm:text-left",e),...s});eo.displayName="AlertDialogHeader";let ed=({className:e,...s})=>(0,t.jsx)("div",{className:(0,el.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...s});ed.displayName="AlertDialogFooter";let ec=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(K,{ref:a,className:(0,el.cn)("text-lg font-semibold",e),...s}));ec.displayName=K.displayName;let em=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(ee,{ref:a,className:(0,el.cn)("text-sm text-muted-foreground",e),...s}));em.displayName=ee.displayName;let ep=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(es,{ref:a,className:(0,el.cn)((0,l.r)(),e),...s}));ep.displayName=es.displayName;let eu=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(et,{ref:a,className:(0,el.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",e),...s}));eu.displayName=et.displayName;var ex=a(19947);function ef({user:e,open:s,onOpenChange:a,onConfirm:l,isDeleting:i}){let[o,d]=(0,r.useState)("");if(!e)return null;let c=o===e.name,m="ADMIN"===e.role;return(0,t.jsx)(L,{open:s,onOpenChange:a,children:(0,t.jsxs)(ei,{className:"max-w-md",children:[(0,t.jsxs)(eo,{children:[(0,t.jsxs)(ec,{className:"flex items-center space-x-2",children:[(0,t.jsx)(ex.A,{className:"h-5 w-5 text-red-600"}),(0,t.jsx)("span",{children:"Delete User Account"})]}),(0,t.jsxs)(em,{className:"space-y-4",children:[(0,t.jsx)("div",{children:"You are about to permanently delete the user account for:"}),(0,t.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg border",children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.phone}),e.email&&(0,t.jsx)("div",{className:"text-sm text-gray-600",children:e.email}),(0,t.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,t.jsx)(A.A,{className:"h-3 w-3"}),(0,t.jsx)("span",{className:"text-sm font-medium",children:e.role})]})]}),m&&(0,t.jsxs)(y.Fc,{className:"border-red-200 bg-red-50",children:[(0,t.jsx)(ex.A,{className:"h-4 w-4 text-red-600"}),(0,t.jsxs)(y.TN,{className:"text-red-800",children:[(0,t.jsx)("strong",{children:"Warning:"})," You are deleting an ADMIN user. This will remove all administrative privileges associated with this account."]})]}),(0,t.jsxs)(y.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,t.jsx)(ex.A,{className:"h-4 w-4 text-orange-600"}),(0,t.jsxs)(y.TN,{className:"text-orange-800",children:["This action will permanently delete:",(0,t.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,t.jsx)("li",{children:"User account and login credentials"}),(0,t.jsx)("li",{children:"Associated profile data"}),(0,t.jsx)("li",{children:"Activity history and logs"}),(0,t.jsx)("li",{children:"Any related records in the system"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)(v.J,{htmlFor:"confirmName",className:"text-sm font-medium",children:["Type ",(0,t.jsx)("strong",{children:e.name})," to confirm deletion:"]}),(0,t.jsx)(n.p,{id:"confirmName",value:o,onChange:e=>d(e.target.value),placeholder:`Type "${e.name}" here`,className:"mt-1",disabled:i})]})]})]}),(0,t.jsxs)(ed,{children:[(0,t.jsx)(eu,{onClick:()=>{d(""),a(!1)},disabled:i,children:"Cancel"}),(0,t.jsx)(ep,{onClick:()=>{c&&!i&&(l(e.id),d(""))},disabled:!c||i,className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",children:i?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Deleting..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Delete User"]})})]})]})})}function eh(){let[e,s]=(0,r.useState)([]),[a,g]=(0,r.useState)([]),[j,N]=(0,r.useState)(""),[v,b]=(0,r.useState)(""),[y,w]=(0,r.useState)(!1),[A,R]=(0,r.useState)(!1),[C,E]=(0,r.useState)(!1),[P,T]=(0,r.useState)(!1),[k,I]=(0,r.useState)(null),[M,U]=(0,r.useState)(null),[F,O]=(0,r.useState)(!1),{toast:z}=(0,D.dj)(),H=async()=>{w(!0);try{let e=await fetch("/api/users");if(e.ok){let a=await e.json();s(a.users),g(a.users)}}catch(e){console.error("Error fetching users:",e)}finally{w(!1)}},_=[...new Set(e.map(e=>e.role))],G=async a=>{O(!0);try{let t=await fetch(`/api/users?id=${a}`,{method:"DELETE"});if(t.ok)s(e.filter(e=>e.id!==a)),T(!1),U(null),z({title:"Success",description:"User deleted successfully"});else{let e=await t.json();throw Error(e.error||"Failed to delete user")}}catch(e){console.error("Error deleting user:",e),z({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"Failed to delete user"})}finally{O(!1)}},$=()=>{R(!1),E(!1),I(null),H()},L=e=>{I(e),E(!0)},q=()=>{R(!1),E(!1),I(null)},B=e=>{U(e),T(!0)},Z=e=>{switch(e){case"ADMIN":return"destructive";case"MANAGER":return"default";case"TEACHER":case"STUDENT":default:return"secondary";case"RECEPTION":case"CASHIER":case"PARENT":return"outline"}};return _.map(s=>({role:s,count:e.filter(e=>e.role===s).length})),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"User Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage system users and their roles"})]}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(l.$,{onClick:()=>{let e=new Blob([[["Name","Phone","Email","Role","Profile Info","Created"],...a.map(e=>[e.name,e.phone,e.email||"",e.role,e.studentProfile?`Student - ${e.studentProfile.level} (${e.studentProfile.branch})`:e.teacherProfile?`Teacher - ${e.teacherProfile.subject} (${e.teacherProfile.branch})`:"",new Date(e.createdAt).toLocaleDateString()])].map(e=>e.map(e=>`"${e}"`).join(",")).join("\n")],{type:"text/csv"}),s=URL.createObjectURL(e),t=document.createElement("a");t.href=s,t.download=`users-${new Date().toISOString().split("T")[0]}.csv`,t.click(),URL.revokeObjectURL(s)},variant:"outline",size:"sm",children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,t.jsxs)(d.lG,{open:A,onOpenChange:R,children:[(0,t.jsx)(d.zM,{asChild:!0,children:(0,t.jsxs)(l.$,{size:"sm",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add User"]})}),(0,t.jsxs)(d.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(d.c7,{children:(0,t.jsx)(d.L3,{children:"Create New User"})}),(0,t.jsx)(S,{onSuccess:$,onCancel:q})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)(c.Zp,{children:(0,t.jsx)(c.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"Total Users"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.length})]})]})})}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"].map(s=>{let a=e.filter(e=>e.role===s).length;return(0,t.jsx)(c.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,t.jsx)(c.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"text-2xl",children:{ADMIN:"\uD83D\uDC51",MANAGER:"\uD83D\uDCCA",TEACHER:"\uD83C\uDF93",RECEPTION:"\uD83D\uDCDE",CASHIER:"\uD83D\uDCB0",STUDENT:"\uD83D\uDCDA",ACADEMIC_MANAGER:"\uD83D\uDCCB"}[s]}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:`text-sm font-medium ${{ADMIN:"text-red-600",MANAGER:"text-blue-600",TEACHER:"text-green-600",RECEPTION:"text-purple-600",CASHIER:"text-orange-600",STUDENT:"text-gray-600",ACADEMIC_MANAGER:"text-indigo-600"}[s]}`,children:s.replace("_"," ")}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a})]})]})})},s)})})]}),(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsxs)("div",{className:"relative flex-1",children:[(0,t.jsx)(x.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,t.jsx)(n.p,{placeholder:"Search users...",value:j,onChange:e=>N(e.target.value),className:"pl-10"})]}),(0,t.jsxs)("select",{value:v,onChange:e=>b(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,t.jsx)("option",{value:"",children:"All Roles"}),(0,t.jsx)("option",{value:"ADMIN",children:"Admin"}),(0,t.jsx)("option",{value:"MANAGER",children:"Manager"}),(0,t.jsx)("option",{value:"TEACHER",children:"Teacher"}),(0,t.jsx)("option",{value:"RECEPTION",children:"Reception"}),(0,t.jsx)("option",{value:"CASHIER",children:"Cashier"}),(0,t.jsx)("option",{value:"STUDENT",children:"Student"}),(0,t.jsx)("option",{value:"PARENT",children:"Parent"})]}),(v||j)&&(0,t.jsx)(l.$,{variant:"outline",onClick:()=>{b(""),N("")},children:"Clear Filters"})]}),(0,t.jsxs)(c.Zp,{children:[(0,t.jsxs)(c.aR,{children:[(0,t.jsx)(c.ZB,{children:"Users"}),(0,t.jsx)(c.BT,{children:"Manage user accounts and permissions"})]}),(0,t.jsx)(c.Wu,{children:(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"User"}),(0,t.jsx)(o.nd,{children:"Contact"}),(0,t.jsx)(o.nd,{children:"Role"}),(0,t.jsx)(o.nd,{children:"Profile"}),(0,t.jsx)(o.nd,{children:"Created"}),(0,t.jsx)(o.nd,{children:"Actions"})]})}),(0,t.jsx)(o.BF,{children:y?(0,t.jsx)(o.Hj,{children:(0,t.jsx)(o.nA,{colSpan:6,className:"text-center py-8",children:"Loading users..."})}):0===a.length?(0,t.jsx)(o.Hj,{children:(0,t.jsx)(o.nA,{colSpan:6,className:"text-center py-8",children:"No users found"})}):a.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id.slice(0,8),"..."]})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{children:e.phone}),e.email&&(0,t.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsx)(i.E,{variant:Z(e.role),children:e.role})}),(0,t.jsx)(o.nA,{children:e.studentProfile?(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Level: ",e.studentProfile.level]}),(0,t.jsxs)("div",{className:"text-gray-500",children:["Branch: ",e.studentProfile.branch]})]}):e.teacherProfile?(0,t.jsxs)("div",{className:"text-sm",children:[(0,t.jsxs)("div",{children:["Subject: ",e.teacherProfile.subject]}),(0,t.jsxs)("div",{className:"text-gray-500",children:["Branch: ",e.teacherProfile.branch]})]}):(0,t.jsx)("span",{className:"text-gray-400",children:"No profile"})}),(0,t.jsx)(o.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>L(e),children:(0,t.jsx)(f.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>B(e),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(h.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",a.length," of ",e.length," users"]}),(0,t.jsx)(d.lG,{open:C,onOpenChange:E,children:(0,t.jsxs)(d.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(d.c7,{children:(0,t.jsx)(d.L3,{children:"Edit User"})}),(0,t.jsx)(S,{user:k,onSuccess:$,onCancel:q})]})}),(0,t.jsx)(ef,{user:M,open:P,onOpenChange:T,onConfirm:G,isDeleting:F})]})}},26134:(e,s,a)=>{"use strict";a.d(s,{G$:()=>J,Hs:()=>b,UC:()=>ea,VY:()=>er,ZL:()=>ee,bL:()=>K,bm:()=>el,hE:()=>et,hJ:()=>es,l9:()=>Q});var t=a(43210),r=a(70569),l=a(98599),n=a(11273),i=a(96963),o=a(65551),d=a(31355),c=a(32547),m=a(25028),p=a(46059),u=a(14163),x=a(1359),f=a(42247),h=a(63376),g=a(8730),j=a(60687),N="Dialog",[v,b]=(0,n.A)(N),[y,w]=v(N),A=e=>{let{__scopeDialog:s,children:a,open:r,defaultOpen:l,onOpenChange:n,modal:d=!0}=e,c=t.useRef(null),m=t.useRef(null),[p,u]=(0,o.i)({prop:r,defaultProp:l??!1,onChange:n,caller:N});return(0,j.jsx)(y,{scope:s,triggerRef:c,contentRef:m,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:u,onOpenToggle:t.useCallback(()=>u(e=>!e),[u]),modal:d,children:a})};A.displayName=N;var R="DialogTrigger",C=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,n=w(R,a),i=(0,l.s)(s,n.triggerRef);return(0,j.jsx)(u.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":n.open,"aria-controls":n.contentId,"data-state":Z(n.open),...t,ref:i,onClick:(0,r.m)(e.onClick,n.onOpenToggle)})});C.displayName=R;var E="DialogPortal",[D,P]=v(E,{forceMount:void 0}),T=e=>{let{__scopeDialog:s,forceMount:a,children:r,container:l}=e,n=w(E,s);return(0,j.jsx)(D,{scope:s,forceMount:a,children:t.Children.map(r,e=>(0,j.jsx)(p.C,{present:a||n.open,children:(0,j.jsx)(m.Z,{asChild:!0,container:l,children:e})}))})};T.displayName=E;var k="DialogOverlay",I=t.forwardRef((e,s)=>{let a=P(k,e.__scopeDialog),{forceMount:t=a.forceMount,...r}=e,l=w(k,e.__scopeDialog);return l.modal?(0,j.jsx)(p.C,{present:t||l.open,children:(0,j.jsx)(M,{...r,ref:s})}):null});I.displayName=k;var S=(0,g.TL)("DialogOverlay.RemoveScroll"),M=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,r=w(k,a);return(0,j.jsx)(f.A,{as:S,allowPinchZoom:!0,shards:[r.contentRef],children:(0,j.jsx)(u.sG.div,{"data-state":Z(r.open),...t,ref:s,style:{pointerEvents:"auto",...t.style}})})}),U="DialogContent",F=t.forwardRef((e,s)=>{let a=P(U,e.__scopeDialog),{forceMount:t=a.forceMount,...r}=e,l=w(U,e.__scopeDialog);return(0,j.jsx)(p.C,{present:t||l.open,children:l.modal?(0,j.jsx)(O,{...r,ref:s}):(0,j.jsx)(z,{...r,ref:s})})});F.displayName=U;var O=t.forwardRef((e,s)=>{let a=w(U,e.__scopeDialog),n=t.useRef(null),i=(0,l.s)(s,a.contentRef,n);return t.useEffect(()=>{let e=n.current;if(e)return(0,h.Eq)(e)},[]),(0,j.jsx)(H,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,a=0===s.button&&!0===s.ctrlKey;(2===s.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),z=t.forwardRef((e,s)=>{let a=w(U,e.__scopeDialog),r=t.useRef(!1),l=t.useRef(!1);return(0,j.jsx)(H,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||a.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,l.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,"pointerdown"===s.detail.originalEvent.type&&(l.current=!0));let t=s.target;a.triggerRef.current?.contains(t)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&l.current&&s.preventDefault()}})}),H=t.forwardRef((e,s)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:n,onCloseAutoFocus:i,...o}=e,m=w(U,a),p=t.useRef(null),u=(0,l.s)(s,p);return(0,x.Oh)(),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:n,onUnmountAutoFocus:i,children:(0,j.jsx)(d.qW,{role:"dialog",id:m.contentId,"aria-describedby":m.descriptionId,"aria-labelledby":m.titleId,"data-state":Z(m.open),...o,ref:u,onDismiss:()=>m.onOpenChange(!1)})}),(0,j.jsxs)(j.Fragment,{children:[(0,j.jsx)(Y,{titleId:m.titleId}),(0,j.jsx)(X,{contentRef:p,descriptionId:m.descriptionId})]})]})}),_="DialogTitle",G=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,r=w(_,a);return(0,j.jsx)(u.sG.h2,{id:r.titleId,...t,ref:s})});G.displayName=_;var $="DialogDescription",L=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,r=w($,a);return(0,j.jsx)(u.sG.p,{id:r.descriptionId,...t,ref:s})});L.displayName=$;var q="DialogClose",B=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,l=w(q,a);return(0,j.jsx)(u.sG.button,{type:"button",...t,ref:s,onClick:(0,r.m)(e.onClick,()=>l.onOpenChange(!1))})});function Z(e){return e?"open":"closed"}B.displayName=q;var W="DialogTitleWarning",[J,V]=(0,n.q)(W,{contentName:U,titleName:_,docsSlug:"dialog"}),Y=({titleId:e})=>{let s=V(W),a=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return t.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},X=({contentRef:e,descriptionId:s})=>{let a=V("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return t.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");s&&a&&(document.getElementById(s)||console.warn(r))},[r,e,s]),null},K=A,Q=C,ee=T,es=I,ea=F,et=G,er=L,el=B},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},37826:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>p,L3:()=>x,c7:()=>u,lG:()=>o,rr:()=>f,zM:()=>d});var t=a(60687),r=a(43210),l=a(26134),n=a(11860),i=a(96241);let o=l.bL,d=l.l9,c=l.ZL;l.bm;let m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));m.displayName=l.hJ.displayName;let p=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(c,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(l.UC,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",e),...a,children:(0,t.jsxs)("div",{className:"relative",children:[s,(0,t.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]}));p.displayName=l.UC.displayName;let u=({className:e,...s})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});u.displayName="DialogHeader";let x=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));x.displayName=l.hE.displayName;let f=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));f.displayName=l.VY.displayName},39390:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var t=a(60687),r=a(43210),l=a(78148),n=a(24224),i=a(96241);let o=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.b,{ref:a,className:(0,i.cn)(o(),e),...s}));d.displayName=l.b.displayName},55192:(e,s,a)=>{"use strict";a.d(s,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var t=a(60687),r=a(43210),l=a(96241);let n=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));n.displayName="Card";let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h3",{ref:a,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));o.displayName="CardTitle";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("p",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));d.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,l.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,s,a)=>{"use strict";a.d(s,{bq:()=>p,eb:()=>h,gC:()=>f,l6:()=>c,yv:()=>m});var t=a(60687),r=a(43210),l=a(22670),n=a(78272),i=a(3589),o=a(13964),d=a(96241);let c=l.bL;l.YJ;let m=l.WT,p=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.l9,{ref:r,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,(0,t.jsx)(l.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));p.displayName=l.l9.displayName;let u=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));u.displayName=l.PP.displayName;let x=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));x.displayName=l.wn.displayName;let f=r.forwardRef(({className:e,children:s,position:a="popper",...r},n)=>(0,t.jsx)(l.ZL,{children:(0,t.jsxs)(l.UC,{ref:n,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,t.jsx)(u,{}),(0,t.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(x,{})]})}));f.displayName=l.UC.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=l.JU.displayName;let h=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(l.q7,{ref:r,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(l.VF,{children:(0,t.jsx)(o.A,{className:"h-4 w-4"})})}),(0,t.jsx)(l.p4,{children:s})]}));h.displayName=l.q7.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(l.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=l.wv.displayName},68988:(e,s,a)=>{"use strict";a.d(s,{p:()=>n});var t=a(60687),r=a(43210),l=a(96241);let n=r.forwardRef(({className:e,type:s,...a},r)=>(0,t.jsx)("input",{type:s,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));n.displayName="Input"},81969:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\users\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\users\\page.tsx","default")},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},88752:(e,s,a)=>{Promise.resolve().then(a.bind(a,81969))},96585:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>p,tree:()=>d});var t=a(65239),r=a(48088),l=a(88170),n=a.n(l),i=a(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);a.d(s,o);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["users",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,81969)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\users\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\users\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/users/page",pathname:"/dashboard/users",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96752:(e,s,a)=>{"use strict";a.d(s,{A0:()=>i,BF:()=>o,Hj:()=>d,XI:()=>n,nA:()=>m,nd:()=>c});var t=a(60687),r=a(43210),l=a(96241);let n=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",e),...s}));i.displayName="TableHeader";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));o.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));d.displayName="TableRow";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},98480:(e,s,a)=>{Promise.resolve().then(a.bind(a,21825))}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,8706,7825,3039],()=>a(96585));module.exports=t})();