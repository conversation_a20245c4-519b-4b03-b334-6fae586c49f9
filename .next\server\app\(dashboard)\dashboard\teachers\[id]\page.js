(()=>{var e={};e.id=861,e.ids=[861],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15150:(e,s,r)=>{Promise.resolve().then(r.bind(r,78644))},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},28559:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},32930:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>t});let t=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\teachers\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\[id]\\page.tsx","default")},33873:e=>{"use strict";e.exports=require("path")},40007:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>c});var t=r(65239),a=r(48088),n=r(88170),l=r.n(n),i=r(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);r.d(s,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["teachers",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,32930)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,x=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\[id]\\page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},m=new t.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/teachers/[id]/page",pathname:"/dashboard/teachers/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},41550:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},48730:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55192:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>x,ZB:()=>d,Zp:()=>l,aR:()=>i});var t=r(60687),a=r(43210),n=r(96241);let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));l.displayName="Card";let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let x=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));x.displayName="CardContent",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78644:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var t=r(60687),a=r(43210),n=r(16189),l=r(55192),i=r(24934),d=r(59821),c=r(96752),x=r(96241),o=r(28559),m=r(9923),h=r(58869),p=r(48340),j=r(41550),u=r(97992),f=r(40228),N=r(86561),b=r(82080),v=r(27351),g=r(41312),y=r(48730),w=r(85814),A=r.n(w);function k(){var e,s;(0,n.useParams)();let[r,w]=(0,a.useState)(null),[k,C]=(0,a.useState)(!0);if(k)return(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:"Loading..."});if(!r)return(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:"Teacher not found"});let P=r.groups.filter(e=>e.isActive).length,R=r.groups.reduce((e,s)=>e+s._count.enrollments,0),T=r._count.classes,_=P>0?R/P:0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(A(),{href:"/dashboard/teachers",children:(0,t.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(o.A,{className:"h-4 w-4 mr-2"}),"Back to Teachers"]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:r.user.name}),(0,t.jsx)("p",{className:"text-gray-600",children:"Teacher Profile"})]})]}),(0,t.jsxs)(i.$,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 mr-2"}),"Personal Information"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{children:r.user.phone})]}),r.user.email&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{children:r.user.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(u.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{children:r.branch})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{children:["Joined ",(0,x.Yq)(r.user.createdAt)]})]}),(0,t.jsx)("div",{className:"pt-2 border-t",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["Role: ",r.user.role]})]})})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"h-5 w-5 mr-2"}),"Professional Details"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Subject"}),(0,t.jsx)("span",{className:"font-semibold",children:r.subject})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Experience"}),(0,t.jsxs)(d.E,{className:(e=r.experience)?e<2?"bg-yellow-100 text-yellow-800":e<5?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",children:[r.experience?`${r.experience}y`:"0y"," - ",(s=r.experience)?s<2?"Junior":s<5?"Mid-level":"Senior":"New"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Branch"}),(0,t.jsx)("span",{className:"font-semibold",children:r.branch})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 mr-2"}),"Teaching Overview"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Active Groups"}),(0,t.jsx)("span",{className:"font-semibold",children:P})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Total Students"}),(0,t.jsx)("span",{className:"font-semibold",children:R})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Total Classes"}),(0,t.jsx)("span",{className:"font-semibold",children:T})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Avg. Class Size"}),(0,t.jsx)("span",{className:"font-semibold",children:_.toFixed(1)})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Students"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:R})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Classes Taught"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:T})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-8 w-8 text-yellow-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Experience"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[r.experience||0,"y"]})]})]})})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Assigned Groups (",r.groups.length,")"]}),(0,t.jsx)(l.BT,{children:"Groups currently assigned to this teacher"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nd,{children:"Group"}),(0,t.jsx)(c.nd,{children:"Course"}),(0,t.jsx)(c.nd,{children:"Level"}),(0,t.jsx)(c.nd,{children:"Students"}),(0,t.jsx)(c.nd,{children:"Duration"}),(0,t.jsx)(c.nd,{children:"Status"}),(0,t.jsx)(c.nd,{children:"Actions"})]})}),(0,t.jsx)(c.BF,{children:r.groups.map(e=>(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nA,{children:(0,t.jsx)("div",{className:"font-medium",children:e.name})}),(0,t.jsx)(c.nA,{children:e.course.name}),(0,t.jsx)(c.nA,{children:(0,t.jsx)(d.E,{variant:"outline",children:e.course.level})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-1 text-blue-600"}),(0,t.jsxs)("span",{children:[e._count.enrollments,"/",e.capacity]})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,x.Yq)(e.startDate)," - ",(0,x.Yq)(e.endDate)]})}),(0,t.jsx)(c.nA,{children:(0,t.jsx)(d.E,{className:e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"View"}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"Manage"})]})})]},e.id))})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Recent Classes"}),(0,t.jsx)(l.BT,{children:"Latest class sessions taught by this teacher"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nd,{children:"Date"}),(0,t.jsx)(c.nd,{children:"Group"}),(0,t.jsx)(c.nd,{children:"Topic"}),(0,t.jsx)(c.nd,{children:"Attendance"}),(0,t.jsx)(c.nd,{children:"Homework"}),(0,t.jsx)(c.nd,{children:"Actions"})]})}),(0,t.jsx)(c.BF,{children:r.classes.slice(0,10).map(e=>(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nA,{children:(0,x.Yq)(e.date)}),(0,t.jsx)(c.nA,{children:e.group.name}),(0,t.jsx)(c.nA,{children:e.topic||"No topic set"}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("span",{className:"text-sm",children:[e._count.attendances," students"]})}),(0,t.jsx)(c.nA,{children:(0,t.jsx)("span",{className:"text-sm text-gray-600",children:e.homework?"Assigned":"None"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"View"}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"Edit"})]})})]},e.id))})]})})]})]})}},96752:(e,s,r)=>{"use strict";r.d(s,{A0:()=>i,BF:()=>d,Hj:()=>c,XI:()=>l,nA:()=>o,nd:()=>x});var t=r(60687),a=r(43210),n=r(96241);let l=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:r,className:(0,n.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let i=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("thead",{ref:r,className:(0,n.cn)("[&_tr]:border-b",e),...s}));i.displayName="TableHeader";let d=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("tbody",{ref:r,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("tfoot",{ref:r,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("tr",{ref:r,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let x=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("th",{ref:r,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableHead";let o=a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("td",{ref:r,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableCell",a.forwardRef(({className:e,...s},r)=>(0,t.jsx)("caption",{ref:r,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"},96998:(e,s,r)=>{Promise.resolve().then(r.bind(r,32930))},97992:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(18962).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,3039],()=>r(40007));module.exports=t})();