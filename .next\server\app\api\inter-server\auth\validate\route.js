(()=>{var e={};e.id=4874,e.ids=[4874],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function a(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,a={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=i.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},n=await fetch(s,{method:r.method,headers:a,body:r.data?JSON.stringify(r.data):void 0}),o=await n.json();return{success:n.ok,data:o,status:n.status,error:n.ok?void 0:o.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>i,cU:()=>n,g2:()=>s});class n{static async authenticateUser(e,r){return a("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return a("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return a("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class i{static logRequest(e,r,t,s){let a=new Date().toISOString(),n=process.env.SERVER_TYPE||"unknown";console.log(`[${a}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:n,success:t,details:s})}static async healthCheck(e){try{return(await a(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},9477:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>v,serverHooks:()=>E,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{POST:()=>l});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(7786),d=t(97110),c=t.n(d),p=t(79464);async function l(e){try{if(!(0,u.g2)(e))return u.LQ.logRequest("incoming","/api/inter-server/auth/validate",!1,"Unauthorized"),o.NextResponse.json({error:"Unauthorized inter-server request"},{status:401});let{phone:r,password:t}=await e.json();if(!r||!t)return o.NextResponse.json({error:"Phone and password are required"},{status:400});let s=await p.z.user.findUnique({where:{phone:r},select:{id:!0,name:!0,phone:!0,email:!0,role:!0,password:!0,createdAt:!0,updatedAt:!0}});if(!s)return u.LQ.logRequest("incoming","/api/inter-server/auth/validate",!1,"User not found"),o.NextResponse.json({error:"Invalid credentials"},{status:401});if(!await c().compare(t,s.password))return u.LQ.logRequest("incoming","/api/inter-server/auth/validate",!1,"Invalid password"),o.NextResponse.json({error:"Invalid credentials"},{status:401});let a=e.headers.get("User-Agent")?.includes("staff")?"staff":"admin";if("staff"===a&&!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER","STUDENT"].includes(s.role))return u.LQ.logRequest("incoming","/api/inter-server/auth/validate",!1,"Role not allowed on staff server"),o.NextResponse.json({error:"Access denied for this server"},{status:403});let{password:n,...i}=s;return u.LQ.logRequest("incoming","/api/inter-server/auth/validate",!0,`User ${s.id} validated`),o.NextResponse.json({success:!0,user:i})}catch(e){return console.error("Inter-server auth validation error:",e),u.LQ.logRequest("incoming","/api/inter-server/auth/validate",!1,e),o.NextResponse.json({error:"Authentication validation failed",details:e instanceof Error?e.message:"Unknown error"},{status:500})}}let v=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/inter-server/auth/validate/route",pathname:"/api/inter-server/auth/validate",filename:"route",bundlePath:"app/api/inter-server/auth/validate/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\inter-server\\auth\\validate\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:R,workUnitAsyncStorage:h,serverHooks:E}=v;function f(){return(0,i.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:h})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},97110:e=>{"use strict";e.exports=require("bcryptjs")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580],()=>t(9477));module.exports=s})();