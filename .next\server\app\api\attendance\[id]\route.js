"use strict";(()=>{var e={};e.id=1179,e.ids=[1179],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},59450:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>f,serverHooks:()=>h,workAsyncStorage:()=>N,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{DELETE:()=>w,GET:()=>g,PUT:()=>m});var n=t(96559),a=t(48088),o=t(37719),u=t(32190),i=t(19854),d=t(41098),c=t(79464),l=t(99326),p=t(45697);let x=p.Ik({status:p.k5(["PRESENT","ABSENT","LATE","EXCUSED"]).optional(),notes:p.Yj().optional()});async function g(e,{params:r}){try{let e=await (0,i.getServerSession)(d.N);if(!e?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r,s=await c.z.attendance.findUnique({where:{id:t},include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},class:{include:{group:{include:{course:{select:{name:!0,level:!0}}}},teacher:{include:{user:{select:{name:!0}}}}}}}});if(!s)return u.NextResponse.json({error:"Attendance record not found"},{status:404});return u.NextResponse.json(s)}catch(e){return console.error("Error fetching attendance:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let t=await (0,i.getServerSession)(d.N);if(!t?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER","TEACHER"].includes(t.user.role))return u.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await e.json(),a=x.parse(n),o=await c.z.attendance.findUnique({where:{id:s},include:{student:{include:{user:{select:{id:!0,name:!0}}}},class:{include:{group:{select:{name:!0}}}}}});if(!o)return u.NextResponse.json({error:"Attendance record not found"},{status:404});let p=await c.z.attendance.update({where:{id:s},data:{...a,updatedAt:new Date},include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},class:{include:{group:{include:{course:{select:{name:!0,level:!0}}}},teacher:{include:{user:{select:{name:!0}}}}}}}});return await l._.log({userId:t.user.id,userRole:t.user.role,action:"UPDATE",resource:"attendance",resourceId:p.id,details:{changes:a,studentName:p.student?.user.name||"Unknown",className:p.class?.group?.name||"Unknown",previousStatus:o.status,newStatus:p.status},ipAddress:l._.getIpAddress(e),userAgent:l._.getUserAgent(e)}),u.NextResponse.json(p)}catch(e){if(e instanceof p.G)return u.NextResponse.json({error:"Validation error",details:e.errors},{status:400});return console.error("Error updating attendance:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function w(e,{params:r}){try{let t=await (0,i.getServerSession)(d.N);if(!t?.user)return u.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER"].includes(t.user.role))return u.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await c.z.attendance.findUnique({where:{id:s},include:{student:{include:{user:{select:{name:!0}}}},class:{include:{group:{select:{name:!0}}}}}});if(!n)return u.NextResponse.json({error:"Attendance record not found"},{status:404});return await c.z.attendance.delete({where:{id:s}}),await l._.log({userId:t.user.id,userRole:t.user.role,action:"DELETE",resource:"attendance",resourceId:s,details:{studentName:n.student?.user.name||"Unknown",className:n.class?.group?.name||"Unknown",status:n.status},ipAddress:l._.getIpAddress(e),userAgent:l._.getUserAgent(e)}),u.NextResponse.json({message:"Attendance record deleted successfully"})}catch(e){return console.error("Error deleting attendance:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let f=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/attendance/[id]/route",pathname:"/api/attendance/[id]",filename:"route",bundlePath:"app/api/attendance/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\attendance\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:N,workUnitAsyncStorage:A,serverHooks:h}=f;function R(){return(0,o.patchFetch)({workAsyncStorage:N,workUnitAsyncStorage:A})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(59450));module.exports=s})();