(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[317],{1586:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2895:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(2115),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{color:i="currentColor",size:c=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:x="",children:m,...u}=s;return(0,r.createElement)("svg",{ref:l,...n,width:c,height:c,stroke:i,strokeWidth:o?24*Number(d)/Number(c):d,className:["lucide","lucide-".concat(a(e)),x].join(" "),...u},[...t.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},2915:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3109:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("TrendingUp",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]])},3999:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>a,r6:()=>c,vv:()=>l});var r=s(2596),n=s(9688);function a(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,r.$)(t))}function l(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},5040:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},5735:(e,t,s)=>{Promise.resolve().then(s.bind(s,9214))},6101:(e,t,s)=>{"use strict";s.d(t,{s:()=>l,t:()=>a});var r=s(2115);function n(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function a(...e){return t=>{let s=!1,r=e.map(e=>{let r=n(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():n(e[t],null)}}}}function l(...e){return r.useCallback(a(...e),e)}},6785:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>c});var r=s(5155),n=s(2115),a=s(9708),l=s(2085),i=s(3999);let c=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,t)=>{let{className:s,variant:n,size:l,asChild:d=!1,...o}=e,x=d?a.DX:"button";return(0,r.jsx)(x,{className:(0,i.cn)(c({variant:n,size:l,className:s})),ref:t,...o})});d.displayName="Button"},7624:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8145:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(5155);s(2115);var n=s(2085),a=s(3999);let l=(0,n.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,...n}=e;return(0,r.jsx)("div",{className:(0,a.cn)(l({variant:s}),t),...n})}},8482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>i});var r=s(5155),n=s(2115),a=s(3999);let l=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",s),...n})});l.displayName="Card";let i=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",s),...n})});i.displayName="CardHeader";let c=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",s),...n})});c.displayName="CardTitle";let d=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",s),...n})});d.displayName="CardDescription";let o=n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",s),...n})});o.displayName="CardContent",n.forwardRef((e,t)=>{let{className:s,...n}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",s),...n})}).displayName="CardFooter"},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9214:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>f});var r=s(5155),n=s(2115),a=s(8482),l=s(8145),i=s(7168),c=s(7624),d=s(6785),o=s(2915),x=s(9074),m=s(5040),u=s(3109),h=s(1586);function f(){let[e,t]=(0,n.useState)(null),[s,f]=(0,n.useState)(!0),[p,y]=(0,n.useState)(null);(0,n.useEffect)(()=>{g()},[]);let g=async()=>{try{f(!0);let e=await fetch("/api/students/current/dashboard");if(!e.ok)throw Error("Failed to fetch dashboard data");let s=await e.json();t(s),y(null)}catch(e){console.error("Error fetching dashboard data:",e),y("Failed to load dashboard data")}finally{f(!1)}};return s?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsx)(c.A,{className:"h-8 w-8 animate-spin text-blue-600"})}):p?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-600 mb-4",children:p}),(0,r.jsx)(i.$,{onClick:g,children:"Try Again"})]})}):e?(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Student Dashboard"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",e.student.name,"!"]}),e.currentEnrollment&&(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["Currently enrolled in ",e.currentEnrollment.courseName," - ",e.currentEnrollment.groupName]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Current Level"}),(0,r.jsx)(d.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(l.E,{className:"bg-yellow-100 text-yellow-800",children:["Level ",e.student.level]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["→ ",e.student.nextLevel]})]}),(0,r.jsxs)("div",{className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-gray-600",children:["Progress to ",e.student.nextLevel]}),(0,r.jsxs)("span",{className:"font-medium",children:[e.progress.overall,"%"]})]}),(0,r.jsx)("div",{className:"mt-1 w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:"".concat(e.progress.overall,"%")}})})]})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Attendance"}),(0,r.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[e.progress.attendance,"%"]}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.stats.attendedClasses," of ",e.stats.totalClasses," classes"]})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Upcoming Classes"}),(0,r.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.stats.upcomingClasses}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"This week"})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(a.ZB,{className:"text-sm font-medium",children:"Assignments"}),(0,r.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(a.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:e.stats.pendingAssignments}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending completion"})]})]})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Quick Actions"}),(0,r.jsx)(a.BT,{children:"Access your most used features"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,r.jsxs)(i.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,r.jsx)(x.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"text-sm",children:"View Schedule"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,r.jsx)(m.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"text-sm",children:"Assignments"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,r.jsx)(u.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"text-sm",children:"Progress Report"})]}),(0,r.jsxs)(i.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,r.jsx)(h.A,{className:"h-6 w-6"}),(0,r.jsx)("span",{className:"text-sm",children:"Payment History"})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Recent Classes"}),(0,r.jsx)(a.BT,{children:"Your latest class attendance"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)("div",{className:"space-y-4",children:e.recentClasses.length>0?e.recentClasses.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:e.topic}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.date})]}),(0,r.jsx)(l.E,{className:"present"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:e.status})]},t)):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No recent classes"})]})})})]}),(0,r.jsxs)(a.Zp,{children:[(0,r.jsxs)(a.aR,{children:[(0,r.jsx)(a.ZB,{children:"Payment Status"}),(0,r.jsx)(a.BT,{children:"Your payment information"})]}),(0,r.jsx)(a.Wu,{children:(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Total Amount"}),(0,r.jsxs)("span",{className:"font-medium",children:[e.payments.totalPayments.toLocaleString()," UZS"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Paid Amount"}),(0,r.jsxs)("span",{className:"font-medium text-green-600",children:[e.payments.paidAmount.toLocaleString()," UZS"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Pending Amount"}),(0,r.jsxs)("span",{className:"font-medium text-orange-600",children:[e.payments.pendingAmount.toLocaleString()," UZS"]})]}),(0,r.jsxs)("div",{className:"pt-2 border-t",children:[(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:"".concat(e.payments.totalPayments>0?e.payments.paidAmount/e.payments.totalPayments*100:0,"%")}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.payments.totalPayments>0?Math.round(e.payments.paidAmount/e.payments.totalPayments*100):0,"% paid"]})]})]})})]})]})]}):(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsx)("p",{className:"text-gray-600",children:"No dashboard data available"})})}},9708:(e,t,s)=>{"use strict";s.d(t,{DX:()=>i,Dc:()=>d,TL:()=>l});var r=s(2115),n=s(6101),a=s(5155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...a}=e;if(r.isValidElement(s)){var l;let e,i,c=(l=s,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,t){let s={...t};for(let r in t){let n=e[r],a=t[r];/^on[A-Z]/.test(r)?n&&a?s[r]=(...e)=>{let t=a(...e);return n(...e),t}:n&&(s[r]=n):"style"===r?s[r]={...n,...a}:"className"===r&&(s[r]=[n,a].filter(Boolean).join(" "))}return{...e,...s}}(a,s.props);return s.type!==r.Fragment&&(d.ref=t?(0,n.t)(t,c):c),r.cloneElement(s,d)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:n,...l}=e,i=r.Children.toArray(n),c=i.find(o);if(c){let e=c.props.children,n=i.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,a.jsx)(t,{...l,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,n):null})}return(0,a.jsx)(t,{...l,ref:s,children:n})});return s.displayName=`${e}.Slot`,s}var i=l("Slot"),c=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=c,t}function o(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,8441,1684,7358],()=>t(5735)),_N_E=e.O()}]);