"use strict";(()=>{var e={};e.id=8616,e.ids=[8616],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},47010:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>R,routeModule:()=>k,serverHooks:()=>b,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>v});var n={};r.r(n),r.d(n,{GET:()=>y,POST:()=>g,PUT:()=>h});var o=r(96559),s=r(48088),a=r(37719),i=r(32190),d=r(45697),c=r(79464),l=r(21321);class u{constructor(){this.workflows=[],this.notificationService=(0,l.d)(),this.initializeDefaultWorkflows()}initializeDefaultWorkflows(){this.workflows=[{id:"enrollment-confirmation",name:"Enrollment Confirmation",description:"Send confirmation when student enrolls in a course",trigger:{event:"enrollment.created"},actions:[{type:"notification",data:{type:"enrollment",priority:"high"}}],enabled:!0},{id:"payment-confirmation",name:"Payment Confirmation",description:"Send confirmation when payment is received",trigger:{event:"payment.created",conditions:{status:"PAID"}},actions:[{type:"notification",data:{type:"payment",priority:"medium"}}],enabled:!0},{id:"payment-reminder",name:"Payment Reminder",description:"Send reminder for overdue payments",trigger:{event:"payment.overdue"},actions:[{type:"notification",data:{type:"payment_reminder",priority:"high"}},{type:"update_status",data:{status:"DEBT"}}],enabled:!0},{id:"class-reminder",name:"Class Reminder",description:"Send reminder 2 hours before class",trigger:{event:"class.upcoming",delay:120},actions:[{type:"notification",data:{type:"class_reminder",priority:"medium",channels:["sms"]}}],enabled:!0},{id:"attendance-alert",name:"Attendance Alert",description:"Alert parents when student is absent",trigger:{event:"attendance.marked",conditions:{status:"ABSENT"}},actions:[{type:"notification",data:{type:"attendance_alert",priority:"high",channels:["sms"]}}],enabled:!0},{id:"course-completion",name:"Course Completion",description:"Congratulate student on course completion",trigger:{event:"enrollment.completed"},actions:[{type:"notification",data:{type:"completion",priority:"medium"}},{type:"create_record",data:{type:"certificate"}}],enabled:!0},{id:"lead-followup",name:"Lead Follow-up",description:"Follow up with leads after 24 hours",trigger:{event:"lead.created",delay:1440},actions:[{type:"send_reminder",data:{type:"lead_followup",assignee:"reception"}}],enabled:!0},{id:"monthly-payment-reminder",name:"Monthly Payment Reminder",description:"Remind students about upcoming monthly payments",trigger:{event:"payment.due_soon",delay:10080},actions:[{type:"notification",data:{type:"payment_reminder",priority:"medium"}}],enabled:!0}]}async triggerWorkflow(e,t){for(let r of this.workflows.filter(r=>r.enabled&&r.trigger.event===e&&this.checkConditions(r.trigger.conditions,t)))r.trigger.delay?setTimeout(()=>{this.executeWorkflow(r,t)},60*r.trigger.delay*1e3):await this.executeWorkflow(r,t)}checkConditions(e,t){return!e||Object.entries(e).every(([e,r])=>t[e]===r)}async executeWorkflow(e,t){for(let r of(console.log(`Executing workflow: ${e.name}`),e.actions))try{await this.executeAction(r,t)}catch(t){console.error(`Failed to execute action in workflow ${e.name}:`,t)}}async executeAction(e,t){switch(e.type){case"notification":await this.executeNotificationAction(e,t);break;case"update_status":await this.executeUpdateStatusAction(e,t);break;case"create_record":await this.executeCreateRecordAction(e,t);break;case"send_reminder":await this.executeSendReminderAction(e,t);break;default:console.warn(`Unknown action type: ${e.type}`)}}async executeNotificationAction(e,t){let{type:r,priority:n,channels:o}=e.data,s=null;if(t.studentId){let e=await c.z.student.findUnique({where:{id:t.studentId},include:{user:!0}});e&&(s={id:e.id,name:e.user.name,phone:e.user.phone,email:e.user.email})}s&&await this.notificationService.sendNotification(s,{type:r,priority:n,channels:o,data:t})}async executeUpdateStatusAction(e,t){let{status:r}=e.data;t.paymentId?await c.z.payment.update({where:{id:t.paymentId},data:{status:r}}):t.enrollmentId&&await c.z.enrollment.update({where:{id:t.enrollmentId},data:{status:r}})}async executeCreateRecordAction(e,t){let{type:r}=e.data;"certificate"===r&&t.enrollmentId&&console.log(`Creating certificate for enrollment: ${t.enrollmentId}`)}async executeSendReminderAction(e,t){let{type:r,assignee:n}=e.data;console.log(`Creating reminder: ${r} for ${n}`)}async checkOverduePayments(){for(let e of(await c.z.payment.findMany({where:{status:"DEBT",createdAt:{lt:new Date(Date.now()-2592e6)}},include:{student:{include:{user:!0}}}})))await this.triggerWorkflow("payment.overdue",{paymentId:e.id,studentId:e.studentId,amount:e.amount,dueDate:e.dueDate,studentName:e.student.user.name})}async checkUpcomingClasses(){for(let e of(await c.z.class.findMany({where:{date:{gte:new Date,lte:new Date(Date.now()+108e5)}},include:{group:{include:{course:!0,enrollments:{include:{student:{include:{user:!0}}}}}}}}))){let t=e.group.enrollments.map(e=>({name:e.student.user.name,phone:e.student.user.phone,email:e.student.user.email||void 0}));await this.notificationService.sendClassReminder(t,{courseName:e.group.course.name,time:e.date.toLocaleTimeString()})}}async checkPaymentsDueSoon(){for(let e of(await c.z.payment.findMany({where:{status:"DEBT",createdAt:{gte:new Date(Date.now()-6048e5),lte:new Date}},include:{student:{include:{user:!0}}}})))await this.triggerWorkflow("payment.due_soon",{paymentId:e.id,studentId:e.studentId,amount:e.amount,dueDate:e.dueDate,studentName:e.student.user.name})}enableWorkflow(e){let t=this.workflows.find(t=>t.id===e);t&&(t.enabled=!0)}disableWorkflow(e){let t=this.workflows.find(t=>t.id===e);t&&(t.enabled=!1)}getWorkflows(){return this.workflows}addWorkflow(e){this.workflows.push(e)}}let p=null;function m(){return p||(p=new u),p}let f=d.z.object({event:d.z.string(),data:d.z.record(d.z.any())}),w=d.z.object({id:d.z.string(),name:d.z.string(),description:d.z.string(),trigger:d.z.object({event:d.z.string(),conditions:d.z.record(d.z.any()).optional(),delay:d.z.number().optional()}),actions:d.z.array(d.z.object({type:d.z.enum(["notification","update_status","create_record","send_reminder"]),data:d.z.record(d.z.any())})),enabled:d.z.boolean()});async function y(e){try{let{searchParams:t}=new URL(e.url),r=t.get("action"),n=m();if("list"===r){let e=n.getWorkflows();return i.NextResponse.json({workflows:e})}if("check-overdue-payments"===r)return await n.checkOverduePayments(),i.NextResponse.json({success:!0,message:"Overdue payment check completed"});if("check-upcoming-classes"===r)return await n.checkUpcomingClasses(),i.NextResponse.json({success:!0,message:"Upcoming classes check completed"});if("check-payments-due-soon"===r)return await n.checkPaymentsDueSoon(),i.NextResponse.json({success:!0,message:"Payments due soon check completed"});return i.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Error in workflows GET:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function g(e){try{let t=await e.json(),r=m();if("trigger"===t.action){let e=f.parse(t);return await r.triggerWorkflow(e.event,e.data),i.NextResponse.json({success:!0,message:`Workflow triggered for event: ${e.event}`})}if("add"===t.action){let e=w.parse(t.workflow);return r.addWorkflow(e),i.NextResponse.json({success:!0,message:"Workflow added successfully"})}if("enable"===t.action){let{workflowId:e}=t;if(!e)return i.NextResponse.json({error:"Workflow ID is required"},{status:400});return r.enableWorkflow(e),i.NextResponse.json({success:!0,message:`Workflow ${e} enabled`})}if("disable"===t.action){let{workflowId:e}=t;if(!e)return i.NextResponse.json({error:"Workflow ID is required"},{status:400});return r.disableWorkflow(e),i.NextResponse.json({success:!0,message:`Workflow ${e} disabled`})}return i.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){if(e instanceof d.z.ZodError)return i.NextResponse.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error in workflows POST:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let{workflowId:t,enabled:r}=await e.json(),n=m();if(!t)return i.NextResponse.json({error:"Workflow ID is required"},{status:400});return r?n.enableWorkflow(t):n.disableWorkflow(t),i.NextResponse.json({success:!0,message:`Workflow ${t} ${r?"enabled":"disabled"}`})}catch(e){return console.error("Error updating workflow:",e),i.NextResponse.json({error:"Failed to update workflow"},{status:500})}}let k=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/workflows/route",pathname:"/api/workflows",filename:"route",bundlePath:"app/api/workflows/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\workflows\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:x,workUnitAsyncStorage:v,serverHooks:b}=k;function R(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:v})}},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,580,5697,9526,2900],()=>r(47010));module.exports=n})();