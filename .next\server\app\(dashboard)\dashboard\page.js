(()=>{var e={};e.id=6337,e.ids=[6337],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23928:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29436:(e,s,t)=>{Promise.resolve().then(t.bind(t,82224))},33873:e=>{"use strict";e.exports=require("path")},43004:(e,s,t)=>{Promise.resolve().then(t.bind(t,67062))},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=t(60687),r=t(43210),l=t(96241);let n=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));n.displayName="Card";let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67062:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\page.tsx","default")},72730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},78122:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},79103:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var a=t(65239),r=t(48088),l=t(88170),n=t.n(l),i=t(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);t.d(s,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,67062)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},82224:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>L});var a=t(60687),r=t(43210),l=t(55192),n=t(59821),i=t(24934),d=t(69327),c=t(96241),o=t(58559),m=t(78122),h=t(18962);let x=(0,h.A)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);var u=t(85814),p=t.n(u);function j({limit:e=10,showHeader:s=!0,showRefresh:t=!0,showViewAll:h=!0,userId:u,resource:j,className:N}){let[f,g]=(0,r.useState)([]),[v,y]=(0,r.useState)(!0),[b,w]=(0,r.useState)(null),A=(0,r.useCallback)(async()=>{y(!0),w(null);try{let s=new URLSearchParams({limit:e.toString(),page:"1"});u&&s.append("userId",u),j&&s.append("resource",j);let t=await fetch(`/api/activity-logs?${s}`);if(!t.ok)throw Error("Failed to fetch activities");let a=await t.json();g(a.logs||[])}catch(e){w(e instanceof Error?e.message:"Failed to load activities")}finally{y(!1)}},[e,u,j]),k=e=>{switch(e){case"ADMIN":return"bg-red-100 text-red-800";case"MANAGER":return"bg-blue-100 text-blue-800";case"TEACHER":return"bg-green-100 text-green-800";case"RECEPTION":return"bg-yellow-100 text-yellow-800";case"CASHIER":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return b?(0,a.jsx)(l.Zp,{className:N,children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"text-center text-red-600",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,a.jsx)("p",{children:"Failed to load activity feed"}),(0,a.jsxs)(i.$,{variant:"outline",size:"sm",onClick:A,className:"mt-2",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Retry"]})]})})}):(0,a.jsxs)(l.Zp,{className:N,children:[s&&(0,a.jsx)(l.aR,{className:"pb-3",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Recent Activity"]}),(0,a.jsx)(l.BT,{children:"Latest system activities and user actions"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[t&&(0,a.jsx)(i.$,{variant:"outline",size:"sm",onClick:A,disabled:v,children:(0,a.jsx)(m.A,{className:`h-4 w-4 ${v?"animate-spin":""}`})}),h&&(0,a.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,a.jsxs)(p(),{href:"/dashboard/admin/activity-logs",children:[(0,a.jsx)(x,{className:"h-4 w-4 mr-2"}),"View All"]})})]})]})}),(0,a.jsx)(l.Wu,{className:"p-0",children:v?(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Loading activities..."})]}):0===f.length?(0,a.jsxs)("div",{className:"p-6 text-center text-gray-500",children:[(0,a.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No recent activities"})]}):(0,a.jsx)(d.F,{className:"h-96",children:(0,a.jsx)("div",{className:"p-4 space-y-3",children:f.map((e,s)=>{let t=["DELETE","LOGIN","LOGOUT"].includes(e.action)?"high":["CREATE","CONTACT","COMPLETE"].includes(e.action)?"medium":"low",r=function(e){var s,t;let a={CREATE:"Created",UPDATE:"Updated",DELETE:"Deleted",LOGIN:"Logged in",LOGOUT:"Logged out",CONTACT:"Contacted",COMPLETE:"Completed",VIEW:"Viewed",EXPORT:"Exported"}[s=e.action]||s,r={student:"Student",lead:"Lead",payment:"Payment",group:"Group",enrollment:"Enrollment",assessment:"Assessment",teacher:"Teacher",course:"Course",class:"Class",attendance:"Attendance",user:"User",auth:"Authentication"}[t=e.resource]||t.charAt(0).toUpperCase()+t.slice(1),l=e.user.name;switch(e.action){case"LOGIN":return`${l} logged into the system`;case"LOGOUT":return`${l} logged out of the system`;case"CONTACT":if("lead"===e.resource){let s=e.details?.leadName||"a lead";return`${l} contacted ${s}`}break;case"CREATE":if("student"===e.resource){let s=e.details?.studentName||"a student";return`${l} created student record for ${s}`}if("payment"===e.resource){let s=e.details?.amount||"payment";return`${l} created a payment record (${s})`}break;case"COMPLETE":if("assessment"===e.resource){let s=e.details?.studentName||"a student",t=e.details?.type||"assessment";return`${l} completed ${t.toLowerCase().replace("_"," ")} for ${s}`}}return`${l} ${a.toLowerCase()} ${r.toLowerCase()}`}(e),l={CREATE:"➕",UPDATE:"✏️",DELETE:"\uD83D\uDDD1️",LOGIN:"\uD83D\uDD10",LOGOUT:"\uD83D\uDEAA",CONTACT:"\uD83D\uDCDE",COMPLETE:"✅",VIEW:"\uD83D\uDC41️",EXPORT:"\uD83D\uDCE4"}[e.action]||"\uD83D\uDCDD",i=function(e){let s=new Date(e),t=Math.floor((new Date().getTime()-s.getTime())/1e3);if(t<60)return"Just now";let a=Math.floor(t/60);if(a<60)return`${a} minute${a>1?"s":""} ago`;let r=Math.floor(a/60);if(r<24)return`${r} hour${r>1?"s":""} ago`;let l=Math.floor(r/24);if(l<7)return`${l} day${l>1?"s":""} ago`;let n=Math.floor(l/7);return n<4?`${n} week${n>1?"s":""} ago`:s.toLocaleDateString()}(e.createdAt);return(0,a.jsxs)("div",{className:`flex items-start space-x-3 p-3 rounded-lg border transition-colors hover:bg-gray-50 ${0===s?"bg-blue-50 border-blue-200":"border-gray-200"}`,children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm ${"high"===t?"bg-red-100":"medium"===t?"bg-yellow-100":"bg-gray-100"}`,children:l})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:r}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(n.E,{className:k(e.userRole),variant:"secondary",children:e.userRole}),"high"===t&&(0,a.jsx)(n.E,{variant:"destructive",className:"text-xs",children:"High"})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.user.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",title:(0,c.Yq)(e.createdAt),children:i})]})]})]},e.id)})})})})]})}var N=t(72730),f=t(40228),g=t(74808),v=t(41312),y=t(25541);let b=(0,h.A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var w=t(23026),A=t(82080),k=t(23689),C=t(23928);let E=(0,h.A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);var T=t(48730),R=t(96545);function L(){let e,{currentBranch:s}=(0,R.O)(),[t,n]=(0,r.useState)(null),[d,c]=(0,r.useState)(!0),[o,m]=(0,r.useState)(null),h=async()=>{try{c(!0);let e=await fetch("/api/dashboard/stats");if(!e.ok)throw Error("Failed to fetch dashboard stats");let s=await e.json();n(s),m(null)}catch(e){console.error("Error fetching dashboard stats:",e),m("Failed to load dashboard data")}finally{c(!1)}},x=e=>new Intl.NumberFormat().format(e);return d?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsx)(N.A,{className:"h-8 w-8 animate-spin text-blue-600"})}):o?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:o}),(0,a.jsx)(i.$,{onClick:h,children:"Try Again"})]})}):(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 tracking-tight",children:["Welcome to ",s.name]}),(0,a.jsx)("p",{className:"text-gray-600 mt-1",children:"Here's what's happening with your educational center today."})]}),(0,a.jsxs)("div",{className:"flex gap-3",children:[(0,a.jsxs)(i.$,{variant:"outline",className:"shadow-sm",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"View Schedule"]}),(0,a.jsxs)(i.$,{className:"shadow-sm",children:[(0,a.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Analytics"]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(l.Zp,{className:"kpi-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"kpi-label",children:"Total Students"}),(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center",children:(0,a.jsx)(v.A,{className:"h-5 w-5 text-blue-600"})})]})}),(0,a.jsxs)(l.Wu,{className:"dashboard-card-content",children:[(0,a.jsx)("div",{className:"kpi-value text-blue-700",children:x(t?.totalStudents.count||0)}),(0,a.jsxs)("div",{className:`kpi-change mt-2 ${(t?.totalStudents.growth??0)>=0?"text-green-600":"text-red-600"}`,children:[(t?.totalStudents.growth??0)>=0?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(b,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[(t?.totalStudents.growth??0)>=0?"+":"",t?.totalStudents.growth||0,"% from last month"]})]})]})]}),(0,a.jsxs)(l.Zp,{className:"kpi-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"kpi-label",children:"New Leads"}),(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-green-50 flex items-center justify-center",children:(0,a.jsx)(w.A,{className:"h-5 w-5 text-green-600"})})]})}),(0,a.jsxs)(l.Wu,{className:"dashboard-card-content",children:[(0,a.jsx)("div",{className:"kpi-value text-green-700",children:x(t?.newLeads.count||0)}),(0,a.jsxs)("div",{className:`kpi-change mt-2 ${(t?.newLeads.growth??0)>=0?"text-green-600":"text-red-600"}`,children:[(t?.newLeads.growth??0)>=0?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(b,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[(t?.newLeads.growth??0)>=0?"+":"",t?.newLeads.growth||0,"% from last week"]})]})]})]}),(0,a.jsxs)(l.Zp,{className:"kpi-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"kpi-label",children:"Active Groups"}),(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center",children:(0,a.jsx)(A.A,{className:"h-5 w-5 text-purple-600"})})]})}),(0,a.jsxs)(l.Wu,{className:"dashboard-card-content",children:[(0,a.jsx)("div",{className:"kpi-value text-purple-700",children:x(t?.activeGroups.count||0)}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsxs)("div",{className:"kpi-change text-blue-600",children:[(0,a.jsx)(k.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Active groups"})]}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"text-xs text-blue-600 hover:text-blue-700",children:"View All"})]})]})]}),(0,a.jsxs)(l.Zp,{className:"kpi-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(l.ZB,{className:"kpi-label",children:"Monthly Revenue"}),(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-amber-50 flex items-center justify-center",children:(0,a.jsx)(C.A,{className:"h-5 w-5 text-amber-600"})})]})}),(0,a.jsxs)(l.Wu,{className:"dashboard-card-content",children:[(0,a.jsx)("div",{className:"kpi-value text-amber-700",children:(e=t?.monthlyRevenue.amount||0,new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0,maximumFractionDigits:0}).format(e).replace("UZS","UZS"))}),(0,a.jsxs)("div",{className:`kpi-change mt-2 ${(t?.monthlyRevenue.growth??0)>=0?"text-green-600":"text-red-600"}`,children:[(t?.monthlyRevenue.growth??0)>=0?(0,a.jsx)(y.A,{className:"h-4 w-4"}):(0,a.jsx)(b,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:[(t?.monthlyRevenue.growth??0)>=0?"+":"",t?.monthlyRevenue.growth||0,"% from last month"]})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)(l.Zp,{className:"dashboard-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold text-gray-900",children:"Recent Leads"}),(0,a.jsx)(l.BT,{className:"mt-1",children:"Latest inquiries from potential students"})]}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"text-sm",children:(0,a.jsx)(E,{className:"h-4 w-4"})})]})}),(0,a.jsx)(l.Wu,{className:"dashboard-card-content",children:(0,a.jsx)("div",{className:"space-y-4",children:t?.recentLeads&&t.recentLeads.length>0?t.recentLeads.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.course})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:e.time}),(0,a.jsx)("span",{className:`status-badge ${"NEW"===e.status?"status-active":"CONTACTED"===e.status?"status-pending":"status-active"}`,children:e.status})]})]},s)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(w.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No recent leads"})]})})})]}),(0,a.jsxs)(l.Zp,{className:"dashboard-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold text-gray-900",children:"Upcoming Classes"}),(0,a.jsx)(l.BT,{className:"mt-1",children:"Today's scheduled classes"})]}),(0,a.jsx)(i.$,{variant:"ghost",size:"sm",className:"text-sm",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})}),(0,a.jsx)(l.Wu,{className:"dashboard-card-content",children:(0,a.jsx)("div",{className:"space-y-4",children:t?.upcomingClasses&&t.upcomingClasses.length>0?t.upcomingClasses.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-gray-900",children:e.group}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:e.teacher})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-blue-600",children:e.time}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.room})]})]},s)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No classes scheduled for today"})]})})})]}),(0,a.jsxs)(l.Zp,{className:"dashboard-card",children:[(0,a.jsx)(l.aR,{className:"dashboard-card-header",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(l.ZB,{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"}),(0,a.jsx)(l.BT,{className:"mt-1",children:"Latest system activities"})]}),(0,a.jsxs)(i.$,{variant:"ghost",size:"sm",className:"text-sm",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"View All"]})]})}),(0,a.jsx)(l.Wu,{className:"dashboard-card-content",children:(0,a.jsx)(j,{limit:8,showHeader:!1,showRefresh:!1,showViewAll:!1})})]})]})]})}}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,7615,2918,8887,3039],()=>t(79103));module.exports=a})();