(()=>{var e={};e.id=6834,e.ids=[6834],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43607:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>h,routeModule:()=>x,serverHooks:()=>w,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{DELETE:()=>m,GET:()=>p,PUT:()=>d});var n=t(96559),o=t(48088),a=t(37719),u=t(32190),i=t(79464),c=t(45697);let l=c.Ik({name:c.Yj().min(1).optional(),level:c.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]).optional(),description:c.Yj().optional(),duration:c.ai().min(1).optional(),price:c.ai().min(0).optional(),isActive:c.zM().optional()});async function p(e,{params:r}){try{let{id:e}=await r,t=await i.z.course.findUnique({where:{id:e},include:{groups:{include:{teacher:{include:{user:{select:{name:!0}}}},_count:{select:{enrollments:!0}}},orderBy:{createdAt:"desc"}},_count:{select:{groups:!0}}}});if(!t)return u.NextResponse.json({error:"Course not found"},{status:404});let s=t.groups.reduce((e,r)=>e+r._count.enrollments,0);return u.NextResponse.json({...t,totalEnrolledStudents:s})}catch(e){return console.error("Error fetching course:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e,{params:r}){try{let{id:t}=await r,s=await e.json(),n=l.parse(s),o=await i.z.course.findUnique({where:{id:t}});if(!o)return u.NextResponse.json({error:"Course not found"},{status:404});if(n.name&&n.name!==o.name&&await i.z.course.findUnique({where:{name:n.name}}))return u.NextResponse.json({error:"Course name already exists"},{status:400});let a=await i.z.course.update({where:{id:t},data:{...n,updatedAt:new Date},include:{_count:{select:{groups:!0}}}});return u.NextResponse.json(a)}catch(e){if(e instanceof c.G)return u.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating course:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let{id:e}=await r,t=await i.z.course.findUnique({where:{id:e},include:{groups:{include:{enrollments:!0}}}});if(!t)return u.NextResponse.json({error:"Course not found"},{status:404});if(t.groups.length>0){if(t.groups.some(e=>e.enrollments.some(e=>"ACTIVE"===e.status)))return u.NextResponse.json({error:"Cannot delete course with active enrollments",details:"Course has groups with active student enrollments"},{status:400});return u.NextResponse.json({error:"Cannot delete course with existing groups",details:`Course has ${t.groups.length} group(s). Please delete groups first.`},{status:400})}return await i.z.course.delete({where:{id:e}}),u.NextResponse.json({message:"Course deleted successfully",deletedId:e})}catch(e){return console.error("Error deleting course:",e),u.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/courses/[id]/route",pathname:"/api/courses/[id]",filename:"route",bundlePath:"app/api/courses/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\courses\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:g,workUnitAsyncStorage:f,serverHooks:w}=x;function h(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697],()=>t(43607));module.exports=s})();