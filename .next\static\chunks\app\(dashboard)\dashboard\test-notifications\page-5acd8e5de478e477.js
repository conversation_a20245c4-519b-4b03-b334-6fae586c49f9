(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5311],{1207:(e,t,r)=>{Promise.resolve().then(r.bind(r,1550))},1550:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var n=r(5155),i=r(8482),s=r(7168),a=r(3580);function o(){let{toast:e}=(0,a.dj)();return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Test Notifications"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Test the notification system"})]}),(0,n.jsxs)(i.Zp,{children:[(0,n.jsxs)(i.aR,{children:[(0,n.jsx)(i.ZB,{children:"Toast Notifications"}),(0,n.jsx)(i.BT,{children:"Test different types of toast notifications"})]}),(0,n.jsx)(i.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,n.jsx)(s.$,{onClick:()=>{e({title:"Success!",description:"This is a success notification."})},variant:"default",children:"Success Toast"}),(0,n.jsx)(s.$,{onClick:()=>{e({variant:"destructive",title:"Error!",description:"This is an error notification."})},variant:"destructive",children:"Error Toast"}),(0,n.jsx)(s.$,{onClick:()=>{e({title:"Information",description:"This is an informational notification."})},variant:"outline",children:"Info Toast"}),(0,n.jsx)(s.$,{onClick:()=>{e({title:"Warning",description:"This is a warning notification."})},variant:"secondary",children:"Warning Toast"})]})})]})]})}},3580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var n=r(2115);let i=0,s=new Map,a=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},5e3);s.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],c={toasts:[]};function d(e){c=o(c,e),l.forEach(e=>{e(c)})}function u(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(c);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},3999:(e,t,r)=>{"use strict";r.d(t,{Yq:()=>o,cn:()=>s,r6:()=>l,vv:()=>a});var n=r(2596),i=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,i.QP)((0,n.$)(t))}function a(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function o(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function l(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},6101:(e,t,r)=>{"use strict";r.d(t,{s:()=>a,t:()=>s});var n=r(2115);function i(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function s(...e){return t=>{let r=!1,n=e.map(e=>{let n=i(e,t);return r||"function"!=typeof n||(r=!0),n});if(r)return()=>{for(let t=0;t<n.length;t++){let r=n[t];"function"==typeof r?r():i(e[t],null)}}}}function a(...e){return n.useCallback(s(...e),e)}},7168:(e,t,r)=>{"use strict";r.d(t,{$:()=>c,r:()=>l});var n=r(5155),i=r(2115),s=r(9708),a=r(2085),o=r(3999);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=i.forwardRef((e,t)=>{let{className:r,variant:i,size:a,asChild:c=!1,...d}=e,u=c?s.DX:"button";return(0,n.jsx)(u,{className:(0,o.cn)(l({variant:i,size:a,className:r})),ref:t,...d})});c.displayName="Button"},8482:(e,t,r)=>{"use strict";r.d(t,{BT:()=>c,Wu:()=>d,ZB:()=>l,Zp:()=>a,aR:()=>o});var n=r(5155),i=r(2115),s=r(3999);let a=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",r),...i})});a.displayName="Card";let o=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...i})});o.displayName="CardHeader";let l=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...i})});l.displayName="CardTitle";let c=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...i})});c.displayName="CardDescription";let d=i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...i})});d.displayName="CardContent",i.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...i})}).displayName="CardFooter"},9708:(e,t,r)=>{"use strict";r.d(t,{DX:()=>o,Dc:()=>c,TL:()=>a});var n=r(2115),i=r(6101),s=r(5155);function a(e){let t=function(e){let t=n.forwardRef((e,t)=>{let{children:r,...s}=e;if(n.isValidElement(r)){var a;let e,o,l=(a=r,(o=(e=Object.getOwnPropertyDescriptor(a.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.ref:(o=(e=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?a.props.ref:a.props.ref||a.ref),c=function(e,t){let r={...t};for(let n in t){let i=e[n],s=t[n];/^on[A-Z]/.test(n)?i&&s?r[n]=(...e)=>{let t=s(...e);return i(...e),t}:i&&(r[n]=i):"style"===n?r[n]={...i,...s}:"className"===n&&(r[n]=[i,s].filter(Boolean).join(" "))}return{...e,...r}}(s,r.props);return r.type!==n.Fragment&&(c.ref=t?(0,i.t)(t,l):l),n.cloneElement(r,c)}return n.Children.count(r)>1?n.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),r=n.forwardRef((e,r)=>{let{children:i,...a}=e,o=n.Children.toArray(i),l=o.find(d);if(l){let e=l.props.children,i=o.map(t=>t!==l?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,s.jsx)(t,{...a,ref:r,children:n.isValidElement(e)?n.cloneElement(e,void 0,i):null})}return(0,s.jsx)(t,{...a,ref:r,children:i})});return r.displayName=`${e}.Slot`,r}var o=a("Slot"),l=Symbol("radix.slottable");function c(e){let t=({children:e})=>(0,s.jsx)(s.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=l,t}function d(e){return n.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===l}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,8441,1684,7358],()=>t(1207)),_N_E=e.O()}]);