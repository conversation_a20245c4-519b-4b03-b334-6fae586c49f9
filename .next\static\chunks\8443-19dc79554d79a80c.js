"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8443],{7705:(e,a,s)=>{s.d(a,{BranchProvider:()=>c,O:()=>i,Z:()=>d});var l=s(5155),r=s(2115);let t=(0,r.createContext)(void 0),n=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"<PERSON><PERSON>'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function c(e){let{children:a}=e,[s,c]=(0,r.useState)(n[0]),[i]=(0,r.useState)(n),[d,m]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let a=i.find(a=>a.id===e);a&&c(a)}m(!1)},[i]),(0,l.jsx)(t.Provider,{value:{currentBranch:s,branches:i,switchBranch:e=>{let a=i.find(a=>a.id===e);a&&(c(a),localStorage.setItem("selectedBranch",e))},isLoading:d},children:a})}function i(){let e=(0,r.useContext)(t);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,r.useContext)(t)||null}},8443:(e,a,s)=>{s.d(a,{A:()=>w});var l=s(5155),r=s(2115),t=s(2177),n=s(221),c=s(1153),i=s(7168),d=s(9852),m=s(2714),h=s(5784),o=s(9474),x=s(8482),u=s(9026),j=s(1007),v=s(9420),p=s(8883),b=s(9074),N=s(4516),f=s(7949),g=s(7624),y=s(7705);let A=c.Ik({name:c.Yj().min(2,"Name must be at least 2 characters"),phone:c.Yj().min(9,"Phone number must be at least 9 characters"),email:c.Yj().email("Invalid email address").optional().or(c.eu("")),level:c.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]),branch:c.Yj().min(1,"Branch is required"),emergencyContact:c.Yj().optional(),dateOfBirth:c.Yj().optional(),address:c.Yj().optional()}),B=[{value:"A1",label:"A1 - Beginner"},{value:"A2",label:"A2 - Elementary"},{value:"B1",label:"B1 - Intermediate"},{value:"B2",label:"B2 - Upper Intermediate"},{value:"IELTS",label:"IELTS"},{value:"SAT",label:"SAT Preparation"},{value:"MATH",label:"Mathematics"},{value:"KIDS",label:"Kids English"}],S=["Main Branch","Branch"];function w(e){let{initialData:a,onSubmit:s,onCancel:c,isEditing:w=!1}=e,{currentBranch:E}=(0,y.O)(),[C,I]=(0,r.useState)(!1),[F,T]=(0,r.useState)(null),{register:k,handleSubmit:O,setValue:J,watch:P,formState:{errors:Y}}=(0,t.mN)({resolver:(0,n.u)(A),defaultValues:{name:(null==a?void 0:a.name)||"",phone:(null==a?void 0:a.phone)||"",email:(null==a?void 0:a.email)||"",level:(null==a?void 0:a.level)||"A1",branch:(null==a?void 0:a.branch)||E.name,emergencyContact:(null==a?void 0:a.emergencyContact)||"",dateOfBirth:(null==a?void 0:a.dateOfBirth)||"",address:(null==a?void 0:a.address)||""}}),M=P("level"),q=P("branch");(0,r.useEffect)(()=>{!(null==a?void 0:a.branch)&&(null==E?void 0:E.name)&&J("branch",E.name)},[E,null==a?void 0:a.branch,J]);let L=async e=>{I(!0),T(null);try{if(!e.branch)throw Error("Branch is required");await s(e)}catch(e){console.error("Form submission error:",e),T(e instanceof Error?e.message:"An error occurred")}finally{I(!1)}};return(0,l.jsxs)(x.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,l.jsxs)(x.aR,{children:[(0,l.jsxs)(x.ZB,{className:"flex items-center",children:[(0,l.jsx)(j.A,{className:"h-5 w-5 mr-2"}),w?"Edit Student":"Add New Student"]}),(0,l.jsx)(x.BT,{children:w?"Update student information":"Enter student details to create a new profile"})]}),(0,l.jsx)(x.Wu,{children:(0,l.jsxs)("form",{onSubmit:O(L),className:"space-y-6",children:[F&&(0,l.jsx)(u.Fc,{variant:"destructive",children:(0,l.jsx)(u.TN,{children:F})}),Object.keys(Y).length>0&&(0,l.jsx)(u.Fc,{variant:"destructive",children:(0,l.jsxs)(u.TN,{children:["Please fix the following errors:",(0,l.jsx)("ul",{className:"mt-2 list-disc list-inside",children:Object.entries(Y).map(e=>{let[a,s]=e;return(0,l.jsx)("li",{children:null==s?void 0:s.message},a)})})]})}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(j.A,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Personal Information"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"name",children:"Full Name *"}),(0,l.jsx)(d.p,{id:"name",...k("name"),placeholder:"Enter full name",className:Y.name?"border-red-500":""}),Y.name&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:Y.name.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"phone",children:"Phone Number *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"phone",...k("phone"),placeholder:"+998 90 123 45 67",className:"pl-10 ".concat(Y.phone?"border-red-500":"")})]}),Y.phone&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:Y.phone.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"email",children:"Email Address"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(p.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"email",type:"email",...k("email"),placeholder:"<EMAIL>",className:"pl-10 ".concat(Y.email?"border-red-500":"")})]}),Y.email&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:Y.email.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"dateOfBirth",type:"date",...k("dateOfBirth"),className:"pl-10"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"address",children:"Address"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(N.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,l.jsx)(o.T,{id:"address",...k("address"),placeholder:"Enter full address",className:"pl-10 min-h-[80px]"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"emergencyContact",children:"Emergency Contact"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"emergencyContact",...k("emergencyContact"),placeholder:"Emergency contact number",className:"pl-10"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(f.A,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Academic Information"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"level",children:"English Level *"}),(0,l.jsxs)(h.l6,{value:M,onValueChange:e=>J("level",e),children:[(0,l.jsx)(h.bq,{className:Y.level?"border-red-500":"",children:(0,l.jsx)(h.yv,{placeholder:"Select level"})}),(0,l.jsx)(h.gC,{children:B.map(e=>(0,l.jsx)(h.eb,{value:e.value,children:e.label},e.value))})]}),Y.level&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:Y.level.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"branch",children:"Branch *"}),(0,l.jsxs)(h.l6,{value:q,onValueChange:e=>J("branch",e),children:[(0,l.jsx)(h.bq,{className:Y.branch?"border-red-500":"",children:(0,l.jsx)(h.yv,{placeholder:"Select branch"})}),(0,l.jsx)(h.gC,{children:S.map(e=>(0,l.jsx)(h.eb,{value:e,children:e},e))})]}),Y.branch&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:Y.branch.message})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[c&&(0,l.jsx)(i.$,{type:"button",variant:"outline",onClick:c,children:"Cancel"}),(0,l.jsxs)(i.$,{type:"submit",disabled:C,children:[C&&(0,l.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),w?"Update Student":"Create Student"]})]})]})})]})}}}]);