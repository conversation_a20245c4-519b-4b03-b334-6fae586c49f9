(()=>{var e={};e.id=7986,e.ids=[7986],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=i.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},a=await fetch(s,{method:r.method,headers:n,body:r.data?JSON.stringify(r.data):void 0}),o=await a.json();return{success:a.ok,data:o,status:a.status,error:a.ok?void 0:o.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>i,cU:()=>a,g2:()=>s});class a{static async authenticateUser(e,r){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class i{static logRequest(e,r,t,s){let n=new Date().toISOString(),a=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:a,success:t,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33121:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d});var n=t(96559),a=t(48088),i=t(37719),o=t(32190),l=t(19854),u=t(41098),c=t(79464);async function d(e,{params:r}){try{let e=await (0,l.getServerSession)(u.N);if(!e?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r;if("STUDENT"===e.user.role&&e.user.id!==t)return o.NextResponse.json({error:"Forbidden"},{status:403});let s=await c.z.student.findUnique({where:{userId:t},include:{user:{select:{name:!0,email:!0}},currentGroup:{include:{course:{select:{name:!0,level:!0}}}},attendances:{include:{class:{select:{date:!0,topic:!0}}},orderBy:{createdAt:"desc"},take:50},assessments:{orderBy:{createdAt:"desc"},take:20},payments:{where:{status:"PAID"},orderBy:{paidDate:"desc"}}}});if(!s)return o.NextResponse.json({error:"Student not found"},{status:404});let n=s.attendances.length,a=s.attendances.filter(e=>"PRESENT"===e.status).length,i=n>0?a/n*100:0,d=s.assessments.filter(e=>null!==e.score),p=d.length>0?d.reduce((e,r)=>e+(r.score||0),0)/d.length:0,m=Math.min(100,.4*i+.6*p),g=["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"],h=g.indexOf(s.level),v=h>=0&&h<g.length-1?g[h+1]:null,E=await c.z.assessment.findMany({where:{studentId:s.id,completedAt:{not:null}},orderBy:{completedAt:"desc"},take:10}),f=function(e,r,t){if(0===e.length){let e=Math.max(0,Math.min(100,r));return{speaking:Math.max(0,Math.min(100,.9*e)),listening:Math.max(0,Math.min(100,1.1*e)),reading:Math.max(0,Math.min(100,1.05*e)),writing:Math.max(0,Math.min(100,.85*e)),grammar:Math.max(0,Math.min(100,e)),vocabulary:Math.max(0,Math.min(100,.95*e))}}let s={speaking:[],listening:[],reading:[],writing:[],grammar:[],vocabulary:[]};e.forEach(e=>{if(e.results&&"object"==typeof e.results){let r=e.results;Object.keys(s).forEach(e=>{void 0!==r[e]?s[e].push(Number(r[e])):r.skills&&void 0!==r.skills[e]&&s[e].push(Number(r.skills[e]))})}});let n=(e,t)=>e.length>0?e.reduce((e,r)=>e+r,0)/e.length:Math.max(0,Math.min(100,r*(({speaking:.9,listening:1.1,reading:1.05,writing:.85,grammar:1,vocabulary:.95})[t]||1)));return{speaking:n(s.speaking,"speaking"),listening:n(s.listening,"listening"),reading:n(s.reading,"reading"),writing:n(s.writing,"writing"),grammar:n(s.grammar,"grammar"),vocabulary:n(s.vocabulary,"vocabulary")}}(E,p,s.level),S=[{name:"Speaking",progress:f.speaking,level:s.level},{name:"Listening",progress:f.listening,level:s.level},{name:"Reading",progress:f.reading,level:s.level},{name:"Writing",progress:f.writing,level:s.level},{name:"Grammar",progress:f.grammar,level:s.level},{name:"Vocabulary",progress:f.vocabulary,level:s.level}],y=[];i>=95&&y.push({name:"Perfect Attendance",date:new Date().toISOString(),icon:"\uD83C\uDFAF"}),p>=90&&y.push({name:"Excellence Award",date:new Date().toISOString(),icon:"\uD83C\uDFC6"}),d.length>=10&&y.push({name:"Assessment Master",date:new Date().toISOString(),icon:"\uD83D\uDCDA"});let R={student:{name:s.user.name,email:s.user.email,level:s.level,nextLevel:v,branch:s.branch},currentGroup:s.currentGroup?{name:s.currentGroup.name,course:s.currentGroup.course.name,level:s.currentGroup.course.level}:null,progress:{overall:Math.round(m),attendance:Math.round(i),averageScore:Math.round(p)},statistics:{totalClasses:n,attendedClasses:a,completedAssessments:d.length,pendingAssessments:s.assessments.filter(e=>null===e.score).length,totalPayments:s.payments.length,totalPaid:s.payments.reduce((e,r)=>e+Number(r.amount),0)},skills:S,achievements:y,recentActivity:{assessments:s.assessments.slice(0,5).map(e=>({id:e.id,testName:e.testName,score:e.score,maxScore:e.maxScore,passed:e.passed,completedAt:e.completedAt})),attendance:s.attendances.slice(0,10).map(e=>({id:e.id,date:e.class.date,topic:e.class.topic,status:e.status}))}};return o.NextResponse.json(R)}catch(e){return console.error("Error fetching student progress:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/[id]/progress/route",pathname:"/api/students/[id]/progress",filename:"route",bundlePath:"app/api/students/[id]/progress/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\[id]\\progress\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:h}=p;function v(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},41098:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(13581),n=t(7786);let a={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let r=await n.cU.authenticateUser(e.phone,e.password);if(!r.success)return console.error("Authentication failed:",r.error),null;let t=r.data.user;if(!t)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(t.role))return console.error("User role not allowed on staff server:",t.role),null;return{id:t.id,phone:t.phone,name:t.name,email:t.email,role:t.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role||null),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3412],()=>t(33121));module.exports=s})();