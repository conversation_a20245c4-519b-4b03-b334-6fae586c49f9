"use strict";(()=>{var e={};e.id=9673,e.ids=[9673],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4804:(e,t,r)=>{r.r(t),r.d(t,{patchFetch:()=>N,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>f,workUnitAsyncStorage:()=>v});var s={};r.r(s),r.d(s,{GET:()=>m,PATCH:()=>h});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(19854),d=r(41098),p=r(79464),l=r(99326),c=r(45697);let x=c.Ik({reEnrollmentNotes:c.Yj().optional(),lastContactedAt:c.Yj().optional()});async function m(e){try{let t=await (0,u.getServerSession)(d.N);if(!t?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:r}=new URL(e.url),s=parseInt(r.get("page")||"1"),n=parseInt(r.get("limit")||"20"),o=r.get("search"),a=r.get("branch"),l={droppedAt:{not:null}};a&&(l.branch="main"===a?"Main Branch":"Branch"),o&&(l.OR=[{user:{name:{contains:o,mode:"insensitive"}}},{user:{phone:{contains:o}}},{user:{email:{contains:o,mode:"insensitive"}}}]),a&&(l.branch=a);let[c,x]=await Promise.all([p.z.student.findMany({where:l,include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}},enrollments:{include:{group:{include:{course:{select:{name:!0,level:!0}}}}},orderBy:{createdAt:"desc"},take:1}},orderBy:{droppedAt:"desc"},skip:(s-1)*n,take:n}),p.z.student.count({where:l})]);return i.NextResponse.json({droppedStudents:c,pagination:{page:s,limit:n,total:x,pages:Math.ceil(x/n)}})}catch(e){return console.error("Error fetching dropped students:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let t=await (0,u.getServerSession)(d.N);if(!t?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER","RECEPTION"].includes(t.user.role))return i.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:r}=new URL(e.url),s=r.get("studentId");if(!s)return i.NextResponse.json({error:"Student ID is required"},{status:400});let n=await e.json(),o=x.parse(n),a=await p.z.student.findFirst({where:{id:s,droppedAt:{not:null}},include:{user:{select:{name:!0}}}});if(!a)return i.NextResponse.json({error:"Dropped student not found"},{status:404});let c={updatedAt:new Date};o.reEnrollmentNotes&&(c.reEnrollmentNotes=o.reEnrollmentNotes),o.lastContactedAt?c.lastContactedAt=new Date(o.lastContactedAt):c.lastContactedAt=new Date;let m=await p.z.student.update({where:{id:s},data:c,include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}});return await l._.logDroppedStudentContacted(t.user.id,t.user.role,s,{studentName:a.user.name,notes:o.reEnrollmentNotes},e),i.NextResponse.json(m)}catch(e){if(e instanceof c.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating dropped student contact:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/students/dropped/route",pathname:"/api/students/dropped",filename:"route",bundlePath:"app/api/students/dropped/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\dropped\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:f,workUnitAsyncStorage:v,serverHooks:w}=g;function N(){return(0,a.patchFetch)({workAsyncStorage:f,workUnitAsyncStorage:v})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,5697,3412,1971],()=>r(4804));module.exports=s})();