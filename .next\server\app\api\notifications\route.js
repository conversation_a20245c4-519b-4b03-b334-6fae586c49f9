"use strict";(()=>{var e={};e.id=2170,e.ids=[2170],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},21820:e=>{e.exports=require("os")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},37366:e=>{e.exports=require("dns")},41903:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>v,serverHooks:()=>q,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>R});var n={};t.r(n),t.d(n,{GET:()=>f,POST:()=>m});var s=t(96559),i=t(48088),a=t(37719),o=t(32190),u=t(45697),p=t(21321),c=t(79464);let l=u.z.object({recipientId:u.z.string(),recipientType:u.z.enum(["student","teacher","parent"]),type:u.z.enum(["enrollment","payment","reminder","completion","attendance"]),priority:u.z.enum(["low","medium","high","urgent"]),channels:u.z.array(u.z.enum(["sms","email","push"])).optional(),data:u.z.record(u.z.any())}),d=u.z.object({recipientIds:u.z.array(u.z.string()),recipientType:u.z.enum(["student","teacher","parent"]),type:u.z.enum(["enrollment","payment","reminder","completion","attendance"]),priority:u.z.enum(["low","medium","high","urgent"]),channels:u.z.array(u.z.enum(["sms","email","push"])).optional(),data:u.z.record(u.z.any())});async function m(e){try{let r=await e.json(),t=(0,p.d)();if(r.recipientIds&&Array.isArray(r.recipientIds)){let e=d.parse(r),n=await h(e.recipientIds,e.recipientType);if(0===n.length)return o.NextResponse.json({error:"No valid recipients found"},{status:400});let s=await t.sendBulkNotification(n,{type:e.type,priority:e.priority,channels:e.channels,data:e.data}),i=s.filter(e=>e.success).length;return o.NextResponse.json({success:!0,message:`Sent ${i}/${n.length} notifications successfully`,results:s})}{let e=l.parse(r),n=await h([e.recipientId],e.recipientType);if(0===n.length)return o.NextResponse.json({error:"Recipient not found"},{status:404});let s=n[0],i=await t.sendNotification(s,{type:e.type,priority:e.priority,channels:e.channels,data:e.data});return o.NextResponse.json({success:i.success,message:i.success?"Notification sent successfully":"Failed to send notification",result:i})}}catch(e){if(e instanceof u.z.ZodError)return o.NextResponse.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error sending notification:",e),o.NextResponse.json({error:"Failed to send notification"},{status:500})}}async function f(e){try{let{searchParams:r}=new URL(e.url),t=r.get("action");if("templates"===t)return o.NextResponse.json({templates:{enrollment:{name:"Enrollment Confirmation",description:"Sent when a student enrolls in a course",requiredData:["studentName","courseName","startDate","groupName"]},payment:{name:"Payment Notification",description:"Sent for payment confirmations or reminders",requiredData:["studentName","amount","courseName"],optionalData:["paymentMethod","transactionId","dueDate","isConfirmation"]},reminder:{name:"Class Reminder",description:"Sent before classes start",requiredData:["studentName","courseName","time"]},completion:{name:"Course Completion",description:"Sent when a student completes a course",requiredData:["studentName","courseName","completionDate"],optionalData:["nextLevel"]},attendance:{name:"Attendance Alert",description:"Sent to parents when student is absent",requiredData:["parentName","studentName","courseName"]}}});if("test"===t){(0,p.d)();let e=await y(),r=await x();return o.NextResponse.json({sms:e,email:r})}return o.NextResponse.json({error:"Invalid action"},{status:400})}catch(e){return console.error("Error in notifications GET:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,r){let t=[];for(let n of e){let e=null;switch(r){case"student":let s=await c.z.student.findUnique({where:{id:n},include:{user:!0}});s&&(e={id:s.id,name:s.user.name,phone:s.user.phone,email:s.user.email||void 0});break;case"teacher":let i=await c.z.teacher.findUnique({where:{id:n},include:{user:!0}});i&&(e={id:i.id,name:i.user.name,phone:i.user.phone,email:i.user.email||void 0});break;case"academic_manager":let a=await c.z.user.findFirst({where:{id:n,role:"ACADEMIC_MANAGER"}});a&&(e={id:a.id,name:a.name,phone:a.phone,email:a.email||void 0})}e&&t.push(e)}return t}async function y(){try{let e=process.env.SMS_PROVIDER,r=process.env.SMS_API_KEY;if(!e||!r)return{available:!1,error:"SMS service not configured (missing SMS_PROVIDER or SMS_API_KEY)"};return{available:!0,provider:e}}catch(e){return{available:!1,error:e instanceof Error?e.message:"Unknown error"}}}async function x(){try{let e=process.env.EMAIL_PROVIDER,r=process.env.EMAIL_USER,t=process.env.EMAIL_PASSWORD;if(!e||!r||!t)return{available:!1,error:"Email service not configured (missing EMAIL_PROVIDER, EMAIL_USER, or EMAIL_PASSWORD)"};return{available:!0,provider:e}}catch(e){return{available:!1,error:e instanceof Error?e.message:"Unknown error"}}}let v=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/notifications/route",pathname:"/api/notifications",filename:"route",bundlePath:"app/api/notifications/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\notifications\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:R,serverHooks:q}=v;function w(){return(0,a.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:R})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79551:e=>{e.exports=require("url")},79646:e=>{e.exports=require("child_process")},81630:e=>{e.exports=require("http")},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),n=r.X(0,[4243,580,5697,9526,2900],()=>t(41903));module.exports=n})();