(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6968],{809:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},2895:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(2115),n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),i=(e,r)=>{let t=(0,s.forwardRef)((t,i)=>{let{color:l="currentColor",size:o=24,strokeWidth:d=2,absoluteStrokeWidth:c,className:u="",children:m,...h}=t;return(0,s.createElement)("svg",{ref:i,...n,width:o,height:o,stroke:l,strokeWidth:c?24*Number(d)/Number(o):d,className:["lucide","lucide-".concat(a(e)),u].join(" "),...h},[...r.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(m)?m:[m]])});return t.displayName="".concat(e),t}},3999:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>l,cn:()=>a,r6:()=>o,vv:()=>i});var s=t(2596),n=t(9688);function a(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,s.$)(r))}function i(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function l(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)}function o(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}},5525:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>i,t:()=>a});var s=t(2115);function n(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function a(...e){return r=>{let t=!1,s=e.map(e=>{let s=n(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():n(e[r],null)}}}}function i(...e){return s.useCallback(a(...e),e)}},7168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>o});var s=t(5155),n=t(2115),a=t(9708),i=t(2085),l=t(3999);let o=(0,i.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef((e,r)=>{let{className:t,variant:n,size:i,asChild:d=!1,...c}=e,u=d?a.DX:"button";return(0,s.jsx)(u,{className:(0,l.cn)(o({variant:n,size:i,className:t})),ref:r,...c})});d.displayName="Button"},7550:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},8223:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var s=t(5155),n=t(5695),a=t(2108),i=t(7168),l=t(8482),o=t(809),d=t(5525),c=t(7550);let u=(0,t(2895).A)("Home",[["path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"y5dka4"}],["polyline",{points:"9 22 9 12 15 12 15 22",key:"e2us08"}]]);var m=t(6874),h=t.n(m);function f(){var e;let r=(0,n.useRouter)(),{data:t}=(0,a.useSession)();return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-orange-100 flex items-center justify-center px-4",children:(0,s.jsx)("div",{className:"w-full max-w-md",children:(0,s.jsxs)(l.Zp,{className:"border-red-200",children:[(0,s.jsxs)(l.aR,{className:"text-center",children:[(0,s.jsx)("div",{className:"mx-auto w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mb-4",children:(0,s.jsx)(o.A,{className:"h-8 w-8 text-red-600"})}),(0,s.jsx)(l.ZB,{className:"text-2xl text-red-800",children:"Access Denied"}),(0,s.jsx)(l.BT,{className:"text-red-600",children:"You don't have permission to access this page"})]}),(0,s.jsxs)(l.Wu,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-4",children:[(0,s.jsx)(d.A,{className:"h-6 w-6 text-red-600 mx-auto mb-2"}),(0,s.jsx)("p",{className:"text-sm text-red-700",children:"This page requires specific permissions that your account doesn't have."})]}),(null==t?void 0:t.user)&&(0,s.jsxs)("div",{className:"text-sm text-gray-600 mb-4",children:[(0,s.jsxs)("p",{children:["Logged in as: ",(0,s.jsx)("span",{className:"font-medium",children:t.user.name})]}),(0,s.jsxs)("p",{children:["Role: ",(0,s.jsx)("span",{className:"font-medium capitalize",children:null==(e=t.user.role)?void 0:e.toLowerCase()})]})]})]}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)(i.$,{onClick:()=>{r.back()},variant:"outline",className:"w-full",children:[(0,s.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Go Back"]}),(0,s.jsx)(h(),{href:(()=>{if(!(null==t?void 0:t.user))return"/dashboard";switch(t.user.role){case"STUDENT":return"/dashboard/student";case"TEACHER":return"/dashboard/teacher";case"RECEPTION":return"/dashboard/leads";case"CASHIER":return"/dashboard/payments";case"ACADEMIC_MANAGER":return"/dashboard/assessments";default:return"/dashboard"}})(),className:"block",children:(0,s.jsxs)(i.$,{className:"w-full",children:[(0,s.jsx)(u,{className:"h-4 w-4 mr-2"}),"Go to Dashboard"]})})]}),(0,s.jsx)("div",{className:"text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-500",children:["Need access to this page?"," ",(0,s.jsx)(h(),{href:"/contact",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Contact Administrator"})]})}),(0,s.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,s.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"Access Levels:"}),(0,s.jsxs)("div",{className:"text-xs text-gray-600 space-y-1",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Admin:"})," Full system access"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Manager:"})," Operations and reporting"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Teacher:"})," Classes and students"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Reception:"})," Leads and enrollments"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Cashier:"})," Payments and billing"]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"Student/Parent:"})," Personal dashboard"]})]})]})]})]})})})}},8482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>i,aR:()=>l});var s=t(5155),n=t(2115),a=t(3999);let i=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...n})});i.displayName="Card";let l=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",t),...n})});l.displayName="CardHeader";let o=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",t),...n})});o.displayName="CardTitle";let d=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",t),...n})});d.displayName="CardDescription";let c=n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...n})});c.displayName="CardContent",n.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,s.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter"},8584:(e,r,t)=>{Promise.resolve().then(t.bind(t,8223))},9708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>l,Dc:()=>d,TL:()=>i});var s=t(2115),n=t(6101),a=t(5155);function i(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...a}=e;if(s.isValidElement(t)){var i;let e,l,o=(i=t,(l=(e=Object.getOwnPropertyDescriptor(i.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(l=(e=Object.getOwnPropertyDescriptor(i,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref),d=function(e,r){let t={...r};for(let s in r){let n=e[s],a=r[s];/^on[A-Z]/.test(s)?n&&a?t[s]=(...e)=>{let r=a(...e);return n(...e),r}:n&&(t[s]=n):"style"===s?t[s]={...n,...a}:"className"===s&&(t[s]=[n,a].filter(Boolean).join(" "))}return{...e,...t}}(a,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,n.t)(r,o):o),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:n,...i}=e,l=s.Children.toArray(n),o=l.find(c);if(o){let e=o.props.children,n=l.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,a.jsx)(r,{...i,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,n):null})}return(0,a.jsx)(r,{...i,ref:t,children:n})});return t.displayName=`${e}.Slot`,t}var l=i("Slot"),o=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,6874,2108,8441,1684,7358],()=>r(8584)),_N_E=e.O()}]);