exports.id=3039,exports.ids=[3039],exports.modules={4432:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>n});var r=s(60687),a=s(82136);function n({children:e}){return(0,r.jsx)(a.<PERSON>,{children:e})}},5106:(e,t,s)=>{"use strict";s.d(t,{AuthProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\providers\\auth-provider.tsx","AuthProvider")},7356:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Sidebar() from the server but Sidebar is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\dashboard\\sidebar.tsx","Sidebar")},13910:(e,t,s)=>{"use strict";s.d(t,{QueryProvider:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\providers\\query-provider.tsx","QueryProvider")},18323:(e,t,s)=>{"use strict";s.d(t,{BranchProvider:()=>a});var r=s(12907);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call BranchProvider() from the server but BranchProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\contexts\\branch-context.tsx","BranchProvider");(0,r.registerClientReference)(function(){throw Error("Attempted to call useBranch() from the server but useBranch is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\contexts\\branch-context.tsx","useBranch"),(0,r.registerClientReference)(function(){throw Error("Attempted to call useBranchSafe() from the server but useBranchSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\contexts\\branch-context.tsx","useBranchSafe")},19675:(e,t,s)=>{Promise.resolve().then(s.bind(s,4432)),Promise.resolve().then(s.bind(s,80924))},24689:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\dashboard\\header.tsx","Header")},24934:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>l});var r=s(60687),a=s(43210),n=s(8730),o=s(24224),i=s(96241);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:s,asChild:a=!1,...o},d)=>{let c=a?n.DX:"button";return(0,r.jsx)(c,{className:(0,i.cn)(l({variant:t,size:s,className:e})),ref:d,...o})});d.displayName="Button"},31546:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>O});var r=s(60687),a=s(85814),n=s.n(a),o=s(16189),i=s(82136),l=s(96241),d=s(49625),c=s(23026),m=s(41312),u=s(93508),x=s(27351),h=s(90103),f=s(85778),p=s(74808),b=s(25541),g=s(40228),v=s(86561),y=s(10022),N=s(58887),j=s(48340),A=s(99891),w=s(58559),E=s(12187),C=s(84027),S=s(82080),T=s(28947),R=s(14952),M=s(78272),P=s(43210),D=s(59821);let I=[{name:"Dashboard",roles:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"],items:[{name:"Overview",href:"/dashboard",icon:d.A,roles:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]}]},{name:"Student Management",roles:["ADMIN","MANAGER","TEACHER","RECEPTION"],collapsible:!0,items:[{name:"Leads",href:"/dashboard/leads",icon:c.A,roles:["ADMIN","MANAGER","RECEPTION"]},{name:"Students",href:"/dashboard/students",icon:m.A,roles:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER"]}]},{name:"Academic Management",roles:["ADMIN","MANAGER","TEACHER"],collapsible:!0,items:[{name:"Teachers",href:"/dashboard/teachers",icon:u.A,roles:["ADMIN","MANAGER"]},{name:"Groups",href:"/dashboard/groups",icon:x.A,roles:["ADMIN","MANAGER","TEACHER"]},{name:"Attendance",href:"/dashboard/attendance",icon:u.A,roles:["MANAGER","TEACHER"]},{name:"Assessments",href:"/dashboard/assessments",icon:h.A,roles:["ADMIN","MANAGER","TEACHER","ACADEMIC_MANAGER"]}]},{name:"Financial Management",roles:["ADMIN","CASHIER"],collapsible:!0,items:[{name:"Payments",href:"/dashboard/payments",icon:f.A,roles:["ADMIN","CASHIER"]},{name:"Analytics",href:"/dashboard/analytics",icon:p.A,roles:["ADMIN"]}]},{name:"Student Progress",roles:["STUDENT"],items:[{name:"My Progress",href:"/dashboard/student/progress",icon:b.A,roles:["STUDENT"]},{name:"My Schedule",href:"/dashboard/student/schedule",icon:g.A,roles:["STUDENT"]},{name:"My Attendance",href:"/dashboard/student/attendance",icon:u.A,roles:["STUDENT"]},{name:"My Payments",href:"/dashboard/student/payments",icon:f.A,roles:["STUDENT"]},{name:"Certificates",href:"/dashboard/student/certificates",icon:v.A,roles:["STUDENT"]},{name:"Assignments",href:"/dashboard/student/assignments",icon:y.A,roles:["STUDENT"]}]},{name:"Communication",roles:["ADMIN","MANAGER","TEACHER","STUDENT","ACADEMIC_MANAGER"],items:[{name:"Messages",href:"/dashboard/communication",icon:N.A,roles:["ADMIN","MANAGER","TEACHER","STUDENT","PARENT"]},{name:"Announcements",href:"/dashboard/communication/announcements",icon:j.A,roles:["ADMIN","MANAGER","TEACHER","STUDENT","PARENT"]}]},{name:"Administration",roles:["ADMIN","MANAGER"],collapsible:!0,items:[{name:"Users",href:"/dashboard/users",icon:A.A,roles:["ADMIN"]},{name:"Activity Logs",href:"/dashboard/admin/activity-logs",icon:w.A,roles:["ADMIN"]},{name:"KPI Dashboard",href:"/dashboard/admin/kpis",icon:E.A,roles:["ADMIN","MANAGER"]},{name:"Teacher KPIs",href:"/dashboard/admin/teacher-kpis",icon:b.A,roles:["ADMIN","MANAGER"]},{name:"Settings",href:"/dashboard/settings",icon:C.A,roles:["ADMIN","MANAGER"]}]}],k=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800",U=e=>{let t=["A1","A2","B1","B2"],s=t.indexOf(e);return -1!==s&&s<t.length-1?t[s+1]:null};function O(){let e=(0,o.usePathname)(),{data:t}=(0,i.useSession)(),[s,a]=(0,P.useState)([]),d=t?.user?.role,c=t?.user?.name,m="STUDENT"===d?"B1":null,u=m?U(m):null,x=e=>{a(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},h=d?I.filter(e=>e.roles.includes(d)).map(e=>({...e,items:e.items.filter(e=>e.roles.includes(d))})).filter(e=>e.items.length>0):[];return(0,r.jsxs)("div",{className:"flex flex-col w-72 bg-white shadow-xl h-full border-r border-gray-100",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center h-20 px-6 gradient-primary",children:[(0,r.jsx)(S.A,{className:"h-8 w-8 text-white mr-3"}),(0,r.jsx)("span",{className:"text-xl font-bold text-white tracking-tight",children:"Innovative CRM"})]}),t?.user&&(0,r.jsxs)("div",{className:"px-6 py-5 border-b border-gray-100 bg-white",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full gradient-primary flex items-center justify-center shadow-md",children:(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:c?.charAt(0).toUpperCase()})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("p",{className:"text-sm font-semibold text-gray-900 truncate",children:c}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-0.5",children:[(0,r.jsx)("p",{className:"text-xs text-gray-500 capitalize font-medium",children:d?.toLowerCase()}),m&&(0,r.jsxs)(D.E,{className:(0,l.cn)("text-xs font-medium",k(m)),children:["Level ",m]})]})]})]}),m&&u&&(0,r.jsxs)("div",{className:"mt-4 p-3 bg-white rounded-lg border border-gray-100 shadow-sm",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,r.jsxs)("span",{className:"text-gray-700 font-medium",children:["Progress to ",u]}),(0,r.jsx)(T.A,{className:"h-3.5 w-3.5 text-blue-600"})]}),(0,r.jsx)("div",{className:"mt-2 w-full bg-gray-100 rounded-full h-2",children:(0,r.jsx)("div",{className:"gradient-primary h-2 rounded-full",style:{width:"65%"}})}),(0,r.jsxs)("p",{className:"text-xs text-gray-600 font-medium mt-1.5 flex items-center",children:[(0,r.jsx)(b.A,{className:"h-3 w-3 mr-1 text-blue-600"}),"65% Complete"]})]})]}),(0,r.jsx)("nav",{className:"flex-1 px-6 py-6 space-y-2 overflow-y-auto",children:h.map(t=>{let a=s.includes(t.name),o=!t.collapsible||!a;return(0,r.jsxs)("div",{className:"space-y-1.5",children:[(0,r.jsx)("div",{className:"flex items-center justify-between",children:t.collapsible?(0,r.jsxs)("button",{onClick:()=>x(t.name),className:"flex items-center w-full px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider hover:text-gray-700 transition-all duration-200 rounded-lg hover:bg-gray-50",children:[(0,r.jsx)("span",{className:"flex-1 text-left",children:t.name}),a?(0,r.jsx)(R.A,{className:"h-3.5 w-3.5 text-gray-400"}):(0,r.jsx)(M.A,{className:"h-3.5 w-3.5 text-gray-400"})]}):(0,r.jsx)("h3",{className:"px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider",children:t.name})}),o&&(0,r.jsx)("div",{className:"space-y-1",children:t.items.map(t=>{let s=e===t.href;return(0,r.jsxs)(n(),{href:t.href,className:(0,l.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group",s?"bg-blue-50 text-blue-700 border-r-4 border-blue-600 shadow-sm":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm"),children:[(0,r.jsx)(t.icon,{className:(0,l.cn)("mr-3 h-5 w-5 transition-colors duration-200",s?"text-blue-600":"text-gray-400 group-hover:text-gray-600")}),(0,r.jsx)("span",{className:"truncate",children:t.name})]},t.name)})}),t!==h[h.length-1]&&(0,r.jsx)("div",{className:"border-b border-gray-100 my-2"})]},t.name)})})]})}},40406:(e,t,s)=>{Promise.resolve().then(s.bind(s,24689)),Promise.resolve().then(s.bind(s,7356)),Promise.resolve().then(s.bind(s,52358)),Promise.resolve().then(s.bind(s,18323))},45086:(e,t,s)=>{"use strict";s.d(t,{Header:()=>I});var r=s(60687),a=s(43210),n=s(82136),o=s(24934),i=s(26312),l=s(14952),d=s(13964),c=s(65822),m=s(96241);let u=i.bL,x=i.l9;i.YJ,i.ZL,i.Pb,i.z6,a.forwardRef(({className:e,inset:t,children:s,...a},n)=>(0,r.jsxs)(i.ZP,{ref:n,className:(0,m.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",t&&"pl-8",e),...a,children:[s,(0,r.jsx)(l.A,{className:"ml-auto h-4 w-4"})]})).displayName=i.ZP.displayName,a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.G5,{ref:s,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})).displayName=i.G5.displayName;let h=a.forwardRef(({className:e,sideOffset:t=4,...s},a)=>(0,r.jsx)(i.ZL,{children:(0,r.jsx)(i.UC,{ref:a,sideOffset:t,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));h.displayName=i.UC.displayName;let f=a.forwardRef(({className:e,inset:t,...s},a)=>(0,r.jsx)(i.q7,{ref:a,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t&&"pl-8",e),...s}));f.displayName=i.q7.displayName,a.forwardRef(({className:e,children:t,checked:s,...a},n)=>(0,r.jsxs)(i.H_,{ref:n,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:s,...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),t]})).displayName=i.H_.displayName,a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(i.hN,{ref:a,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(c.A,{className:"h-2 w-2 fill-current"})})}),t]})).displayName=i.hN.displayName;let p=a.forwardRef(({className:e,inset:t,...s},a)=>(0,r.jsx)(i.JU,{ref:a,className:(0,m.cn)("px-2 py-1.5 text-sm font-semibold",t&&"pl-8",e),...s}));p.displayName=i.JU.displayName;let b=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(i.wv,{ref:s,className:(0,m.cn)("-mx-1 my-1 h-px bg-muted",e),...t}));b.displayName=i.wv.displayName;var g=s(59821),v=s(17313),y=s(78272),N=s(96545);function j(){let{currentBranch:e,branches:t,switchBranch:s,isLoading:n}=(0,N.O)(),[i,l]=(0,a.useState)(!1);return n?(0,r.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-md",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"text-sm",children:"Loading..."})]}):(0,r.jsxs)(u,{open:i,onOpenChange:l,children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"outline",className:"flex items-center gap-3 min-w-[280px] justify-between bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white hover:shadow-sm transition-all duration-200 rounded-xl py-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-lg bg-blue-50 flex items-center justify-center",children:(0,r.jsx)(v.A,{className:"h-4 w-4 text-blue-600"})}),(0,r.jsxs)("div",{className:"flex flex-col items-start",children:[(0,r.jsx)("span",{className:"font-semibold text-gray-900 text-sm",children:e.name}),(0,r.jsx)(g.E,{variant:"secondary",className:"text-xs font-medium bg-green-100 text-green-700",children:"Active"})]})]}),(0,r.jsx)(y.A,{className:"h-4 w-4 text-gray-400"})]})}),(0,r.jsxs)(h,{align:"start",className:"w-[280px] rounded-xl border-gray-100 shadow-lg",children:[(0,r.jsx)(p,{className:"text-sm font-semibold text-gray-900 px-4 py-3",children:"Switch Branch"}),(0,r.jsx)(b,{className:"bg-gray-100"}),t.map(t=>(0,r.jsx)(f,{onClick:()=>{s(t.id),l(!1)},className:"flex items-center justify-between cursor-pointer px-4 py-3 hover:bg-gray-50 transition-colors duration-200",children:(0,r.jsxs)("div",{className:"flex flex-col",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"h-6 w-6 rounded-md bg-gray-100 flex items-center justify-center",children:(0,r.jsx)(v.A,{className:"h-3.5 w-3.5 text-gray-600"})}),(0,r.jsx)("span",{className:"font-medium text-gray-900",children:t.name}),e.id===t.id&&(0,r.jsx)(d.A,{className:"h-4 w-4 text-green-600"})]}),t.address&&(0,r.jsx)("span",{className:"text-xs text-gray-500 ml-9 mt-0.5",children:t.address})]})},t.id))]})]})}var A=s(69327),w=s(23689),E=s(63851),C=s(11860),S=s(96882),T=s(97051),R=s(15814);function M(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(0),[i,l]=(0,a.useState)(!1),[d,c]=(0,a.useState)(!1),m=e=>{switch(e){case"success":return(0,r.jsx)(w.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,r.jsx)(E.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,r.jsx)(C.A,{className:"h-4 w-4 text-red-500"});default:return(0,r.jsx)(S.A,{className:"h-4 w-4 text-blue-500"})}},v=e=>{switch(e){case"urgent":return"bg-red-100 text-red-800";case"high":return"bg-orange-100 text-orange-800";case"medium":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=async e=>{t(t=>t.map(t=>t.id===e?{...t,read:!0}:t)),n(e=>Math.max(0,e-1))},N=async()=>{c(!0);try{t(e=>e.map(e=>({...e,read:!0}))),n(0)}catch(e){console.error("Failed to mark all as read:",e)}finally{c(!1)}},j=e=>{e.read||y(e.id),e.actionUrl&&(window.location.href=e.actionUrl),l(!1)};return(0,r.jsxs)(u,{open:i,onOpenChange:l,children:[(0,r.jsx)(x,{asChild:!0,children:(0,r.jsxs)(o.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,r.jsx)(T.A,{className:"h-5 w-5"}),s>0&&(0,r.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:s>9?"9+":s})]})}),(0,r.jsxs)(h,{align:"end",className:"w-80",children:[(0,r.jsxs)(p,{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{children:"Notifications"}),s>0&&(0,r.jsx)(o.$,{variant:"ghost",size:"sm",onClick:N,disabled:d,className:"text-xs",children:"Mark all read"})]}),(0,r.jsx)(b,{}),(0,r.jsx)(A.F,{className:"h-96",children:0===e.length?(0,r.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,r.jsx)(T.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No notifications"})]}):(0,r.jsx)("div",{className:"space-y-1",children:e.map(e=>(0,r.jsx)(f,{className:`p-3 cursor-pointer ${!e.read?"bg-blue-50":""}`,onClick:()=>j(e),children:(0,r.jsxs)("div",{className:"flex items-start space-x-3 w-full",children:[(0,r.jsx)("div",{className:"flex-shrink-0 mt-1",children:m(e.type)}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),!e.read&&(0,r.jsx)("div",{className:"h-2 w-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.message}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-xs text-gray-400",children:(0,R.A)(new Date(e.createdAt),{addSuffix:!0})}),"low"!==e.priority&&(0,r.jsx)(g.E,{className:`text-xs ${v(e.priority)}`,children:e.priority})]})]})]})},e.id))})}),e.length>0&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(b,{}),(0,r.jsx)(f,{className:"text-center text-blue-600 hover:text-blue-800",children:(0,r.jsx)(o.$,{variant:"ghost",size:"sm",className:"w-full",children:"View all notifications"})})]})]})]})}var P=s(99270),D=s(40083);function I(){let{data:e}=(0,n.useSession)(),[t,s]=(0,a.useState)(!1);return(0,r.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-between px-8 py-5",children:[(0,r.jsxs)("div",{className:"flex items-center flex-1 gap-6",children:[(0,r.jsx)(j,{}),(0,r.jsxs)("div",{className:"relative max-w-lg w-full",children:[(0,r.jsx)(P.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,r.jsx)("input",{type:"text",placeholder:"Search students, leads, groups...",className:"w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white transition-all duration-200 text-sm font-medium"})]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(M,{}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(o.$,{variant:"ghost",onClick:()=>s(!t),className:"flex items-center space-x-3 hover:bg-gray-100 rounded-xl px-4 py-2",children:[(0,r.jsx)("div",{className:"h-8 w-8 rounded-full gradient-primary flex items-center justify-center",children:(0,r.jsx)("span",{className:"text-sm font-medium text-white",children:e?.user?.name?.charAt(0).toUpperCase()||"U"})}),(0,r.jsx)("span",{className:"hidden md:block font-medium text-gray-700",children:e?.user?.name||"User"})]}),t&&(0,r.jsxs)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg py-2 z-50 border border-gray-100",children:[(0,r.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-700 border-b border-gray-100",children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900",children:e?.user?.name}),(0,r.jsx)("div",{className:"text-gray-500 capitalize",children:e?.user?.role?.toLowerCase()})]}),(0,r.jsxs)("button",{onClick:()=>(0,n.signOut)(),className:"flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",children:[(0,r.jsx)(D.A,{className:"mr-3 h-4 w-4 text-gray-500"}),"Sign out"]})]})]})]})]})})}},48877:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},52358:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\ui\\toaster.tsx","Toaster")},54019:(e,t,s)=>{Promise.resolve().then(s.bind(s,5106)),Promise.resolve().then(s.bind(s,13910))},58014:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d,metadata:()=>l});var r=s(37413),a=s(61421),n=s.n(a),o=s(5106),i=s(13910);s(82704);let l={title:"Innovative Centre CRM",description:"Customer Relationship Management System for Innovative Centre"};function d({children:e}){return(0,r.jsx)("html",{lang:"en",children:(0,r.jsx)("body",{className:n().className,children:(0,r.jsx)(i.QueryProvider,{children:(0,r.jsx)(o.AuthProvider,{children:e})})})})}},59821:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(60687);s(43210);var a=s(24224),n=s(96241);let o=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i({className:e,variant:t,...s}){return(0,r.jsx)("div",{className:(0,n.cn)(o({variant:t}),e),...s})}},69327:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(60687),a=s(43210),n=s(68123),o=s(96241);let i=a.forwardRef(({className:e,children:t,...s},a)=>(0,r.jsxs)(n.bL,{ref:a,className:(0,o.cn)("relative overflow-hidden",e),...s,children:[(0,r.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:t}),(0,r.jsx)(l,{}),(0,r.jsx)(n.OK,{})]}));i.displayName=n.bL.displayName;let l=a.forwardRef(({className:e,orientation:t="vertical",...s},a)=>(0,r.jsx)(n.VM,{ref:a,orientation:t,className:(0,o.cn)("flex touch-none select-none transition-colors","vertical"===t&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===t&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",e),...s,children:(0,r.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})}));l.displayName=n.VM.displayName},69794:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>b});var r=s(60687),a=s(43210),n=s(47313),o=s(24224),i=s(11860),l=s(96241);let d=n.Kq,c=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.LM,{ref:s,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));c.displayName=n.LM.displayName;let m=(0,o.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground",success:"border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900 dark:text-green-200",warning:"border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",info:"border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900 dark:text-blue-200"}},defaultVariants:{variant:"default"}}),u=a.forwardRef(({className:e,variant:t,...s},a)=>(0,r.jsx)(n.bL,{ref:a,className:(0,l.cn)(m({variant:t}),e),...s}));u.displayName=n.bL.displayName,a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.rc,{ref:s,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=n.rc.displayName;let x=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.bm,{ref:s,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,r.jsx)(i.A,{className:"h-4 w-4"})}));x.displayName=n.bm.displayName;let h=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.hE,{ref:s,className:(0,l.cn)("text-sm font-semibold",e),...t}));h.displayName=n.hE.displayName;let f=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(n.VY,{ref:s,className:(0,l.cn)("text-sm opacity-90",e),...t}));f.displayName=n.VY.displayName;var p=s(71702);function b(){let{toasts:e}=(0,p.dj)();return(0,r.jsxs)(d,{children:[e.map(function({id:e,title:t,description:s,action:a,...n}){return(0,r.jsxs)(u,{...n,children:[(0,r.jsxs)("div",{className:"grid gap-1",children:[t&&(0,r.jsx)(h,{children:t}),s&&(0,r.jsx)(f,{children:s})]}),a,(0,r.jsx)(x,{})]},e)}),(0,r.jsx)(c,{})]})}},71702:(e,t,s)=>{"use strict";s.d(t,{dj:()=>u});var r=s(43210);let a=0,n=new Map,o=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},5e3);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=i(d,e),l.forEach(e=>{e(d)})}function m({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),s=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||s()}}}),{id:t,dismiss:s,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,t]=r.useState(d);return r.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},71934:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>l});var r=s(37413),a=s(7356),n=s(24689),o=s(52358),i=s(18323);function l({children:e}){return(0,r.jsx)(i.BranchProvider,{children:(0,r.jsxs)("div",{className:"flex h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50",children:[(0,r.jsx)(a.Sidebar,{}),(0,r.jsxs)("div",{className:"flex-1 flex flex-col overflow-hidden",children:[(0,r.jsx)(n.Header,{}),(0,r.jsx)("main",{className:"flex-1 overflow-x-hidden overflow-y-auto bg-transparent p-6 lg:p-8",children:(0,r.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,r.jsx)("div",{className:"fade-in",children:e})})})]}),(0,r.jsx)(o.Toaster,{})]})})}},80924:(e,t,s)=>{"use strict";s.d(t,{QueryProvider:()=>i});var r=s(60687),a=s(92314),n=s(8693),o=s(43210);function i({children:e}){let[t]=(0,o.useState)(()=>new a.E({defaultOptions:{queries:{staleTime:6e4}}}));return(0,r.jsx)(n.Ht,{client:t,children:e})}},82704:()=>{},87262:(e,t,s)=>{Promise.resolve().then(s.bind(s,45086)),Promise.resolve().then(s.bind(s,31546)),Promise.resolve().then(s.bind(s,69794)),Promise.resolve().then(s.bind(s,96545))},96141:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},96241:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>n,r6:()=>l,vv:()=>o});var r=s(49384),a=s(82348);function n(...e){return(0,a.QP)((0,r.$)(e))}function o(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function l(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},96545:(e,t,s)=>{"use strict";s.d(t,{BranchProvider:()=>i,O:()=>l,Z:()=>d});var r=s(60687),a=s(43210);let n=(0,a.createContext)(void 0),o=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function i({children:e}){let[t,s]=(0,a.useState)(o[0]),[i]=(0,a.useState)(o),[l,d]=(0,a.useState)(!0);return(0,r.jsx)(n.Provider,{value:{currentBranch:t,branches:i,switchBranch:e=>{let t=i.find(t=>t.id===e);t&&(s(t),localStorage.setItem("selectedBranch",e))},isLoading:l},children:e})}function l(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,a.useContext)(n)||null}}};