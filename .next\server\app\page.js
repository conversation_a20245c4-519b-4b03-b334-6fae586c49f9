(()=>{var e={};e.id=8974,e.ids=[8974],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4432:(e,r,s)=>{"use strict";s.d(r,{AuthProvider:()=>i});var t=s(60687),n=s(82136);function i({children:e}){return(0,t.jsx)(n.SessionProvider,{children:e})}},4536:(e,r,s)=>{let{createProxy:t}=s(39844);e.exports=t("C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\node_modules\\next\\dist\\client\\app-dir\\link.js")},5106:(e,r,s)=>{"use strict";s.d(r,{AuthProvider:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\providers\\auth-provider.tsx","AuthProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13910:(e,r,s)=>{"use strict";s.d(r,{QueryProvider:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\providers\\query-provider.tsx","QueryProvider")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19675:(e,r,s)=>{Promise.resolve().then(s.bind(s,4432)),Promise.resolve().then(s.bind(s,80924))},19863:(e,r,s)=>{Promise.resolve().then(s.bind(s,38148)),Promise.resolve().then(s.t.bind(s,4536,23))},23469:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>v});var t=s(37413),n=s(4536),i=s.n(n),a=s(94592),o=s(51358),l=s(38148),d=s(75234),c=s(18618);let m=(0,c.A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);var h=s(61227),u=s(49046),x=s(53148);let p=(0,c.A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]);function v(){return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100",children:[(0,t.jsx)("header",{className:"bg-white shadow-sm",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center py-6",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(d.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:"Innovative Centre"})]}),(0,t.jsx)(i(),{href:"/auth/signin",children:(0,t.jsx)(a.$,{variant:"outline",children:"Staff Login"})})]})})}),(0,t.jsx)("section",{className:"py-20",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-4xl font-bold text-gray-900 mb-6",children:"Master English at Uzbekistan's Leading Language Center"}),(0,t.jsx)("p",{className:"text-xl text-gray-600 mb-8",children:"Join 4,000+ students across our 2 branches. From General English to IELTS preparation, we offer comprehensive courses for all ages and levels."}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 mb-8",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(m,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"text-gray-700",children:"4,000+ Students"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(h.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"text-gray-700",children:"Expert Teachers"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"text-gray-700",children:"2 Branches"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-2"}),(0,t.jsx)("span",{className:"text-gray-700",children:"Flexible Schedule"})]})]})]}),(0,t.jsx)("div",{children:(0,t.jsxs)(o.Zp,{className:"w-full max-w-md mx-auto",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsx)(o.ZB,{children:"Start Your English Journey"}),(0,t.jsx)(o.BT,{children:"Fill out the form below and we'll contact you within 24 hours"})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsx)(l.LeadForm,{})})]})})]})})}),(0,t.jsx)("section",{className:"py-16 bg-white",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Our Courses"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Choose from our comprehensive range of English programs"})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",children:f.map((e,r)=>(0,t.jsxs)(o.Zp,{className:"hover:shadow-lg transition-shadow",children:[(0,t.jsxs)(o.aR,{children:[(0,t.jsx)(o.ZB,{className:"text-xl",children:e.name}),(0,t.jsx)(o.BT,{children:e.description})]}),(0,t.jsx)(o.Wu,{children:(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Duration: ",e.duration]}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Level: ",e.level]}),(0,t.jsx)("p",{className:"text-lg font-semibold text-blue-600",children:e.price})]})})]},r))})]})}),(0,t.jsx)("section",{className:"py-16 bg-gray-50",children:(0,t.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,t.jsxs)("div",{className:"text-center mb-12",children:[(0,t.jsx)("h3",{className:"text-3xl font-bold text-gray-900 mb-4",children:"Contact Us"}),(0,t.jsx)("p",{className:"text-xl text-gray-600",children:"Visit our branches or get in touch"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-8",children:[(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{children:"Main Branch"})}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,t.jsx)("span",{children:"Tashkent, Uzbekistan"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,t.jsx)("span",{children:"+998 90 123 45 67"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,t.jsx)("span",{children:"Mon-Sat: 8:00 AM - 8:00 PM"})]})]})]}),(0,t.jsxs)(o.Zp,{children:[(0,t.jsx)(o.aR,{children:(0,t.jsx)(o.ZB,{children:"Second Branch"})}),(0,t.jsxs)(o.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,t.jsx)("span",{children:"Tashkent, Uzbekistan"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,t.jsx)("span",{children:"+998 90 123 45 68"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 text-blue-600 mr-3"}),(0,t.jsx)("span",{children:"Mon-Sat: 8:00 AM - 8:00 PM"})]})]})]})]})]})}),(0,t.jsx)("footer",{className:"bg-gray-900 text-white py-8",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsx)("div",{className:"text-center",children:(0,t.jsx)("p",{children:"\xa9 2024 Innovative Centre. All rights reserved."})})})})]})}let f=[{name:"General English",description:"Complete English course from A1 to C2 levels",duration:"3-4 months per level",level:"A1 - C2",price:"From 500,000 UZS/month"},{name:"IELTS Preparation",description:"Intensive IELTS preparation for all band scores",duration:"2-3 months",level:"5.5 - 7.0+",price:"From 800,000 UZS/month"},{name:"SAT Preparation",description:"Comprehensive SAT preparation course",duration:"3-4 months",level:"Intermediate+",price:"From 700,000 UZS/month"},{name:"Kids English",description:"Fun and engaging English for children",duration:"6 months",level:"Ages 6-12",price:"From 400,000 UZS/month"},{name:"Business English",description:"Professional English for business communication",duration:"2-3 months",level:"B1+",price:"From 600,000 UZS/month"},{name:"Math Courses",description:"Mathematics courses in English",duration:"4-6 months",level:"All levels",price:"From 450,000 UZS/month"}]},24934:(e,r,s)=>{"use strict";s.d(r,{$:()=>d,r:()=>l});var t=s(60687),n=s(43210),i=s(8730),a=s(24224),o=s(96241);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:r,size:s,asChild:n=!1,...a},d)=>{let c=n?i.DX:"button";return(0,t.jsx)(c,{className:(0,o.cn)(l({variant:r,size:s,className:e})),ref:d,...a})});d.displayName="Button"},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},38148:(e,r,s)=>{"use strict";s.d(r,{LeadForm:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call LeadForm() from the server but LeadForm is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\components\\forms\\lead-form.tsx","LeadForm")},39390:(e,r,s)=>{"use strict";s.d(r,{J:()=>d});var t=s(60687),n=s(43210),i=s(78148),a=s(24224),o=s(96241);let l=(0,a.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=n.forwardRef(({className:e,...r},s)=>(0,t.jsx)(i.b,{ref:s,className:(0,o.cn)(l(),e),...r}));d.displayName=i.b.displayName},48877:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,16444,23)),Promise.resolve().then(s.t.bind(s,16042,23)),Promise.resolve().then(s.t.bind(s,88170,23)),Promise.resolve().then(s.t.bind(s,49477,23)),Promise.resolve().then(s.t.bind(s,29345,23)),Promise.resolve().then(s.t.bind(s,12089,23)),Promise.resolve().then(s.t.bind(s,46577,23)),Promise.resolve().then(s.t.bind(s,31307,23))},49046:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(18618).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},51358:(e,r,s)=>{"use strict";s.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>l,Zp:()=>a,aR:()=>o});var t=s(37413),n=s(61120),i=s(66819);let a=n.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...r}));a.displayName="Card";let o=n.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...r}));o.displayName="CardHeader";let l=n.forwardRef(({className:e,...r},s)=>(0,t.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...r}));l.displayName="CardTitle";let d=n.forwardRef(({className:e,...r},s)=>(0,t.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...r}));d.displayName="CardDescription";let c=n.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...r}));c.displayName="CardContent",n.forwardRef(({className:e,...r},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...r})).displayName="CardFooter"},54019:(e,r,s)=>{Promise.resolve().then(s.bind(s,5106)),Promise.resolve().then(s.bind(s,13910))},58014:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>d,metadata:()=>l});var t=s(37413),n=s(61421),i=s.n(n),a=s(5106),o=s(13910);s(82704);let l={title:"Innovative Centre CRM",description:"Customer Relationship Management System for Innovative Centre"};function d({children:e}){return(0,t.jsx)("html",{lang:"en",children:(0,t.jsx)("body",{className:i().className,children:(0,t.jsx)(o.QueryProvider,{children:(0,t.jsx)(a.AuthProvider,{children:e})})})})}},61227:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(18618).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66819:(e,r,s)=>{"use strict";s.d(r,{cn:()=>i});var t=s(75986),n=s(8974);function i(...e){return(0,n.QP)((0,t.$)(e))}},68988:(e,r,s)=>{"use strict";s.d(r,{p:()=>a});var t=s(60687),n=s(43210),i=s(96241);let a=n.forwardRef(({className:e,type:r,...s},n)=>(0,t.jsx)("input",{type:r,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:n,...s}));a.displayName="Input"},73007:(e,r,s)=>{Promise.resolve().then(s.bind(s,89130)),Promise.resolve().then(s.t.bind(s,85814,23))},75234:(e,r,s)=>{"use strict";s.d(r,{A:()=>t});let t=(0,s(18618).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},79709:(e,r,s)=>{"use strict";s.r(r),s.d(r,{GlobalError:()=>a.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>h,tree:()=>d});var t=s(65239),n=s(48088),i=s(88170),a=s.n(i),o=s(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(r,l);let d=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,23469)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}],c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:n.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},80924:(e,r,s)=>{"use strict";s.d(r,{QueryProvider:()=>o});var t=s(60687),n=s(92314),i=s(8693),a=s(43210);function o({children:e}){let[r]=(0,a.useState)(()=>new n.E({defaultOptions:{queries:{staleTime:6e4}}}));return(0,t.jsx)(i.Ht,{client:r,children:e})}},82704:()=>{},89130:(e,r,s)=>{"use strict";s.d(r,{LeadForm:()=>u});var t=s(60687),n=s(43210),i=s(96545),a=s(27605),o=s(63442),l=s(9275),d=s(24934),c=s(68988),m=s(39390);let h=l.Ik({name:l.Yj().min(2,"Name must be at least 2 characters"),phone:l.Yj().min(9,"Please enter a valid phone number"),coursePreference:l.Yj().min(1,"Please select a course")});function u(){let e=(0,i.Z)(),r=e?.currentBranch||{id:"main"},[s,l]=(0,n.useState)(!1),[u,x]=(0,n.useState)(!1),{register:p,handleSubmit:v,formState:{errors:f},reset:g}=(0,a.mN)({resolver:(0,o.u)(h)}),b=async e=>{l(!0);try{if((await fetch("/api/leads",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...e,branch:r.id})})).ok)x(!0),g();else throw Error("Failed to submit form")}catch(e){console.error("Error submitting form:",e),alert("There was an error submitting your information. Please try again.")}finally{l(!1)}};return u?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"text-green-600 text-lg font-semibold mb-2",children:"Thank you for your interest!"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"We'll contact you within 24 hours to discuss your English learning goals."}),(0,t.jsx)(d.$,{onClick:()=>x(!1),variant:"outline",children:"Submit Another Request"})]}):(0,t.jsxs)("form",{onSubmit:v(b),className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"name",children:"Full Name"}),(0,t.jsx)(c.p,{id:"name",...p("name"),placeholder:"Enter your full name",className:"mt-1"}),f.name&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.name.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"phone",children:"Phone Number"}),(0,t.jsx)(c.p,{id:"phone",...p("phone"),placeholder:"+998 90 123 45 67",className:"mt-1"}),f.phone&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.phone.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"coursePreference",children:"Course Interest"}),(0,t.jsxs)("select",{id:"coursePreference",...p("coursePreference"),className:"mt-1 block w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",children:[(0,t.jsx)("option",{value:"",children:"Select a course"}),(0,t.jsx)("option",{value:"General English",children:"General English"}),(0,t.jsx)("option",{value:"IELTS Preparation",children:"IELTS Preparation"}),(0,t.jsx)("option",{value:"SAT Preparation",children:"SAT Preparation"}),(0,t.jsx)("option",{value:"Kids English",children:"Kids English"}),(0,t.jsx)("option",{value:"Business English",children:"Business English"}),(0,t.jsx)("option",{value:"Math Courses",children:"Math Courses"})]}),f.coursePreference&&(0,t.jsx)("p",{className:"text-red-500 text-sm mt-1",children:f.coursePreference.message})]}),(0,t.jsx)(d.$,{type:"submit",className:"w-full",disabled:s,children:s?"Submitting...":"Get Free Consultation"})]})}},94592:(e,r,s)=>{"use strict";s.d(r,{$:()=>d});var t=s(37413),n=s(61120),i=s(70403),a=s(50662),o=s(66819);let l=(0,a.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:r,size:s,asChild:n=!1,...a},d)=>{let c=n?i.DX:"button";return(0,t.jsx)(c,{className:(0,o.cn)(l({variant:r,size:s,className:e})),ref:d,...a})});d.displayName="Button"},96141:(e,r,s)=>{Promise.resolve().then(s.t.bind(s,86346,23)),Promise.resolve().then(s.t.bind(s,27924,23)),Promise.resolve().then(s.t.bind(s,35656,23)),Promise.resolve().then(s.t.bind(s,40099,23)),Promise.resolve().then(s.t.bind(s,38243,23)),Promise.resolve().then(s.t.bind(s,28827,23)),Promise.resolve().then(s.t.bind(s,62763,23)),Promise.resolve().then(s.t.bind(s,97173,23))},96241:(e,r,s)=>{"use strict";s.d(r,{Yq:()=>o,cn:()=>i,r6:()=>l,vv:()=>a});var t=s(49384),n=s(82348);function i(...e){return(0,n.QP)((0,t.$)(e))}function a(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function o(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)}function l(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}},96545:(e,r,s)=>{"use strict";s.d(r,{BranchProvider:()=>o,O:()=>l,Z:()=>d});var t=s(60687),n=s(43210);let i=(0,n.createContext)(void 0),a=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function o({children:e}){let[r,s]=(0,n.useState)(a[0]),[o]=(0,n.useState)(a),[l,d]=(0,n.useState)(!0);return(0,t.jsx)(i.Provider,{value:{currentBranch:r,branches:o,switchBranch:e=>{let r=o.find(r=>r.id===e);r&&(s(r),localStorage.setItem("selectedBranch",e))},isLoading:l},children:e})}function l(){let e=(0,n.useContext)(i);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,n.useContext)(i)||null}}};var r=require("../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,7615,2918,7825,7144],()=>s(79709));module.exports=t})();