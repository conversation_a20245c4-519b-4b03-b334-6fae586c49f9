(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8091],{311:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2178:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]])},2318:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2525:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2714:(e,t,s)=>{"use strict";s.d(t,{J:()=>c});var a=s(5155),r=s(2115),l=s(968),n=s(2085),d=s(3999);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.b,{ref:t,className:(0,d.cn)(i(),s),...r})});c.displayName=l.b.displayName},2915:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3374:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>M});var a=s(5155),r=s(2115),l=s(8482),n=s(7168),d=s(8145),i=s(9852),c=s(5784),o=s(8524),u=s(9840),m=s(3999),x=s(2915),h=s(311),f=s(2178),p=s(4616),g=s(7924),j=s(7949),y=s(1007),v=s(4186),N=s(9074),b=s(4621),w=s(2525),A=s(2177),E=s(221),k=s(1153),C=s(2714),S=s(9026),D=s(2318),T=s(7624),R=s(7705);let P=k.Ik({studentId:k.Yj().min(1,"Student is required"),groupId:k.Yj().min(1,"Group is required"),status:k.k5(["ACTIVE","COMPLETED","DROPPED","SUSPENDED"]).default("ACTIVE"),startDate:k.Yj().min(1,"Start date is required"),endDate:k.Yj().optional()}),I=[{value:"ACTIVE",label:"Active",color:"bg-green-100 text-green-800"},{value:"COMPLETED",label:"Completed",color:"bg-blue-100 text-blue-800"},{value:"DROPPED",label:"Dropped",color:"bg-red-100 text-red-800"},{value:"SUSPENDED",label:"Suspended",color:"bg-yellow-100 text-yellow-800"}],L=function(e){let{initialData:t,onSubmit:s,onCancel:o,isEditing:u=!1,preselectedStudentId:m,preselectedGroupId:x}=e,{currentBranch:h}=(0,R.O)(),[f,p]=(0,r.useState)(!1),[g,v]=(0,r.useState)(null),[b,w]=(0,r.useState)([]),[k,L]=(0,r.useState)([]),[M,F]=(0,r.useState)([]),[z,U]=(0,r.useState)(null),[V,O]=(0,r.useState)(null),{register:Z,handleSubmit:q,setValue:B,watch:_,formState:{errors:J}}=(0,A.mN)({resolver:(0,E.u)(P),defaultValues:{studentId:m||(null==t?void 0:t.studentId)||"",groupId:x||(null==t?void 0:t.groupId)||"",status:(null==t?void 0:t.status)||"ACTIVE",startDate:(null==t?void 0:t.startDate)||new Date().toISOString().split("T")[0],endDate:(null==t?void 0:t.endDate)||""}}),H=_("studentId"),W=_("groupId"),G=_("status");(0,r.useEffect)(()=>{(null==h?void 0:h.id)&&(Y(),$())},[null==h?void 0:h.id]),(0,r.useEffect)(()=>{if(H){let e=b.find(e=>e.id===H);U(e||null),e&&F(k.filter(t=>t.isActive&&t.branch===e.branch&&t.course.level===e.level&&t._count.enrollments<t.capacity))}else F(k.filter(e=>e.isActive))},[H,b,k]),(0,r.useEffect)(()=>{if(W){let e=k.find(e=>e.id===W);O(e||null),e&&!_("endDate")&&B("endDate",e.endDate)}},[W,k,B,_]);let Y=async()=>{if(null==h?void 0:h.id)try{let e=await fetch("/api/students?branch=".concat(h.id)),t=await e.json();w(t.students||[])}catch(e){console.error("Error fetching students:",e)}},$=async()=>{if(null==h?void 0:h.id)try{let e=await fetch("/api/groups?branch=".concat(h.id)),t=await e.json();L(t.groups||[])}catch(e){console.error("Error fetching groups:",e)}},X=async e=>{p(!0),v(null);try{await s(e)}catch(e){v(e instanceof Error?e.message:"An error occurred")}finally{p(!1)}},Q=!!(V&&V._count.enrollments>=V.capacity),K=null==z?void 0:z.enrollments.some(e=>"ACTIVE"===e.status&&e.group.course.level===(null==V?void 0:V.course.level));return(0,a.jsxs)(l.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{className:"flex items-center",children:[(0,a.jsx)(D.A,{className:"h-5 w-5 mr-2"}),u?"Edit Enrollment":"New Student Enrollment"]}),(0,a.jsx)(l.BT,{children:u?"Update enrollment information":"Enroll a student in a group"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("form",{onSubmit:q(X),className:"space-y-6",children:[g&&(0,a.jsx)(S.Fc,{variant:"destructive",children:(0,a.jsx)(S.TN,{children:g})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Student Information"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(C.J,{htmlFor:"studentId",children:"Student *"}),(0,a.jsxs)(c.l6,{value:H,onValueChange:e=>B("studentId",e),disabled:!!m,children:[(0,a.jsx)(c.bq,{className:J.studentId?"border-red-500":"",children:(0,a.jsx)(c.yv,{placeholder:"Select student"})}),(0,a.jsx)(c.gC,{children:b.map(e=>(0,a.jsxs)(c.eb,{value:e.id,children:[e.user.name," - ",e.level," (",e.branch,")"]},e.id))})]}),J.studentId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:J.studentId.message}),z&&(0,a.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,a.jsx)("div",{className:"flex justify-between items-start",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:z.user.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:z.user.phone}),(0,a.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,a.jsx)(d.E,{variant:"outline",children:z.level}),(0,a.jsx)(d.E,{variant:"outline",children:z.branch})]})]})}),z.enrollments.length>0&&(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Current Enrollments:"}),z.enrollments.map((e,t)=>(0,a.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:[e.group.name," - ",e.group.course.name,(0,a.jsx)(d.E,{className:"ml-2",size:"sm",children:e.status})]},t))]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(j.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Group Selection"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(C.J,{htmlFor:"groupId",children:"Group *"}),(0,a.jsxs)(c.l6,{value:W,onValueChange:e=>B("groupId",e),disabled:!!x,children:[(0,a.jsx)(c.bq,{className:J.groupId?"border-red-500":"",children:(0,a.jsx)(c.yv,{placeholder:"Select group"})}),(0,a.jsx)(c.gC,{children:M.map(e=>(0,a.jsxs)(c.eb,{value:e.id,children:[e.name," - ",e.course.name," (",e._count.enrollments,"/",e.capacity,")"]},e.id))})]}),J.groupId&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:J.groupId.message}),z&&0===M.length&&(0,a.jsx)(S.Fc,{children:(0,a.jsxs)(S.TN,{children:["No available groups found for this student's level (",z.level,") and branch (",z.branch,")."]})}),V&&(0,a.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:V.name}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:V.course.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Teacher: ",V.teacher.user.name]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsx)(d.E,{className:V.course.level===(null==z?void 0:z.level)?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",children:V.course.level}),(0,a.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[V._count.enrollments,"/",V.capacity," students"]}),(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["$",(V.course.price/12500).toFixed(0)," / month"]})]})]}),(0,a.jsxs)("div",{className:"mt-3 pt-3 border-t text-sm text-gray-600",children:[(0,a.jsxs)("p",{children:["Duration: ",V.course.duration," weeks"]}),(0,a.jsxs)("p",{children:["Period: ",new Date(V.startDate).toLocaleDateString()," - ",new Date(V.endDate).toLocaleDateString()]})]}),Q&&(0,a.jsx)(S.Fc,{className:"mt-3",variant:"destructive",children:(0,a.jsxs)(S.TN,{children:["This group is at full capacity (",V.capacity," students)."]})}),K&&(0,a.jsx)(S.Fc,{className:"mt-3",children:(0,a.jsxs)(S.TN,{children:["Student is already enrolled in another ",V.course.level," level course."]})})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Enrollment Details"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(C.J,{htmlFor:"status",children:"Status *"}),(0,a.jsxs)(c.l6,{value:G,onValueChange:e=>B("status",e),children:[(0,a.jsx)(c.bq,{className:J.status?"border-red-500":"",children:(0,a.jsx)(c.yv,{placeholder:"Select status"})}),(0,a.jsx)(c.gC,{children:I.map(e=>(0,a.jsx)(c.eb,{value:e.value,children:e.label},e.value))})]}),J.status&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:J.status.message})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(C.J,{htmlFor:"startDate",children:"Start Date *"}),(0,a.jsx)(i.p,{id:"startDate",type:"date",...Z("startDate"),className:J.startDate?"border-red-500":""}),J.startDate&&(0,a.jsx)("p",{className:"text-sm text-red-500",children:J.startDate.message})]}),(0,a.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,a.jsx)(C.J,{htmlFor:"endDate",children:"End Date"}),(0,a.jsx)(i.p,{id:"endDate",type:"date",...Z("endDate")}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Leave empty to use group's end date"})]})]})]}),z&&V&&(0,a.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium mb-2",children:"Enrollment Summary"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Student:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:z.user.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Group:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:V.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Course:"}),(0,a.jsx)("span",{className:"ml-2 font-medium",children:V.course.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Level Match:"}),(0,a.jsx)("span",{className:"ml-2 font-medium ".concat(V.course.level===z.level?"text-green-600":"text-yellow-600"),children:V.course.level===z.level?"Perfect Match":"Level Mismatch"})]})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[o&&(0,a.jsx)(n.$,{type:"button",variant:"outline",onClick:o,children:"Cancel"}),(0,a.jsxs)(n.$,{type:"submit",disabled:f||Q,children:[f&&(0,a.jsx)(T.A,{className:"mr-2 h-4 w-4 animate-spin"}),u?"Update Enrollment":"Enroll Student"]})]})]})})]})};function M(){let{currentBranch:e}=(0,R.O)(),[t,s]=(0,r.useState)([]),[A,E]=(0,r.useState)(!0),[k,C]=(0,r.useState)(""),[S,D]=(0,r.useState)("ALL"),[T,P]=(0,r.useState)(!1),[I,M]=(0,r.useState)(!1),[F,z]=(0,r.useState)(null),[U,V]=(0,r.useState)(!1),[O,Z]=(0,r.useState)(null),q=(0,r.useCallback)(async()=>{if(null==e?void 0:e.id)try{let t=new URLSearchParams({branch:e.id,limit:"50"});"ALL"!==S&&t.append("status",S);let a="/api/enrollments?".concat(t.toString()),r=await fetch(a),l=await r.json();s(l.enrollments||[])}catch(e){console.error("Error fetching enrollments:",e)}finally{E(!1)}},[S,null==e?void 0:e.id]);(0,r.useEffect)(()=>{(null==e?void 0:e.id)&&q()},[q,null==e?void 0:e.id]);let B=t.filter(e=>e.student.user.name.toLowerCase().includes(k.toLowerCase())||e.student.user.phone.includes(k)||e.group.name.toLowerCase().includes(k.toLowerCase())||e.group.course.name.toLowerCase().includes(k.toLowerCase())||e.group.teacher.user.name.toLowerCase().includes(k.toLowerCase())),_=e=>{switch(e){case"ACTIVE":return(0,a.jsx)(x.A,{className:"h-4 w-4 text-green-600"});case"COMPLETED":return(0,a.jsx)(x.A,{className:"h-4 w-4 text-blue-600"});case"DROPPED":return(0,a.jsx)(h.A,{className:"h-4 w-4 text-red-600"});case"SUSPENDED":return(0,a.jsx)(f.A,{className:"h-4 w-4 text-yellow-600"});default:return null}},J=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"COMPLETED":return"bg-blue-100 text-blue-800";case"DROPPED":return"bg-red-100 text-red-800";case"SUSPENDED":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},H=e=>new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e),W=t.length,G=t.filter(e=>"ACTIVE"===e.status).length,Y=t.filter(e=>"COMPLETED"===e.status).length,$=t.filter(e=>"DROPPED"===e.status).length,X=t.filter(e=>"SUSPENDED"===e.status).length;return A?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:"Loading..."}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Enrollments Management - ",e.name]}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Manage student enrollments and course assignments for ",e.name]})]}),(0,a.jsxs)(u.lG,{open:T,onOpenChange:P,children:[(0,a.jsx)(u.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"New Enrollment"]})}),(0,a.jsxs)(u.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(u.c7,{children:[(0,a.jsx)(u.L3,{children:"Create New Enrollment"}),(0,a.jsx)(u.rr,{children:"Enroll a student in a course group."})]}),(0,a.jsx)(L,{onSubmit:async e=>{try{(await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok&&(P(!1),q())}catch(e){console.error("Error creating enrollment:",e)}},onCancel:()=>P(!1),isEditing:!1})]})]})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsx)(l.aR,{children:(0,a.jsx)(l.ZB,{children:"Search & Filter"})}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(i.p,{placeholder:"Search by student, group, course, or teacher...",value:k,onChange:e=>C(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(c.l6,{value:S,onValueChange:D,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Filter by status"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"ALL",children:"All Status"}),(0,a.jsx)(c.eb,{value:"ACTIVE",children:"Active"}),(0,a.jsx)(c.eb,{value:"COMPLETED",children:"Completed"}),(0,a.jsx)(c.eb,{value:"DROPPED",children:"Dropped"}),(0,a.jsx)(c.eb,{value:"SUSPENDED",children:"Suspended"})]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(j.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Enrollments"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:W})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-green-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:G})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Y})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(h.A,{className:"h-8 w-8 text-red-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Dropped"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:$})]})]})})}),(0,a.jsx)(l.Zp,{children:(0,a.jsx)(l.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(f.A,{className:"h-8 w-8 text-yellow-600"}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Suspended"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:X})]})]})})})]}),(0,a.jsxs)(l.Zp,{children:[(0,a.jsxs)(l.aR,{children:[(0,a.jsxs)(l.ZB,{children:["Enrollments (",B.length,")"]}),(0,a.jsx)(l.BT,{children:"Student course enrollments and their current status"})]}),(0,a.jsx)(l.Wu,{children:(0,a.jsxs)(o.XI,{children:[(0,a.jsx)(o.A0,{children:(0,a.jsxs)(o.Hj,{children:[(0,a.jsx)(o.nd,{children:"Student"}),(0,a.jsx)(o.nd,{children:"Course"}),(0,a.jsx)(o.nd,{children:"Group"}),(0,a.jsx)(o.nd,{children:"Teacher"}),(0,a.jsx)(o.nd,{children:"Duration"}),(0,a.jsx)(o.nd,{children:"Price"}),(0,a.jsx)(o.nd,{children:"Start Date"}),(0,a.jsx)(o.nd,{children:"Status"}),(0,a.jsx)(o.nd,{children:"Actions"})]})}),(0,a.jsx)(o.BF,{children:B.map(e=>(0,a.jsxs)(o.Hj,{children:[(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-600"})})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student.user.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.student.user.phone})]})]})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.group.course.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Level: ",e.group.course.level]})]})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"font-medium",children:e.group.name})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"text-sm",children:e.group.teacher.user.name})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1 text-gray-400"}),(0,a.jsxs)("span",{className:"text-sm",children:[e.group.course.duration," weeks"]})]})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)("span",{className:"text-sm font-medium",children:H(e.group.course.price)})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-1 text-gray-400"}),(0,a.jsx)("span",{className:"text-sm",children:(0,m.Yq)(e.startDate)})]})}),(0,a.jsx)(o.nA,{children:(0,a.jsx)(d.E,{className:J(e.status),children:(0,a.jsxs)("div",{className:"flex items-center",children:[_(e.status),(0,a.jsx)("span",{className:"ml-1",children:e.status})]})})}),(0,a.jsx)(o.nA,{children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{z(e),M(!0)},children:(0,a.jsx)(b.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:async()=>{if(confirm("Are you sure you want to delete this enrollment?"))try{(await fetch("/api/enrollments/".concat(e.id),{method:"DELETE"})).ok&&q()}catch(e){console.error("Error deleting enrollment:",e)}},className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]})]})}},3999:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>d,cn:()=>l,r6:()=>i,vv:()=>n});var a=s(2596),r=s(9688);function l(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4616:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5784:(e,t,s)=>{"use strict";s.d(t,{bq:()=>m,eb:()=>p,gC:()=>f,l6:()=>o,yv:()=>u});var a=s(5155),r=s(2115),l=s(1992),n=s(6474),d=s(7863),i=s(5196),c=s(3999);let o=l.bL;l.YJ;let u=l.WT,m=r.forwardRef((e,t)=>{let{className:s,children:r,...d}=e;return(0,a.jsxs)(l.l9,{ref:t,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",s),...d,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=l.l9.displayName;let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.PP,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wn,{ref:t,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",s),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let f=r.forwardRef((e,t)=>{let{className:s,children:r,position:n="popper",...d}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:t,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",s),position:n,...d,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,c.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.JU,{ref:t,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",s),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:t,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.wv,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",s),...r})}).displayName=l.wv.displayName},6341:(e,t,s)=>{Promise.resolve().then(s.bind(s,3374))},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>c,r:()=>i});var a=s(5155),r=s(2115),l=s(9708),n=s(2085),d=s(3999);let i=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=r.forwardRef((e,t)=>{let{className:s,variant:r,size:n,asChild:c=!1,...o}=e,u=c?l.DX:"button";return(0,a.jsx)(u,{className:(0,d.cn)(i({variant:r,size:n,className:s})),ref:t,...o})});c.displayName="Button"},7624:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7705:(e,t,s)=>{"use strict";s.d(t,{BranchProvider:()=>d,O:()=>i,Z:()=>c});var a=s(5155),r=s(2115);let l=(0,r.createContext)(void 0),n=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function d(e){let{children:t}=e,[s,d]=(0,r.useState)(n[0]),[i]=(0,r.useState)(n),[c,o]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let t=i.find(t=>t.id===e);t&&d(t)}o(!1)},[i]),(0,a.jsx)(l.Provider,{value:{currentBranch:s,branches:i,switchBranch:e=>{let t=i.find(t=>t.id===e);t&&(d(t),localStorage.setItem("selectedBranch",e))},isLoading:c},children:t})}function i(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function c(){return(0,r.useContext)(l)||null}},7924:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7949:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},8145:(e,t,s)=>{"use strict";s.d(t,{E:()=>d});var a=s(5155);s(2115);var r=s(2085),l=s(3999);let n=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:s}),t),...r})}},8482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d});var a=s(5155),r=s(2115),l=s(3999);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",s),...r})});n.displayName="Card";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",s),...r})});d.displayName="CardHeader";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",s),...r})});i.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",s),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},8524:(e,t,s)=>{"use strict";s.d(t,{A0:()=>d,BF:()=>i,Hj:()=>c,XI:()=>n,nA:()=>u,nd:()=>o});var a=s(5155),r=s(2115),l=s(3999);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});n.displayName="Table";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",s),...r})});d.displayName="TableHeader";let i=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});i.displayName="TableBody",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});c.displayName="TableRow";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});o.displayName="TableHead";let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});u.displayName="TableCell",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"},9026:(e,t,s)=>{"use strict";s.d(t,{Fc:()=>i,TN:()=>c});var a=s(5155),r=s(2115),l=s(2085),n=s(3999);let d=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=r.forwardRef((e,t)=>{let{className:s,variant:r,...l}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,n.cn)(d({variant:r}),s),...l})});i.displayName="Alert",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h5",{ref:t,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",s),...r})}).displayName="AlertTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",s),...r})});c.displayName="AlertDescription"},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9840:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>m,L3:()=>h,c7:()=>x,lG:()=>i,rr:()=>f,zM:()=>c});var a=s(5155),r=s(2115),l=s(5452),n=s(4416),d=s(3999);let i=l.bL,c=l.l9,o=l.ZL;l.bm;let u=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.hJ,{ref:t,className:(0,d.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",s),...r})});u.displayName=l.hJ.displayName;let m=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(o,{children:[(0,a.jsx)(u,{}),(0,a.jsx)(l.UC,{ref:t,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",s),...i,children:(0,a.jsxs)("div",{className:"relative",children:[r,(0,a.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});m.displayName=l.UC.displayName;let x=e=>{let{className:t,...s}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...s})};x.displayName="DialogHeader";let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.hE,{ref:t,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",s),...r})});h.displayName=l.hE.displayName;let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(l.VY,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",s),...r})});f.displayName=l.VY.displayName},9852:(e,t,s)=>{"use strict";s.d(t,{p:()=>n});var a=s(5155),r=s(2115),l=s(3999);let n=r.forwardRef((e,t)=>{let{className:s,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",s),ref:t,...n})});n.displayName="Input"}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,6221,4358,1071,2356,8441,1684,7358],()=>t(6341)),_N_E=e.O()}]);