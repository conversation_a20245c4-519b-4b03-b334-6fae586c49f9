(()=>{var e={};e.id=1779,e.ids=[1779],e.modules={3018:(e,a,s)=>{"use strict";s.d(a,{Fc:()=>d,TN:()=>c});var t=s(60687),r=s(43210),i=s(24224),n=s(96241);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef(({className:e,variant:a,...s},r)=>(0,t.jsx)("div",{ref:r,role:"alert",className:(0,n.cn)(l({variant:a}),e),...s}));d.displayName="Alert",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",e),...a})).displayName="AlertTitle";let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",e),...a}));c.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13861:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(18962).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15616:(e,a,s)=>{"use strict";s.d(a,{T:()=>n});var t=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("textarea",{className:(0,i.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));n.displayName="Textarea"},19080:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(18962).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37826:(e,a,s)=>{"use strict";s.d(a,{Cf:()=>h,L3:()=>p,c7:()=>x,lG:()=>d,rr:()=>u,zM:()=>c});var t=s(60687),r=s(43210),i=s(26134),n=s(11860),l=s(96241);let d=i.bL,c=i.l9,o=i.ZL;i.bm;let m=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.hJ,{ref:s,className:(0,l.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));m.displayName=i.hJ.displayName;let h=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(o,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(i.UC,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",e),...s,children:(0,t.jsxs)("div",{className:"relative",children:[a,(0,t.jsxs)(i.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(n.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]}));h.displayName=i.UC.displayName;let x=({className:e,...a})=>(0,t.jsx)("div",{className:(0,l.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...a});x.displayName="DialogHeader";let p=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.hE,{ref:s,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...a}));p.displayName=i.hE.displayName;let u=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.VY,{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",e),...a}));u.displayName=i.VY.displayName},39390:(e,a,s)=>{"use strict";s.d(a,{J:()=>c});var t=s(60687),r=s(43210),i=s(78148),n=s(24224),l=s(96241);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.b,{ref:s,className:(0,l.cn)(d(),e),...a}));c.displayName=i.b.displayName},41700:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>t});let t=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\cabinets\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\page.tsx","default")},42902:(e,a,s)=>{"use strict";s.d(a,{d:()=>l});var t=s(60687),r=s(43210),i=s(90270),n=s(96241);let l=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.bL,{className:(0,n.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",e),...a,ref:s,children:(0,t.jsx)(i.zi,{className:(0,n.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})}));l.displayName=i.bL.displayName},46605:(e,a,s)=>{Promise.resolve().then(s.bind(s,41700))},55192:(e,a,s)=>{"use strict";s.d(a,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>n,aR:()=>l});var t=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...a}));n.displayName="Card";let l=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));l.displayName="CardHeader";let d=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));d.displayName="CardTitle";let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",e),...a}));o.displayName="CardContent",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,a,s)=>{"use strict";s.d(a,{bq:()=>h,eb:()=>f,gC:()=>u,l6:()=>o,yv:()=>m});var t=s(60687),r=s(43210),i=s(22670),n=s(78272),l=s(3589),d=s(13964),c=s(96241);let o=i.bL;i.YJ;let m=i.WT,h=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(i.l9,{ref:r,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...s,children:[a,(0,t.jsx)(i.In,{asChild:!0,children:(0,t.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]}));h.displayName=i.l9.displayName;let x=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(l.A,{className:"h-4 w-4"})}));x.displayName=i.PP.displayName;let p=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",e),...a,children:(0,t.jsx)(n.A,{className:"h-4 w-4"})}));p.displayName=i.wn.displayName;let u=r.forwardRef(({className:e,children:a,position:s="popper",...r},n)=>(0,t.jsx)(i.ZL,{children:(0,t.jsxs)(i.UC,{ref:n,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:s,...r,children:[(0,t.jsx)(x,{}),(0,t.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:a}),(0,t.jsx)(p,{})]})}));u.displayName=i.UC.displayName,r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...a})).displayName=i.JU.displayName;let f=r.forwardRef(({className:e,children:a,...s},r)=>(0,t.jsxs)(i.q7,{ref:r,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(i.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(i.p4,{children:a})]}));f.displayName=i.q7.displayName,r.forwardRef(({className:e,...a},s)=>(0,t.jsx)(i.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=i.wv.displayName},68988:(e,a,s)=>{"use strict";s.d(a,{p:()=>n});var t=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,type:a,...s},r)=>(0,t.jsx)("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...s}));n.displayName="Input"},74691:(e,a,s)=>{"use strict";s.r(a),s.d(a,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>c});var t=s(65239),r=s(48088),i=s(88170),n=s.n(i),l=s(30893),d={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>l[e]);s.d(a,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["cabinets",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,41700)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\cabinets\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},h=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/cabinets/page",pathname:"/dashboard/cabinets",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},85838:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>_});var t=s(60687),r=s(43210),i=s(55192),n=s(24934),l=s(68988),d=s(59821),c=s(96752),o=s(37826),m=s(63974),h=s(3018),x=s(96545),p=s(72730),u=s(96474),f=s(99270),b=s(97992),j=s(41312),y=s(13861),g=s(9923),v=s(88233),N=s(27605),w=s(63442),k=s(9275),A=s(39390),C=s(15616),R=s(42902),M=s(18962);let P=(0,M.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),T=(0,M.A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var q=s(19080);let F=k.Ik({name:k.Yj().min(2,"Cabinet name must be at least 2 characters"),number:k.Yj().min(1,"Cabinet number is required"),capacity:k.ai().min(1,"Capacity must be at least 1").max(100,"Capacity cannot exceed 100"),floor:k.ai().optional(),building:k.Yj().optional(),branch:k.Yj().min(1,"Branch is required"),equipment:k.Yj().optional(),notes:k.Yj().optional(),isActive:k.zM().default(!0)}),E=[{value:"Main Branch",label:"Main Branch"},{value:"Branch",label:"Branch"}];function L({initialData:e,onSubmit:a,onCancel:s,isEditing:i=!1}){let[d,c]=(0,r.useState)(!1),[o,x]=(0,r.useState)(null),{register:u,handleSubmit:f,setValue:y,watch:g,formState:{errors:v}}=(0,N.mN)({resolver:(0,w.u)(F),defaultValues:{name:e?.name||"",number:e?.number||"",capacity:e?.capacity||20,floor:e?.floor||void 0,building:e?.building||"",branch:e?.branch||"",equipment:e?.equipment||"",notes:e?.notes||"",isActive:e?.isActive??!0}}),k=g("isActive"),M=async e=>{try{c(!0),x(null),await a(e)}catch(e){x(e instanceof Error?e.message:"An error occurred")}finally{c(!1)}};return(0,t.jsxs)("form",{onSubmit:f(M),className:"space-y-6",children:[o&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:o})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(P,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"name",children:"Cabinet Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(P,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{id:"name",...u("name"),placeholder:"e.g., Computer Lab 1",className:"pl-10"})]}),v.name&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:v.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"number",children:"Cabinet Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(T,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{id:"number",...u("number"),placeholder:"e.g., 101, A-205",className:"pl-10"})]}),v.number&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:v.number.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"capacity",children:"Capacity *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{id:"capacity",type:"number",min:"1",max:"100",...u("capacity",{valueAsNumber:!0}),placeholder:"20",className:"pl-10"})]}),v.capacity&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:v.capacity.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"branch",children:"Branch *"}),(0,t.jsxs)(m.l6,{value:g("branch"),onValueChange:e=>y("branch",e),children:[(0,t.jsx)(m.bq,{children:(0,t.jsx)(m.yv,{placeholder:"Select branch"})}),(0,t.jsx)(m.gC,{children:E.map(e=>(0,t.jsx)(m.eb,{value:e.value,children:e.label},e.value))})]}),v.branch&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:v.branch.message})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Location Details"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"floor",children:"Floor"}),(0,t.jsx)(l.p,{id:"floor",type:"number",min:"0",...u("floor",{valueAsNumber:!0}),placeholder:"e.g., 1, 2, 3"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"building",children:"Building"}),(0,t.jsx)(l.p,{id:"building",...u("building"),placeholder:"e.g., Main Building, Block A"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(q.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Additional Information"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"equipment",children:"Equipment"}),(0,t.jsx)(C.T,{id:"equipment",...u("equipment"),placeholder:"List available equipment (projector, whiteboard, computers, etc.)",rows:3})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"notes",children:"Notes"}),(0,t.jsx)(C.T,{id:"notes",...u("notes"),placeholder:"Additional notes about the cabinet",rows:3})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(R.d,{id:"isActive",checked:k,onCheckedChange:e=>y("isActive",e)}),(0,t.jsx)(A.J,{htmlFor:"isActive",children:"Active Cabinet"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-6 border-t",children:[s&&(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:s,children:"Cancel"}),(0,t.jsxs)(n.$,{type:"submit",disabled:d,children:[d&&(0,t.jsx)(p.A,{className:"mr-2 h-4 w-4 animate-spin"}),i?"Update Cabinet":"Create Cabinet"]})]})]})}var S=s(85814),z=s.n(S);function _(){let{currentBranch:e}=(0,x.O)(),[a,s]=(0,r.useState)([]),[N,w]=(0,r.useState)(!0),[k,A]=(0,r.useState)(null),[C,R]=(0,r.useState)(""),[M,P]=(0,r.useState)("all"),[T,q]=(0,r.useState)(!1),[F,E]=(0,r.useState)(!1),[S,_]=(0,r.useState)(null),B=(0,r.useCallback)(async()=>{try{w(!0);let a=new URLSearchParams({branch:e.id,..."all"!==M&&{isActive:M},...C&&{search:C}}),t=await fetch(`/api/cabinets?${a}`);if(!t.ok)throw Error("Failed to fetch cabinets");let r=await t.json();s(r.cabinets)}catch(e){A(e instanceof Error?e.message:"An error occurred")}finally{w(!1)}},[e.id,M,C]),J=async a=>{let s=await fetch("/api/cabinets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...a,branch:e.id})});if(!s.ok)throw Error((await s.json()).error||"Failed to create cabinet");q(!1),B()},U=async e=>{if(!S)return;let a=await fetch(`/api/cabinets/${S.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).error||"Failed to update cabinet");E(!1),_(null),B()},D=async e=>{if(confirm("Are you sure you want to delete this cabinet? This action cannot be undone."))try{let a=await fetch(`/api/cabinets/${e}`,{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to delete cabinet")}B()}catch(e){A(e instanceof Error?e.message:"An error occurred")}},Z=a.filter(e=>{let a=e.name.toLowerCase().includes(C.toLowerCase())||e.number.toLowerCase().includes(C.toLowerCase())||e.building?.toLowerCase().includes(C.toLowerCase()),s="all"===M||"true"===M&&e.isActive||"false"===M&&!e.isActive;return a&&s});return N?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(p.A,{className:"h-8 w-8 animate-spin"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[k&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:k})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Cabinets Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage classroom cabinets and their schedules"})]}),(0,t.jsxs)(o.lG,{open:T,onOpenChange:q,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsxs)(n.$,{children:[(0,t.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add Cabinet"]})}),(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Add New Cabinet"}),(0,t.jsx)(o.rr,{children:"Create a new cabinet for classroom management."})]}),(0,t.jsx)(L,{onSubmit:J,onCancel:()=>q(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)(i.ZB,{children:"Filters"})}),(0,t.jsx)(i.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(l.p,{placeholder:"Search cabinets...",value:C,onChange:e=>R(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)(m.l6,{value:M,onValueChange:P,children:[(0,t.jsx)(m.bq,{className:"w-full sm:w-48",children:(0,t.jsx)(m.yv,{placeholder:"Filter by status"})}),(0,t.jsxs)(m.gC,{children:[(0,t.jsx)(m.eb,{value:"all",children:"All Cabinets"}),(0,t.jsx)(m.eb,{value:"true",children:"Active Only"}),(0,t.jsx)(m.eb,{value:"false",children:"Inactive Only"})]})]})]})})]}),(0,t.jsxs)(i.Zp,{children:[(0,t.jsxs)(i.aR,{children:[(0,t.jsxs)(i.ZB,{children:["Cabinets (",Z.length,")"]}),(0,t.jsx)(i.BT,{children:"Manage classroom cabinets and their assignments"})]}),(0,t.jsxs)(i.Wu,{children:[(0,t.jsxs)(c.XI,{children:[(0,t.jsx)(c.A0,{children:(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nd,{children:"Cabinet"}),(0,t.jsx)(c.nd,{children:"Location"}),(0,t.jsx)(c.nd,{children:"Capacity"}),(0,t.jsx)(c.nd,{children:"Groups"}),(0,t.jsx)(c.nd,{children:"Schedules"}),(0,t.jsx)(c.nd,{children:"Status"}),(0,t.jsx)(c.nd,{children:"Actions"})]})}),(0,t.jsx)(c.BF,{children:Z.map(e=>(0,t.jsxs)(c.Hj,{children:[(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["#",e.number]})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm",children:[e.building&&`${e.building}, `,void 0!==e.floor&&`Floor ${e.floor}`]})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(j.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{children:e.capacity})]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)(d.E,{variant:"outline",children:[e._count.groups," groups"]})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)(d.E,{variant:"outline",children:[e._count.schedules," schedules"]})}),(0,t.jsx)(c.nA,{children:(0,t.jsx)(d.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(c.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(z(),{href:`/dashboard/cabinets/${e.id}`,children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",children:(0,t.jsx)(y.A,{className:"h-4 w-4"})})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{_(e),E(!0)},children:(0,t.jsx)(g.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>D(e.id),children:(0,t.jsx)(v.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===Z.length&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No cabinets found matching your criteria."})})]})]}),(0,t.jsx)(o.lG,{open:F,onOpenChange:E,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Edit Cabinet"}),(0,t.jsx)(o.rr,{children:"Update cabinet information and settings."})]}),S&&(0,t.jsx)(L,{initialData:S,onSubmit:U,onCancel:()=>{E(!1),_(null)},isEditing:!0})]})})]})}},88233:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(18962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93869:(e,a,s)=>{Promise.resolve().then(s.bind(s,85838))},96752:(e,a,s)=>{"use strict";s.d(a,{A0:()=>l,BF:()=>d,Hj:()=>c,XI:()=>n,nA:()=>m,nd:()=>o});var t=s(60687),r=s(43210),i=s(96241);let n=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,i.cn)("w-full caption-bottom text-sm",e),...a})}));n.displayName="Table";let l=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("thead",{ref:s,className:(0,i.cn)("[&_tr]:border-b",e),...a}));l.displayName="TableHeader";let d=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tbody",{ref:s,className:(0,i.cn)("[&_tr:last-child]:border-0",e),...a}));d.displayName="TableBody",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tfoot",{ref:s,className:(0,i.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let c=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("tr",{ref:s,className:(0,i.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));c.displayName="TableRow";let o=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("th",{ref:s,className:(0,i.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));o.displayName="TableHead";let m=r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("td",{ref:s,className:(0,i.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));m.displayName="TableCell",r.forwardRef(({className:e,...a},s)=>(0,t.jsx)("caption",{ref:s,className:(0,i.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption"},97992:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(18962).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var a=require("../../../../webpack-runtime.js");a.C(e);var s=e=>a(a.s=e),t=a.X(0,[4243,7615,2918,8887,8706,7825,6027,3039],()=>s(74691));module.exports=t})();