(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2122],{3580:(e,s,a)=>{"use strict";a.d(s,{dj:()=>x});var t=a(2115);let r=0,l=new Map,n=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),o({type:"REMOVE_TOAST",toastId:e})},5e3);l.set(e,s)},c=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=s;return a?n(a):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},i=[],d={toasts:[]};function o(e){d=c(d,e),i.forEach(e=>{e(d)})}function h(e){let{...s}=e,a=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),t=()=>o({type:"DISMISS_TOAST",toastId:a});return o({type:"ADD_TOAST",toast:{...s,id:a,open:!0,onOpenChange:e=>{e||t()}}}),{id:a,dismiss:t,update:e=>o({type:"UPDATE_TOAST",toast:{...e,id:a}})}}function x(){let[e,s]=t.useState(d);return t.useEffect(()=>(i.push(s),()=>{let e=i.indexOf(s);e>-1&&i.splice(e,1)}),[e]),{...e,toast:h,dismiss:e=>o({type:"DISMISS_TOAST",toastId:e})}}},4964:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>i,tU:()=>c});var t=a(5155),r=a(2115),l=a(704),n=a(3999);let c=l.bL,i=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...r})});i.displayName=l.B8.displayName;let d=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...r})});d.displayName=l.l9.displayName;let o=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)(l.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...r})});o.displayName=l.UC.displayName},5450:(e,s,a)=>{Promise.resolve().then(a.bind(a,8673))},8673:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>Y});var t=a(5155),r=a(2115),l=a(8482),n=a(7168),c=a(8145),i=a(9852),d=a(5784),o=a(9840),h=a(9026),x=a(4964),u=a(3999),m=a(4621),j=a(2525),p=a(1007),g=a(4186),y=a(9074),f=a(7580),v=a(2318),N=a(7863),b=a(6474),w=a(7624),E=a(5040),A=a(7924),S=a(4616),C=a(2177),L=a(221),T=a(1153),I=a(2714),D=a(88),_=a(4516),k=a(7705);let O=T.Ik({name:T.Yj().min(2,"Group name must be at least 2 characters"),courseId:T.Yj().min(1,"Course is required"),teacherId:T.Yj().min(1,"Teacher is required"),capacity:T.ai().min(1,"Capacity must be at least 1").max(50,"Capacity cannot exceed 50"),schedule:T.Yj().min(1,"Schedule is required"),room:T.Yj().optional(),cabinetId:T.Yj().optional(),branch:T.Yj().min(1,"Branch is required"),startDate:T.Yj().min(1,"Start date is required"),endDate:T.Yj().min(1,"End date is required"),isActive:T.zM().default(!0)}),F=[{value:"Main Branch",label:"Main Branch"},{value:"Branch",label:"Branch"}],V=["Monday, Wednesday, Friday - 9:00-11:00","Monday, Wednesday, Friday - 11:00-13:00","Monday, Wednesday, Friday - 14:00-16:00","Monday, Wednesday, Friday - 16:00-18:00","Monday, Wednesday, Friday - 18:00-20:00","Tuesday, Thursday, Saturday - 9:00-11:00","Tuesday, Thursday, Saturday - 11:00-13:00","Tuesday, Thursday, Saturday - 14:00-16:00","Tuesday, Thursday, Saturday - 16:00-18:00","Tuesday, Thursday, Saturday - 18:00-20:00","Daily - 9:00-10:30","Daily - 10:30-12:00","Daily - 14:00-15:30","Daily - 15:30-17:00","Daily - 17:00-18:30","Daily - 18:30-20:00"],B=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},W=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";case"NEW":return"New";default:return"Unknown"}};function M(e){var s;let{initialData:a,onSubmit:c,onCancel:o,isEditing:x=!1}=e,{currentBranch:u}=(0,k.O)(),[m,j]=(0,r.useState)(!1),[p,v]=(0,r.useState)(null),[N,b]=(0,r.useState)([]),[A,S]=(0,r.useState)([]),[T,M]=(0,r.useState)([]),[G,U]=(0,r.useState)([]),[J,$]=(0,r.useState)([]),{register:q,handleSubmit:z,setValue:P,watch:R,formState:{errors:Z}}=(0,C.mN)({resolver:(0,L.u)(O),defaultValues:{name:(null==a?void 0:a.name)||"",courseId:(null==a?void 0:a.courseId)||"",teacherId:(null==a?void 0:a.teacherId)||"",capacity:(null==a?void 0:a.capacity)||15,schedule:(null==a?void 0:a.schedule)||"",room:(null==a?void 0:a.room)||"",cabinetId:(null==a?void 0:a.cabinetId)||"",branch:(null==a?void 0:a.branch)||u.name,startDate:(null==a?void 0:a.startDate)||"",endDate:(null==a?void 0:a.endDate)||"",isActive:null==(s=null==a?void 0:a.isActive)||s}}),Y=R("courseId"),H=R("teacherId"),K=R("cabinetId"),X=R("branch"),Q=R("schedule"),ee=R("isActive");(0,r.useEffect)(()=>{!(null==a?void 0:a.branch)&&(null==u?void 0:u.name)&&P("branch",u.name)},[u,null==a?void 0:a.branch,P]),(0,r.useEffect)(()=>{es(),ea(),et()},[]),(0,r.useEffect)(()=>{let e={A_LEVEL:1,B_LEVEL:2,C_LEVEL:3,NEW:4};if(X){let s=A.filter(e=>e.branch===X).sort((s,a)=>(e[s.tier||"NEW"]||4)-(e[a.tier||"NEW"]||4));U(s),H&&!s.find(e=>e.id===H)&&P("teacherId","")}else U(A.sort((s,a)=>(e[s.tier||"NEW"]||4)-(e[a.tier||"NEW"]||4)))},[X,A,H,P]),(0,r.useEffect)(()=>{if(X){let e=T.filter(e=>e.branch===X&&e.isActive);$(e),K&&!e.find(e=>e.id===K)&&P("cabinetId","")}else $(T.filter(e=>e.isActive))},[X,T,K,P]);let es=async()=>{try{let e=await fetch("/api/courses"),s=await e.json();b(s.courses||[])}catch(e){console.error("Error fetching courses:",e)}},ea=async()=>{try{let e=await fetch("/api/teachers"),s=await e.json();S(s.teachers||[])}catch(e){console.error("Error fetching teachers:",e)}},et=async()=>{try{let e=await fetch("/api/cabinets"),s=await e.json();M(s.cabinets||[])}catch(e){console.error("Error fetching cabinets:",e)}},er=async e=>{j(!0),v(null);try{await c(e)}catch(e){v(e instanceof Error?e.message:"An error occurred")}finally{j(!1)}},el=N.find(e=>e.id===Y);return(0,t.jsxs)(l.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 mr-2"}),x?"Edit Group":"Create New Group"]}),(0,t.jsx)(l.BT,{children:x?"Update group information":"Enter group details to create a new class group"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("form",{onSubmit:z(er),className:"space-y-6",children:[p&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:p})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"name",children:"Group Name *"}),(0,t.jsx)(i.p,{id:"name",...q("name"),placeholder:"e.g., A1-Morning-01",className:Z.name?"border-red-500":""}),Z.name&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"capacity",children:"Capacity *"}),(0,t.jsx)(i.p,{id:"capacity",type:"number",min:"1",max:"50",...q("capacity",{valueAsNumber:!0}),className:Z.capacity?"border-red-500":""}),Z.capacity&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.capacity.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"branch",children:"Branch *"}),(0,t.jsxs)(d.l6,{value:X,onValueChange:e=>P("branch",e),children:[(0,t.jsx)(d.bq,{className:Z.branch?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select branch"})}),(0,t.jsx)(d.gC,{children:F.map(e=>(0,t.jsx)(d.eb,{value:e.value,children:e.label},e.value))})]}),Z.branch&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.branch.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"room",children:"Room"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(_.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(i.p,{id:"room",...q("room"),placeholder:"e.g., Room 101",className:"pl-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"cabinetId",children:"Cabinet"}),(0,t.jsxs)(d.l6,{value:K,onValueChange:e=>P("cabinetId",e),children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"Select cabinet"})}),(0,t.jsx)(d.gC,{children:J.map(e=>(0,t.jsxs)(d.eb,{value:e.id,children:[e.name," (#",e.number,") - Capacity: ",e.capacity]},e.id))})]}),X&&0===J.length&&(0,t.jsx)("p",{className:"text-sm text-yellow-600",children:"No cabinets available for selected branch"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(D.d,{id:"isActive",checked:ee,onCheckedChange:e=>P("isActive",e)}),(0,t.jsx)(I.J,{htmlFor:"isActive",children:"Active Group"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Course & Teacher"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"courseId",children:"Course *"}),(0,t.jsxs)(d.l6,{value:Y,onValueChange:e=>P("courseId",e),children:[(0,t.jsx)(d.bq,{className:Z.courseId?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select course"})}),(0,t.jsx)(d.gC,{children:N.map(e=>(0,t.jsxs)(d.eb,{value:e.id,children:[e.name," - ",e.level]},e.id))})]}),Z.courseId&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.courseId.message}),el&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Duration: ",el.duration," weeks | Price: $",(el.price/12500).toFixed(0)]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"teacherId",children:"Teacher *"}),(0,t.jsxs)(d.l6,{value:H,onValueChange:e=>P("teacherId",e),children:[(0,t.jsx)(d.bq,{className:Z.teacherId?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select teacher"})}),(0,t.jsx)(d.gC,{children:G.map(e=>(0,t.jsx)(d.eb,{value:e.id,children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("span",{children:[e.user.name," - ",e.subject]}),(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(B(e.tier||"NEW")),children:W(e.tier||"NEW")})]})},e.id))})]}),Z.teacherId&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.teacherId.message}),X&&0===G.length&&(0,t.jsx)("p",{className:"text-sm text-yellow-600",children:"No teachers available for selected branch"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Schedule & Duration"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"schedule",children:"Schedule *"}),(0,t.jsxs)(d.l6,{value:Q,onValueChange:e=>P("schedule",e),children:[(0,t.jsx)(d.bq,{className:Z.schedule?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select schedule"})}),(0,t.jsx)(d.gC,{children:V.map(e=>(0,t.jsx)(d.eb,{value:e,children:e},e))})]}),Z.schedule&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.schedule.message})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"startDate",children:"Start Date *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(i.p,{id:"startDate",type:"date",...q("startDate"),className:"pl-10 ".concat(Z.startDate?"border-red-500":"")})]}),Z.startDate&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.startDate.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(I.J,{htmlFor:"endDate",children:"End Date *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(i.p,{id:"endDate",type:"date",...q("endDate"),className:"pl-10 ".concat(Z.endDate?"border-red-500":"")})]}),Z.endDate&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:Z.endDate.message})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[o&&(0,t.jsx)(n.$,{type:"button",variant:"outline",onClick:o,children:"Cancel"}),(0,t.jsxs)(n.$,{type:"submit",disabled:m,children:[m&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),x?"Update Group":"Create Group"]})]})]})})]})}var G=a(8443),U=a(8524),J=a(5868),$=a(1039);function q(){let[e,s]=(0,r.useState)([]),[a,d]=(0,r.useState)(!0),[x,u]=(0,r.useState)(""),[p,g]=(0,r.useState)(!1),[y,v]=(0,r.useState)(!1),[N,b]=(0,r.useState)(null),[C,L]=(0,r.useState)(!1),[T,I]=(0,r.useState)(null);(0,r.useEffect)(()=>{D()},[]);let D=async()=>{try{d(!0);let e=await fetch("/api/courses"),a=await e.json();s(a.courses||[]),I(null)}catch(e){console.error("Error fetching courses:",e),I("Failed to fetch courses")}finally{d(!1)}},_=async e=>{L(!0),I(null);try{let s=await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create course")}g(!1),D()}catch(e){I(e instanceof Error?e.message:"An error occurred")}finally{L(!1)}},k=async e=>{if(N){L(!0),I(null);try{let s=await fetch("/api/courses/".concat(N.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update course")}v(!1),b(null),D()}catch(e){I(e instanceof Error?e.message:"An error occurred")}finally{L(!1)}}},O=async e=>{if(confirm("Are you sure you want to delete this course? This action cannot be undone."))try{let s=await fetch("/api/courses/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete course")}D()}catch(e){I(e instanceof Error?e.message:"An error occurred")}},F=e.filter(e=>{var s;return e.name.toLowerCase().includes(x.toLowerCase())||e.level.toLowerCase().includes(x.toLowerCase())||(null==(s=e.description)?void 0:s.toLowerCase().includes(x.toLowerCase()))}),V=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800";return a?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(w.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading courses..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[T&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:T})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Courses"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage course catalog and pricing"})]}),(0,t.jsxs)(o.lG,{open:p,onOpenChange:g,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsxs)(n.$,{children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Add Course"]})}),(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Add New Course"}),(0,t.jsx)(o.rr,{children:"Create a new course offering with pricing and duration details."})]}),(0,t.jsx)($.A,{onSubmit:_,onCancel:()=>g(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{children:"Search Courses"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(i.p,{placeholder:"Search by course name, level, or description...",value:x,onChange:e=>u(e.target.value),className:"pl-10"})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Courses (",F.length,")"]}),(0,t.jsx)(l.BT,{children:"Complete list of available courses"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsxs)(U.XI,{children:[(0,t.jsx)(U.A0,{children:(0,t.jsxs)(U.Hj,{children:[(0,t.jsx)(U.nd,{children:"Course"}),(0,t.jsx)(U.nd,{children:"Level"}),(0,t.jsx)(U.nd,{children:"Duration"}),(0,t.jsx)(U.nd,{children:"Price"}),(0,t.jsx)(U.nd,{children:"Groups"}),(0,t.jsx)(U.nd,{children:"Students"}),(0,t.jsx)(U.nd,{children:"Status"}),(0,t.jsx)(U.nd,{children:"Actions"})]})}),(0,t.jsx)(U.BF,{children:F.map(e=>(0,t.jsxs)(U.Hj,{children:[(0,t.jsx)(U.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(E.A,{className:"h-5 w-5 text-blue-600"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description})]})]})}),(0,t.jsx)(U.nA,{children:(0,t.jsx)(c.E,{className:V(e.level),children:e.level.replace("_"," ")})}),(0,t.jsxs)(U.nA,{children:[e.duration," weeks"]}),(0,t.jsx)(U.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(J.A,{className:"h-4 w-4 text-gray-400 mr-1"}),e.price.toLocaleString()," UZS"]})}),(0,t.jsx)(U.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400 mr-1"}),e._count.groups]})}),(0,t.jsx)(U.nA,{children:e.groups.reduce((e,s)=>e+s._count.enrollments,0)}),(0,t.jsx)(U.nA,{children:(0,t.jsx)(c.E,{className:e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(U.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{b(e),v(!0)},children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>O(e.id),children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===F.length&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No courses found matching your search."})})]})]}),(0,t.jsx)(o.lG,{open:y,onOpenChange:v,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Edit Course"}),(0,t.jsx)(o.rr,{children:"Update course information, pricing, and availability."})]}),N&&(0,t.jsx)($.A,{initialData:{name:N.name,level:N.level,description:N.description||"",duration:N.duration,price:N.price,isActive:N.isActive},onSubmit:k,onCancel:()=>{v(!1),b(null)},isEditing:!0})]})})]})}var z=a(3580);let P=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},R=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";case"NEW":return"New";default:return"Unknown"}};function Z(e){let{group:s,onEdit:a,onDelete:r,onAddStudent:i,onToggleExpansion:d,isExpanded:o}=e;return(0,t.jsx)(l.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(c.E,{className:{A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"}[s.course.level]||"bg-gray-100 text-gray-800",variant:"secondary",children:s.course.level.replace("_"," ")}),(0,t.jsxs)(c.E,{className:"bg-blue-100 text-blue-800",variant:"secondary",children:[s._count.enrollments,"/",s.capacity," students"]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>a(s),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>r(s.id),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:s.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:s.course.name})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Teacher: ",s.teacher.user.name]})]}),(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(P(s.teacher.tier||"NEW")),children:R(s.teacher.tier||"NEW")})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm",children:(e=>{let s=e.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/);return s?"".concat(s[1],":").concat(s[2]," - ").concat(s[3],":").concat(s[4]):""})(s.schedule)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm",children:["Start date: ",(0,u.Yq)(new Date(s.startDate))]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(c.E,{className:s.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",variant:"secondary",children:s.isActive?"Started":"Not Started"}),(0,t.jsx)(c.E,{className:"bg-blue-100 text-blue-800",variant:"secondary",children:"UZBEK"})]}),(0,t.jsxs)("div",{className:"space-y-3 pt-4 border-t",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Students (",s._count.enrollments,"/",s.capacity,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(n.$,{variant:"outline",size:"sm",onClick:()=>i(s),disabled:s._count.enrollments>=s.capacity,className:"text-green-600 hover:text-green-700",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Add Student"]}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>d(s.id),children:o?(0,t.jsx)(N.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})})]})]}),o&&(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 space-y-2",children:s.enrollments.length>0?s.enrollments.filter(e=>"ACTIVE"===e.status).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:e.student.user.name}),(0,t.jsx)("span",{className:"text-gray-500 ml-2",children:e.student.user.phone})]}),(0,t.jsx)(c.E,{variant:"outline",className:"text-xs",children:e.status})]},e.id)):(0,t.jsx)("p",{className:"text-sm text-gray-500 text-center py-2",children:"No students enrolled"})})]})]})})})}function Y(){let{currentBranch:e}=(0,k.O)(),{toast:s}=(0,z.dj)(),[a,c]=(0,r.useState)([]),[u,m]=(0,r.useState)([]),[j,p]=(0,r.useState)(!0),[g,y]=(0,r.useState)(""),[v,N]=(0,r.useState)(""),[b,C]=(0,r.useState)(""),[L,T]=(0,r.useState)(""),[I,D]=(0,r.useState)(""),[_,O]=(0,r.useState)(!1),[F,V]=(0,r.useState)(!1),[B,W]=(0,r.useState)(null),[U,J]=(0,r.useState)(!1),[$,Y]=(0,r.useState)(null),[H,K]=(0,r.useState)(new Set),[X,Q]=(0,r.useState)(!1),[ee,es]=(0,r.useState)(null),[ea,et]=(0,r.useState)([]),[er,el]=(0,r.useState)([]),[en,ec]=(0,r.useState)(""),[ei,ed]=(0,r.useState)(!1),[eo,eh]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(null==e?void 0:e.id)&&ex()},[null==e?void 0:e.id]);let ex=async()=>{if(null==e?void 0:e.id)try{p(!0);let s=await fetch("/api/groups?branch=".concat(e.id)),a=await s.json();c(a.groups||[]),m(a.slotTierAnalysis||[]),Y(null)}catch(e){console.error("Error fetching groups:",e),Y("Failed to fetch groups")}finally{p(!1)}},eu=async e=>{J(!0),Y(null);try{let s=await fetch("/api/groups",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create group")}O(!1),ex()}catch(e){Y(e instanceof Error?e.message:"An error occurred")}finally{J(!1)}},em=async e=>{if(B){J(!0),Y(null);try{let s=await fetch("/api/groups/".concat(B.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update group")}V(!1),W(null),ex()}catch(e){Y(e instanceof Error?e.message:"An error occurred")}finally{J(!1)}}},ej=async e=>{if(confirm("Are you sure you want to delete this group? This action cannot be undone."))try{let s=await fetch("/api/groups/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete group")}ex()}catch(e){Y(e instanceof Error?e.message:"An error occurred")}},ep=e=>{let s=new Set(H);s.has(e)?s.delete(e):s.add(e),K(s)},eg=async a=>{try{ed(!0);let s=await fetch("/api/students?available=true&branch=".concat(null==e?void 0:e.id)),a=await s.json();et(a.students||[]),el(a.students||[]),ec("")}catch(e){console.error("Error fetching students:",e),s({title:"Error",description:"Failed to fetch available students",variant:"destructive"})}finally{ed(!1)}},ey=e=>{try{let s=e;try{let a=JSON.parse(e);s=Array.isArray(a)?a.join(" "):e}catch(a){s=e}let a=s.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/),t=a?"".concat(a[1],":").concat(a[2],"-").concat(a[3],":").concat(a[4]):"Unknown",r=s.toLowerCase(),l=r.includes("monday")&&r.includes("wednesday")&&r.includes("friday")?"MWF":r.includes("tuesday")&&r.includes("thursday")&&r.includes("saturday")?"TTS":"Other";return{time:t,days:l}}catch(s){return console.error("Error parsing schedule:",s,"Schedule:",e),{time:"Unknown",days:"Unknown"}}},ef=async s=>{try{let a=await fetch("/api/groups?branch=".concat(null==e?void 0:e.id)),t=await a.json();if(t.slotTierAnalysis){let{time:e,days:a}=ey(s.schedule),r="".concat(s.course.level,"-").concat(a,"-").concat(e),l=t.slotTierAnalysis.find(e=>e.slotKey===r);if(l)return l.availableTiers.includes(s.teacher.tier)}return!0}catch(e){return console.error("Error checking tier availability:",e),!0}},ev=async e=>{let a=e.teacher.tier||"NEW";if("A_LEVEL"!==a&&!await ef(e))return void s({title:"Teacher Tier Restriction",description:"".concat(R(a)," teachers are not available for new students yet. Higher tier teachers must reach 80% capacity first."),variant:"destructive"});es(e),await eg(e.id),Q(!0)},eN=e=>{ec(e),e.trim()?el(ea.filter(s=>s.user.name.toLowerCase().includes(e.toLowerCase())||s.user.phone.includes(e)||s.level.toLowerCase().includes(e.toLowerCase()))):el(ea)},eb=async e=>{if(ee)try{J(!0);let a=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:e,groupId:ee.id,startDate:new Date().toISOString(),status:"ACTIVE"})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to enroll student")}await ex(),Q(!1),es(null),s({title:"Success",description:"Student enrolled successfully",variant:"success"})}catch(e){console.error("Error enrolling student:",e),s({title:"Error",description:e instanceof Error?e.message:"Failed to enroll student",variant:"destructive"})}finally{J(!1)}},ew=async a=>{if(ee&&e)try{J(!0);let t=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:a.name,phone:a.phone,email:a.email||void 0,role:"STUDENT"})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create user")}let r=await t.json(),l=await fetch("/api/students",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:r.id,level:a.level,branch:e.id,emergencyContact:a.emergencyContact,dateOfBirth:a.dateOfBirth,address:a.address})});if(!l.ok){let e=await l.json();throw Error(e.error||"Failed to create student")}let n=await l.json(),c=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:n.id,groupId:ee.id,startDate:new Date().toISOString(),status:"ACTIVE"})});if(!c.ok){let e=await c.json();throw Error(e.error||"Failed to enroll student")}await ex(),eh(!1),Q(!1),es(null),s({title:"Success",description:"Student created and enrolled successfully",variant:"success"})}catch(e){console.error("Error creating and enrolling student:",e),s({title:"Error",description:e instanceof Error?e.message:"Failed to create and enroll student",variant:"destructive"})}finally{J(!1)}},eE=a.filter(e=>{let s=e.name.toLowerCase().includes(g.toLowerCase())||e.course.name.toLowerCase().includes(g.toLowerCase())||e.teacher.user.name.toLowerCase().includes(g.toLowerCase()),a=!v||"all"===v||e.course.level===v,t=!b||"all"===b||e.course.name.toLowerCase().includes(b.toLowerCase()),r=!I||"all"===I||e.teacher.tier===I,l=!0;return L&&(l=(function(e){let s=e.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/);if(!s)return"";let a=parseInt(s[1]);return a<12?"Morning":a<17?"Afternoon":"Evening"})(e.schedule).toLowerCase()===L),s&&a&&t&&l&&r}).sort((e,s)=>{let a={A_LEVEL:1,B_LEVEL:2,C_LEVEL:3,NEW:4};return(a[e.teacher.tier||"NEW"]||4)-(a[s.teacher.tier||"NEW"]||4)}),eA=eE.filter(e=>e.schedule.toLowerCase().includes("monday")&&e.schedule.toLowerCase().includes("wednesday")&&e.schedule.toLowerCase().includes("friday")),eS=eE.filter(e=>e.schedule.toLowerCase().includes("tuesday")&&e.schedule.toLowerCase().includes("thursday")&&e.schedule.toLowerCase().includes("saturday"));return j?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(w.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading groups..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[$&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:$})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Groups"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage class groups and schedules"})]}),(0,t.jsxs)(o.lG,{open:_,onOpenChange:O,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsx)(n.$,{className:"bg-blue-600 hover:bg-blue-700",children:"Add New Group"})}),(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Create New Group"}),(0,t.jsx)(o.rr,{children:"Set up a new class group with course, teacher, and schedule details."})]}),(0,t.jsx)(M,{onSubmit:eu,onCancel:()=>O(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(x.tU,{defaultValue:"groups",className:"space-y-6",children:[(0,t.jsxs)(x.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(x.Xi,{value:"groups",className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Groups"]}),(0,t.jsxs)(x.Xi,{value:"courses",className:"flex items-center",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Courses"]})]}),(0,t.jsxs)(x.av,{value:"groups",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Select Level"}),(0,t.jsxs)(d.l6,{value:v,onValueChange:N,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"All Levels"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"All Levels"}),(0,t.jsx)(d.eb,{value:"A1",children:"A1"}),(0,t.jsx)(d.eb,{value:"A2",children:"A2"}),(0,t.jsx)(d.eb,{value:"B1",children:"B1"}),(0,t.jsx)(d.eb,{value:"B2",children:"B2"}),(0,t.jsx)(d.eb,{value:"IELTS",children:"IELTS"}),(0,t.jsx)(d.eb,{value:"SAT",children:"SAT"}),(0,t.jsx)(d.eb,{value:"MATH",children:"MATH"}),(0,t.jsx)(d.eb,{value:"KIDS",children:"KIDS"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Language"}),(0,t.jsxs)(d.l6,{value:b,onValueChange:C,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"All Languages"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"All Languages"}),(0,t.jsx)(d.eb,{value:"english",children:"English"}),(0,t.jsx)(d.eb,{value:"russian",children:"Russian"}),(0,t.jsx)(d.eb,{value:"uzbek",children:"Uzbek"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Teacher Tier"}),(0,t.jsxs)(d.l6,{value:I,onValueChange:D,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"All Tiers"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"All Tiers"}),(0,t.jsx)(d.eb,{value:"A_LEVEL",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(P("A_LEVEL")),children:"A-Level"}),(0,t.jsx)("span",{children:"A-Level Teachers"})]})}),(0,t.jsx)(d.eb,{value:"B_LEVEL",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(P("B_LEVEL")),children:"B-Level"}),(0,t.jsx)("span",{children:"B-Level Teachers"})]})}),(0,t.jsx)(d.eb,{value:"C_LEVEL",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(P("C_LEVEL")),children:"C-Level"}),(0,t.jsx)("span",{children:"C-Level Teachers"})]})}),(0,t.jsx)(d.eb,{value:"NEW",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(P("NEW")),children:"New"}),(0,t.jsx)("span",{children:"New Teachers"})]})})]})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-8 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Search"}),(0,t.jsx)(i.p,{placeholder:"Search by teacher or group name",value:g,onChange:e=>y(e.target.value),className:"pl-10"})]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(n.$,{variant:""===L?"default":"outline",onClick:()=>T(""),className:"text-sm",children:"Available Groups"}),(0,t.jsx)(n.$,{variant:"morning"===L?"default":"outline",onClick:()=>T("morning"),className:"text-sm",children:"Morning Groups"}),(0,t.jsx)(n.$,{variant:"afternoon"===L?"default":"outline",onClick:()=>T("afternoon"),className:"text-sm",children:"Afternoon Groups"}),(0,t.jsx)(n.$,{variant:"evening"===L?"default":"outline",onClick:()=>T("evening"),className:"text-sm",children:"Evening Groups"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-blue-600",children:"Monday/Wednesday/Friday"}),(0,t.jsxs)("div",{className:"space-y-4",children:[eA.map(e=>(0,t.jsx)(Z,{group:e,onEdit:e=>{W(e),V(!0)},onDelete:ej,onAddStudent:ev,onToggleExpansion:ep,isExpanded:H.has(e.id)},e.id)),0===eA.length&&(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No M/W/F groups found"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-blue-600",children:"Tuesday/Thursday/Saturday"}),(0,t.jsxs)("div",{className:"space-y-4",children:[eS.map(e=>(0,t.jsx)(Z,{group:e,onEdit:e=>{W(e),V(!0)},onDelete:ej,onAddStudent:ev,onToggleExpansion:ep,isExpanded:H.has(e.id)},e.id)),0===eS.length&&(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No T/T/S groups found"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.length})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.filter(e=>e.isActive).length})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-yellow-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Students"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.reduce((e,s)=>e+s._count.enrollments,0)})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg. Group Size"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.length>0?Math.round(a.reduce((e,s)=>e+s._count.enrollments,0)/a.length):0})]})]})})})]})]}),(0,t.jsx)(x.av,{value:"courses",className:"space-y-6",children:(0,t.jsx)(q,{})})]}),(0,t.jsx)(o.lG,{open:F,onOpenChange:V,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Edit Group"}),(0,t.jsx)(o.rr,{children:"Update group information, schedule, and settings."})]}),B&&(0,t.jsx)(M,{initialData:{name:B.name,courseId:"",teacherId:"",capacity:B.capacity,schedule:B.schedule,room:B.room||"",branch:B.branch,startDate:new Date(B.startDate).toISOString().split("T")[0],endDate:new Date(B.endDate).toISOString().split("T")[0],isActive:B.isActive},onSubmit:em,onCancel:()=>{V(!1),W(null)},isEditing:!0})]})}),(0,t.jsx)(o.lG,{open:X,onOpenChange:Q,children:(0,t.jsxs)(o.Cf,{className:"max-w-lg",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Add Student to Group"}),(0,t.jsx)(o.rr,{children:ee&&(0,t.jsxs)(t.Fragment,{children:["Select a student to enroll in ",(0,t.jsx)("strong",{children:ee.name})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(i.p,{placeholder:"Search students by name, phone, or level...",value:en,onChange:e=>eN(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>eh(!0),className:"w-full text-blue-600 border-blue-200 hover:bg-blue-50",children:[(0,t.jsx)(S.A,{className:"h-4 w-4 mr-2"}),"Create New Student"]})]}),ei?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(w.A,{className:"h-6 w-6 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading students..."})]}):er.length>0?(0,t.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:er.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer",onClick:()=>eb(e.id),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.user.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.user.phone}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["Level: ",e.level]})]}),(0,t.jsx)(n.$,{size:"sm",disabled:U,children:U?(0,t.jsx)(w.A,{className:"h-4 w-4 animate-spin"}):"Enroll"})]},e.id))}):ea.length>0?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No students found matching your search"}),(0,t.jsx)("p",{className:"text-sm",children:"Try a different search term or create a new student"})]}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No available students found"}),(0,t.jsx)("p",{className:"text-sm",children:"All students may already be enrolled in groups"})]}),(0,t.jsx)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{Q(!1),es(null),ec(""),el([])},children:"Cancel"})})]})]})}),(0,t.jsx)(o.lG,{open:eo,onOpenChange:eh,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Create New Student"}),(0,t.jsxs)(o.rr,{children:["Create a new student and automatically enroll them in ",null==ee?void 0:ee.name]})]}),(0,t.jsx)(G.A,{initialData:{branch:(null==e?void 0:e.id)||""},onSubmit:ew,onCancel:()=>eh(!1),isEditing:!1})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,2356,1408,7968,5362,8443,8441,1684,7358],()=>s(5450)),_N_E=e.O()}]);