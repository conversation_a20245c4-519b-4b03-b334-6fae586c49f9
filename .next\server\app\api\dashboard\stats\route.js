(()=>{var e={};e.id=5694,e.ids=[5694],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,t,r)=>{"use strict";function s(e){let t=e.headers.get("X-Inter-Server-Secret"),r=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!t||!s||t!==s)return!1;if(r){let e=parseInt(r),t=Date.now();if(isNaN(e)||t-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function a(e,t){try{let r="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!r)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${r}${t.endpoint}`,a={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let t=Date.now().toString(),r=o.getServerConfig(),s=`${r.serverType}-${t}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":r.serverType,"X-Request-ID":s,"X-Timestamp":t,"User-Agent":`${r.serverType}-server`}}(),...t.headers},n=await fetch(s,{method:t.method,headers:a,body:t.data?JSON.stringify(t.data):void 0}),i=await n.json();return{success:n.ok,data:i,status:n.status,error:n.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}r.d(t,{LQ:()=>o,cU:()=>n,g2:()=>s});class n{static async authenticateUser(e,t){return a("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:t}})}static async getUserData(e){return a("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,t){return a("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:t}})}}class o{static logRequest(e,t,r,s){let a=new Date().toISOString(),n=process.env.SERVER_TYPE||"unknown";console.log(`[${a}] Inter-Server ${e.toUpperCase()}: ${t}`,{server:n,success:r,details:s})}static async healthCheck(e){try{return(await a(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41098:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),a=r(7786);let n={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let t=await a.cU.authenticateUser(e.phone,e.password);if(!t.success)return console.error("Authentication failed:",t.error),null;let r=t.data.user;if(!r)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(r.role))return console.error("User role not allowed on staff server:",r.role),null;return{id:r.id,phone:r.phone,name:r.name,email:r.email,role:r.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role||null),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91783:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d});var a=r(96559),n=r(48088),o=r(37719),i=r(32190),u=r(19854),c=r(41098),l=r(79464);async function d(e){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let t=new Date,r=new Date(t.getFullYear(),t.getMonth(),1),s=new Date(t.getFullYear(),t.getMonth()-1,1),a=new Date(t.getFullYear(),t.getMonth(),0),n=new Date(t);n.setDate(t.getDate()-7);let o=await l.z.student.count(),d=await l.z.student.count({where:{createdAt:{lte:a}}}),p=d>0?Math.round((o-d)/d*100):0,h=0,g=0;try{h=await l.z.lead.count({where:{createdAt:{gte:n}}});let e=new Date(n);e.setDate(e.getDate()-7),g=await l.z.lead.count({where:{createdAt:{gte:e,lt:n}}})}catch(e){console.log("Leads table not available:",e)}let m=g>0?Math.round((h-g)/g*100):0,v=0;try{v=await l.z.group.count({where:{isActive:!0}})}catch(e){console.log("Groups table not available:",e)}let w=0,E=0;try{let e=await l.z.payment.aggregate({where:{status:"PAID",createdAt:{gte:r,lte:t}},_sum:{amount:!0}}),n=await l.z.payment.aggregate({where:{status:"PAID",createdAt:{gte:s,lte:a}},_sum:{amount:!0}});w=Number(e._sum.amount)||0,E=Number(n._sum.amount)||0}catch(e){console.log("Payments table not available:",e)}let R=E>0?Math.round((w-E)/E*100):0,f=[];try{f=await l.z.lead.findMany({orderBy:{createdAt:"desc"},take:4,select:{id:!0,name:!0,coursePreference:!0,createdAt:!0}})}catch(e){console.log("Recent leads not available:",e)}let y=[];try{let e=new Date;e.setHours(0,0,0,0);let t=new Date(e);t.setDate(t.getDate()+1),y=await l.z.class.findMany({where:{date:{gte:e,lt:t}},include:{group:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}},orderBy:{date:"asc"},take:4})}catch(e){console.log("Classes table not available:",e)}let S={totalStudents:{count:o,growth:p},newLeads:{count:h,growth:m},activeGroups:{count:v},monthlyRevenue:{amount:w,growth:R},recentLeads:f.map(e=>({name:e.name,course:e.coursePreference,status:"NEW",time:function(e){let t=Math.floor((new Date().getTime()-e.getTime())/36e5);if(t<1)return"Just now";{if(t<24)return`${t} hour${t>1?"s":""} ago`;let e=Math.floor(t/24);return`${e} day${e>1?"s":""} ago`}}(e.createdAt)})),upcomingClasses:y.map(e=>{var t;return{group:`${e.group?.course?.name||"Unknown Course"} - ${e.group?.course?.level||"Unknown Level"}`,teacher:e.group?.teacher?.user?.name||"No Teacher",time:e.date?(t=e.date.toISOString(),new Date(t).toLocaleTimeString("en-US",{hour:"numeric",minute:"2-digit",hour12:!0})):"TBA",room:"TBA"}})};return i.NextResponse.json(S)}catch(e){return console.error("Error fetching dashboard stats:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/dashboard/stats/route",pathname:"/api/dashboard/stats",filename:"route",bundlePath:"app/api/dashboard/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\dashboard\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:m}=p;function v(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,3412],()=>r(91783));module.exports=s})();