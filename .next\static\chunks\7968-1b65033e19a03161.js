"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7968],{3999:(e,t,r)=>{r.d(t,{Yq:()=>d,cn:()=>n,r6:()=>l,vv:()=>o});var a=r(2596),s=r(9688);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,a.$)(t))}function o(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function d(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function l(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},5784:(e,t,r)=>{r.d(t,{bq:()=>u,eb:()=>x,gC:()=>g,l6:()=>c,yv:()=>f});var a=r(5155),s=r(2115),n=r(1992),o=r(6474),d=r(7863),l=r(5196),i=r(3999);let c=n.bL;n.YJ;let f=n.WT,u=s.forwardRef((e,t)=>{let{className:r,children:s,...d}=e;return(0,a.jsxs)(n.l9,{ref:t,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...d,children:[s,(0,a.jsx)(n.In,{asChild:!0,children:(0,a.jsx)(o.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=n.l9.displayName;let m=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.PP,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})});m.displayName=n.PP.displayName;let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wn,{ref:t,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",r),...s,children:(0,a.jsx)(o.A,{className:"h-4 w-4"})})});p.displayName=n.wn.displayName;let g=s.forwardRef((e,t)=>{let{className:r,children:s,position:o="popper",...d}=e;return(0,a.jsx)(n.ZL,{children:(0,a.jsxs)(n.UC,{ref:t,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:o,...d,children:[(0,a.jsx)(m,{}),(0,a.jsx)(n.LM,{className:(0,i.cn)("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,a.jsx)(p,{})]})})});g.displayName=n.UC.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.JU,{ref:t,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...s})}).displayName=n.JU.displayName;let x=s.forwardRef((e,t)=>{let{className:r,children:s,...o}=e;return(0,a.jsxs)(n.q7,{ref:t,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(n.VF,{children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})}),(0,a.jsx)(n.p4,{children:s})]})});x.displayName=n.q7.displayName,s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.wv,{ref:t,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",r),...s})}).displayName=n.wv.displayName},7168:(e,t,r)=>{r.d(t,{$:()=>i,r:()=>l});var a=r(5155),s=r(2115),n=r(9708),o=r(2085),d=r(3999);let l=(0,o.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),i=s.forwardRef((e,t)=>{let{className:r,variant:s,size:o,asChild:i=!1,...c}=e,f=i?n.DX:"button";return(0,a.jsx)(f,{className:(0,d.cn)(l({variant:s,size:o,className:r})),ref:t,...c})});i.displayName="Button"},8145:(e,t,r)=>{r.d(t,{E:()=>d});var a=r(5155);r(2115);var s=r(2085),n=r(3999);let o=(0,s.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function d(e){let{className:t,variant:r,...s}=e;return(0,a.jsx)("div",{className:(0,n.cn)(o({variant:r}),t),...s})}},8482:(e,t,r)=>{r.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>l,Zp:()=>o,aR:()=>d});var a=r(5155),s=r(2115),n=r(3999);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",r),...s})});o.displayName="Card";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...s})});d.displayName="CardHeader";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});l.displayName="CardTitle";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...s})});i.displayName="CardDescription";let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...s})});c.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},9026:(e,t,r)=>{r.d(t,{Fc:()=>l,TN:()=>i});var a=r(5155),s=r(2115),n=r(2085),o=r(3999);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),l=s.forwardRef((e,t)=>{let{className:r,variant:s,...n}=e;return(0,a.jsx)("div",{ref:t,role:"alert",className:(0,o.cn)(d({variant:s}),r),...n})});l.displayName="Alert",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("h5",{ref:t,className:(0,o.cn)("mb-1 font-medium leading-none tracking-tight",r),...s})}).displayName="AlertTitle";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("div",{ref:t,className:(0,o.cn)("text-sm [&_p]:leading-relaxed",r),...s})});i.displayName="AlertDescription"},9474:(e,t,r)=>{r.d(t,{T:()=>o});var a=r(5155),s=r(2115),n=r(3999);let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...s})});o.displayName="Textarea"},9840:(e,t,r)=>{r.d(t,{Cf:()=>u,L3:()=>p,c7:()=>m,lG:()=>l,rr:()=>g,zM:()=>i});var a=r(5155),s=r(2115),n=r(5452),o=r(4416),d=r(3999);let l=n.bL,i=n.l9,c=n.ZL;n.bm;let f=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.hJ,{ref:t,className:(0,d.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...s})});f.displayName=n.hJ.displayName;let u=s.forwardRef((e,t)=>{let{className:r,children:s,...l}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(f,{}),(0,a.jsx)(n.UC,{ref:t,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",r),...l,children:(0,a.jsxs)("div",{className:"relative",children:[s,(0,a.jsxs)(n.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(o.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});u.displayName=n.UC.displayName;let m=e=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...r})};m.displayName="DialogHeader";let p=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",r),...s})});p.displayName=n.hE.displayName;let g=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,d.cn)("text-sm text-muted-foreground",r),...s})});g.displayName=n.VY.displayName},9852:(e,t,r)=>{r.d(t,{p:()=>o});var a=r(5155),s=r(2115),n=r(3999);let o=s.forwardRef((e,t)=>{let{className:r,type:s,...o}=e;return(0,a.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",r),ref:t,...o})});o.displayName="Input"}}]);