(()=>{var e={};e.id=4325,e.ids=[4325],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},43769:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>w,serverHooks:()=>x,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>D});var n={};r.r(n),r.d(n,{DELETE:()=>m,GET:()=>c,PUT:()=>p});var s=r(96559),a=r(48088),o=r(37719),i=r(32190),l=r(79464),u=r(45697);let d=u.Ik({status:u.k5(["ACTIVE","COMPLETED","DROPPED","SUSPENDED"]).optional(),startDate:u.Yj().optional(),endDate:u.Yj().optional()});async function c(e,{params:t}){try{let{id:e}=await t,r=await l.z.enrollment.findUnique({where:{id:e},include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}},payments:{where:{createdAt:{gte:new Date(new Date().setMonth(new Date().getMonth()-3))}},orderBy:{createdAt:"desc"}}}},group:{include:{course:{select:{id:!0,name:!0,level:!0,description:!0,duration:!0,price:!0}},teacher:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},classes:{include:{attendances:{where:{studentId:e}}},orderBy:{date:"desc"},take:10}}}}});if(!r)return i.NextResponse.json({error:"Enrollment not found"},{status:404});let n=r.group.classes.length,s=r.group.classes.filter(e=>e.attendances.some(e=>"PRESENT"===e.status)).length,a=n>0?s/n*100:0;return i.NextResponse.json({...r,attendanceStats:{totalClasses:n,attendedClasses:s,attendanceRate:Math.round(100*a)/100}})}catch(e){return console.error("Error fetching enrollment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:t}){try{let{id:r}=await t,n=await e.json(),s=d.parse(n);if(!await l.z.enrollment.findUnique({where:{id:r}}))return i.NextResponse.json({error:"Enrollment not found"},{status:404});let a={...s,updatedAt:new Date};s.startDate&&(a.startDate=new Date(s.startDate)),s.endDate&&(a.endDate=new Date(s.endDate)),s.status&&["COMPLETED","DROPPED"].includes(s.status)&&!s.endDate&&(a.endDate=new Date);let o=await l.z.enrollment.update({where:{id:r},data:a,include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},group:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}}});return i.NextResponse.json(o)}catch(e){if(e instanceof u.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating enrollment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:t}){try{let{id:e}=await t,r=await l.z.enrollment.findUnique({where:{id:e}});if(!r)return i.NextResponse.json({error:"Enrollment not found"},{status:404});if("ACTIVE"===r.status)return i.NextResponse.json({error:"Cannot delete active enrollment",details:"Please change status to DROPPED or COMPLETED before deletion"},{status:400});return await l.z.enrollment.delete({where:{id:e}}),i.NextResponse.json({message:"Enrollment deleted successfully",deletedId:e})}catch(e){return console.error("Error deleting enrollment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let w=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/enrollments/[id]/route",pathname:"/api/enrollments/[id]",filename:"route",bundlePath:"app/api/enrollments/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\enrollments\\[id]\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:h,workUnitAsyncStorage:D,serverHooks:x}=w;function E(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:D})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var n=r(96330);let s=globalThis.prisma??new n.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,580,5697],()=>r(43769));module.exports=n})();