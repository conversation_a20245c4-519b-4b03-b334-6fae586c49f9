"use strict";(()=>{var e={};e.id=1498,e.ids=[1498],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},74827:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>g,serverHooks:()=>y,workAsyncStorage:()=>T,workUnitAsyncStorage:()=>w});var s={};t.r(s),t.d(s,{GET:()=>f,POST:()=>h});var n=t(96559),i=t(48088),a=t(37719),o=t(32190),u=t(19854),d=t(41098),c=t(79464),l=t(99326),p=t(96330),m=t(45697);let x=m.Ik({groupId:m.Yj().optional(),dayOfWeek:m.ai().min(0).max(6),startTime:m.Yj().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,"Invalid time format (HH:MM)"),endTime:m.Yj().regex(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/,"Invalid time format (HH:MM)"),title:m.Yj().optional(),isBlocked:m.zM().default(!1)});async function f(e,{params:r}){try{let e=await (0,u.getServerSession)(d.N);if(!e?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r;if(!await c.z.cabinet.findUnique({where:{id:t}}))return o.NextResponse.json({error:"Cabinet not found"},{status:404});let s=await c.z.cabinetSchedule.findMany({where:{cabinetId:t},include:{group:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}},orderBy:[{dayOfWeek:"asc"},{startTime:"asc"}]});return o.NextResponse.json(s)}catch(e){return console.error("Error fetching cabinet schedules:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let t=await (0,u.getServerSession)(d.N);if(!t?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER"].includes(t.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await e.json(),i=x.parse(n),a=await c.z.cabinet.findUnique({where:{id:s}});if(!a)return o.NextResponse.json({error:"Cabinet not found"},{status:404});if(i.startTime>=i.endTime)return o.NextResponse.json({error:"End time must be after start time"},{status:400});if(await c.z.cabinetSchedule.findFirst({where:{cabinetId:s,dayOfWeek:i.dayOfWeek,OR:[{AND:[{startTime:{lte:i.startTime}},{endTime:{gt:i.startTime}}]},{AND:[{startTime:{lt:i.endTime}},{endTime:{gte:i.endTime}}]},{AND:[{startTime:{gte:i.startTime}},{endTime:{lte:i.endTime}}]}]}}))return o.NextResponse.json({error:"Time slot conflicts with existing schedule"},{status:400});if(i.groupId&&!await c.z.group.findUnique({where:{id:i.groupId}}))return o.NextResponse.json({error:"Group not found"},{status:400});let m=await c.z.cabinetSchedule.create({data:{cabinetId:s,...i},include:{group:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}}});return await l._.log({userId:t.user.id,userRole:t.user.role||p.Role.ADMIN,action:"CREATE",resource:"CABINET_SCHEDULE",resourceId:m.id,details:`Created schedule for cabinet ${a.name} on ${["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"][i.dayOfWeek]||"Unknown"} ${i.startTime}-${i.endTime}`}),o.NextResponse.json(m,{status:201})}catch(e){if(e instanceof m.G)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating cabinet schedule:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/cabinets/[id]/schedules/route",pathname:"/api/cabinets/[id]/schedules",filename:"route",bundlePath:"app/api/cabinets/[id]/schedules/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\cabinets\\[id]\\schedules\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:T,workUnitAsyncStorage:w,serverHooks:y}=g;function j(){return(0,a.patchFetch)({workAsyncStorage:T,workUnitAsyncStorage:w})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(74827));module.exports=s})();