(()=>{var e={};e.id=8091,e.ids=[8091],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},53396:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\enrollments\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\enrollments\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63451:(e,s,t)=>{Promise.resolve().then(t.bind(t,72397))},72397:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>q});var r=t(60687),a=t(43210),l=t(55192),n=t(24934),d=t(59821),c=t(68988),i=t(63974),o=t(96752),x=t(37826),m=t(96241),h=t(23689),u=t(83281),p=t(36058),j=t(96474),g=t(99270),v=t(27351),N=t(58869),y=t(48730),f=t(40228),b=t(9923),w=t(88233),E=t(27605),D=t(63442),A=t(9275),S=t(39390),C=t(3018),P=t(23026),k=t(72730),I=t(96545);let L=A.Ik({studentId:A.Yj().min(1,"Student is required"),groupId:A.Yj().min(1,"Group is required"),status:A.k5(["ACTIVE","COMPLETED","DROPPED","SUSPENDED"]).default("ACTIVE"),startDate:A.Yj().min(1,"Start date is required"),endDate:A.Yj().optional()}),T=[{value:"ACTIVE",label:"Active",color:"bg-green-100 text-green-800"},{value:"COMPLETED",label:"Completed",color:"bg-blue-100 text-blue-800"},{value:"DROPPED",label:"Dropped",color:"bg-red-100 text-red-800"},{value:"SUSPENDED",label:"Suspended",color:"bg-yellow-100 text-yellow-800"}],M=function({initialData:e,onSubmit:s,onCancel:t,isEditing:o=!1,preselectedStudentId:x,preselectedGroupId:m}){let{currentBranch:h}=(0,I.O)(),[u,p]=(0,a.useState)(!1),[j,g]=(0,a.useState)(null),[y,b]=(0,a.useState)([]),[w,A]=(0,a.useState)([]),[M,q]=(0,a.useState)([]),[O,_]=(0,a.useState)(null),[U,V]=(0,a.useState)(null),{register:F,handleSubmit:W,setValue:Z,watch:R,formState:{errors:z}}=(0,E.mN)({resolver:(0,D.u)(L),defaultValues:{studentId:x||e?.studentId||"",groupId:m||e?.groupId||"",status:e?.status||"ACTIVE",startDate:e?.startDate||new Date().toISOString().split("T")[0],endDate:e?.endDate||""}}),G=R("studentId"),$=R("groupId"),B=R("status"),J=async e=>{p(!0),g(null);try{await s(e)}catch(e){g(e instanceof Error?e.message:"An error occurred")}finally{p(!1)}},Y=!!(U&&U._count.enrollments>=U.capacity),H=O?.enrollments.some(e=>"ACTIVE"===e.status&&e.group.course.level===U?.course.level);return(0,r.jsxs)(l.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{className:"flex items-center",children:[(0,r.jsx)(P.A,{className:"h-5 w-5 mr-2"}),o?"Edit Enrollment":"New Student Enrollment"]}),(0,r.jsx)(l.BT,{children:o?"Update enrollment information":"Enroll a student in a group"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("form",{onSubmit:W(J),className:"space-y-6",children:[j&&(0,r.jsx)(C.Fc,{variant:"destructive",children:(0,r.jsx)(C.TN,{children:j})}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Student Information"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(S.J,{htmlFor:"studentId",children:"Student *"}),(0,r.jsxs)(i.l6,{value:G,onValueChange:e=>Z("studentId",e),disabled:!!x,children:[(0,r.jsx)(i.bq,{className:z.studentId?"border-red-500":"",children:(0,r.jsx)(i.yv,{placeholder:"Select student"})}),(0,r.jsx)(i.gC,{children:y.map(e=>(0,r.jsxs)(i.eb,{value:e.id,children:[e.user.name," - ",e.level," (",e.branch,")"]},e.id))})]}),z.studentId&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:z.studentId.message}),O&&(0,r.jsxs)("div",{className:"bg-blue-50 p-4 rounded-lg",children:[(0,r.jsx)("div",{className:"flex justify-between items-start",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:O.user.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:O.user.phone}),(0,r.jsxs)("div",{className:"flex space-x-2 mt-2",children:[(0,r.jsx)(d.E,{variant:"outline",children:O.level}),(0,r.jsx)(d.E,{variant:"outline",children:O.branch})]})]})}),O.enrollments.length>0&&(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-700",children:"Current Enrollments:"}),O.enrollments.map((e,s)=>(0,r.jsxs)("div",{className:"text-sm text-gray-600 mt-1",children:[e.group.name," - ",e.group.course.name,(0,r.jsx)(d.E,{className:"ml-2",size:"sm",children:e.status})]},s))]})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(v.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Group Selection"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(S.J,{htmlFor:"groupId",children:"Group *"}),(0,r.jsxs)(i.l6,{value:$,onValueChange:e=>Z("groupId",e),disabled:!!m,children:[(0,r.jsx)(i.bq,{className:z.groupId?"border-red-500":"",children:(0,r.jsx)(i.yv,{placeholder:"Select group"})}),(0,r.jsx)(i.gC,{children:M.map(e=>(0,r.jsxs)(i.eb,{value:e.id,children:[e.name," - ",e.course.name," (",e._count.enrollments,"/",e.capacity,")"]},e.id))})]}),z.groupId&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:z.groupId.message}),O&&0===M.length&&(0,r.jsx)(C.Fc,{children:(0,r.jsxs)(C.TN,{children:["No available groups found for this student's level (",O.level,") and branch (",O.branch,")."]})}),U&&(0,r.jsxs)("div",{className:"bg-green-50 p-4 rounded-lg",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium",children:U.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:U.course.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Teacher: ",U.teacher.user.name]})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)(d.E,{className:U.course.level===O?.level?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800",children:U.course.level}),(0,r.jsxs)("p",{className:"text-sm text-gray-600 mt-1",children:[U._count.enrollments,"/",U.capacity," students"]}),(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:["$",(U.course.price/12500).toFixed(0)," / month"]})]})]}),(0,r.jsxs)("div",{className:"mt-3 pt-3 border-t text-sm text-gray-600",children:[(0,r.jsxs)("p",{children:["Duration: ",U.course.duration," weeks"]}),(0,r.jsxs)("p",{children:["Period: ",new Date(U.startDate).toLocaleDateString()," - ",new Date(U.endDate).toLocaleDateString()]})]}),Y&&(0,r.jsx)(C.Fc,{className:"mt-3",variant:"destructive",children:(0,r.jsxs)(C.TN,{children:["This group is at full capacity (",U.capacity," students)."]})}),H&&(0,r.jsx)(C.Fc,{className:"mt-3",children:(0,r.jsxs)(C.TN,{children:["Student is already enrolled in another ",U.course.level," level course."]})})]})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 text-gray-500"}),(0,r.jsx)("h3",{className:"text-lg font-medium",children:"Enrollment Details"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(S.J,{htmlFor:"status",children:"Status *"}),(0,r.jsxs)(i.l6,{value:B,onValueChange:e=>Z("status",e),children:[(0,r.jsx)(i.bq,{className:z.status?"border-red-500":"",children:(0,r.jsx)(i.yv,{placeholder:"Select status"})}),(0,r.jsx)(i.gC,{children:T.map(e=>(0,r.jsx)(i.eb,{value:e.value,children:e.label},e.value))})]}),z.status&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:z.status.message})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(S.J,{htmlFor:"startDate",children:"Start Date *"}),(0,r.jsx)(c.p,{id:"startDate",type:"date",...F("startDate"),className:z.startDate?"border-red-500":""}),z.startDate&&(0,r.jsx)("p",{className:"text-sm text-red-500",children:z.startDate.message})]}),(0,r.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,r.jsx)(S.J,{htmlFor:"endDate",children:"End Date"}),(0,r.jsx)(c.p,{id:"endDate",type:"date",...F("endDate")}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"Leave empty to use group's end date"})]})]})]}),O&&U&&(0,r.jsxs)("div",{className:"bg-gray-50 p-4 rounded-lg",children:[(0,r.jsx)("h4",{className:"font-medium mb-2",children:"Enrollment Summary"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Student:"}),(0,r.jsx)("span",{className:"ml-2 font-medium",children:O.user.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Group:"}),(0,r.jsx)("span",{className:"ml-2 font-medium",children:U.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Course:"}),(0,r.jsx)("span",{className:"ml-2 font-medium",children:U.course.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Level Match:"}),(0,r.jsx)("span",{className:`ml-2 font-medium ${U.course.level===O.level?"text-green-600":"text-yellow-600"}`,children:U.course.level===O.level?"Perfect Match":"Level Mismatch"})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[t&&(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:t,children:"Cancel"}),(0,r.jsxs)(n.$,{type:"submit",disabled:u||Y,children:[u&&(0,r.jsx)(k.A,{className:"mr-2 h-4 w-4 animate-spin"}),o?"Update Enrollment":"Enroll Student"]})]})]})})]})};function q(){let{currentBranch:e}=(0,I.O)(),[s,t]=(0,a.useState)([]),[E,D]=(0,a.useState)(!0),[A,S]=(0,a.useState)(""),[C,P]=(0,a.useState)("ALL"),[k,L]=(0,a.useState)(!1),[T,q]=(0,a.useState)(!1),[O,_]=(0,a.useState)(null),[U,V]=(0,a.useState)(!1),[F,W]=(0,a.useState)(null),Z=(0,a.useCallback)(async()=>{if(e?.id)try{let s=new URLSearchParams({branch:e.id,limit:"50"});"ALL"!==C&&s.append("status",C);let r=`/api/enrollments?${s.toString()}`,a=await fetch(r),l=await a.json();t(l.enrollments||[])}catch(e){console.error("Error fetching enrollments:",e)}finally{D(!1)}},[C,e?.id]),R=s.filter(e=>e.student.user.name.toLowerCase().includes(A.toLowerCase())||e.student.user.phone.includes(A)||e.group.name.toLowerCase().includes(A.toLowerCase())||e.group.course.name.toLowerCase().includes(A.toLowerCase())||e.group.teacher.user.name.toLowerCase().includes(A.toLowerCase())),z=e=>{switch(e){case"ACTIVE":return(0,r.jsx)(h.A,{className:"h-4 w-4 text-green-600"});case"COMPLETED":return(0,r.jsx)(h.A,{className:"h-4 w-4 text-blue-600"});case"DROPPED":return(0,r.jsx)(u.A,{className:"h-4 w-4 text-red-600"});case"SUSPENDED":return(0,r.jsx)(p.A,{className:"h-4 w-4 text-yellow-600"});default:return null}},G=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"COMPLETED":return"bg-blue-100 text-blue-800";case"DROPPED":return"bg-red-100 text-red-800";case"SUSPENDED":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},$=e=>new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e),B=s.length,J=s.filter(e=>"ACTIVE"===e.status).length,Y=s.filter(e=>"COMPLETED"===e.status).length,H=s.filter(e=>"DROPPED"===e.status).length,X=s.filter(e=>"SUSPENDED"===e.status).length;return E?(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:"Loading..."}):(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Enrollments Management - ",e.name]}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Manage student enrollments and course assignments for ",e.name]})]}),(0,r.jsxs)(x.lG,{open:k,onOpenChange:L,children:[(0,r.jsx)(x.zM,{asChild:!0,children:(0,r.jsxs)(n.$,{children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"New Enrollment"]})}),(0,r.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(x.c7,{children:[(0,r.jsx)(x.L3,{children:"Create New Enrollment"}),(0,r.jsx)(x.rr,{children:"Enroll a student in a course group."})]}),(0,r.jsx)(M,{onSubmit:async e=>{try{(await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok&&(L(!1),Z())}catch(e){console.error("Error creating enrollment:",e)}},onCancel:()=>L(!1),isEditing:!1})]})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Search & Filter"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(c.p,{placeholder:"Search by student, group, course, or teacher...",value:A,onChange:e=>S(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(i.l6,{value:C,onValueChange:P,children:[(0,r.jsx)(i.bq,{children:(0,r.jsx)(i.yv,{placeholder:"Filter by status"})}),(0,r.jsxs)(i.gC,{children:[(0,r.jsx)(i.eb,{value:"ALL",children:"All Status"}),(0,r.jsx)(i.eb,{value:"ACTIVE",children:"Active"}),(0,r.jsx)(i.eb,{value:"COMPLETED",children:"Completed"}),(0,r.jsx)(i.eb,{value:"DROPPED",children:"Dropped"}),(0,r.jsx)(i.eb,{value:"SUSPENDED",children:"Suspended"})]})]})]})})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Enrollments"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:B})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:J})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Y})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-red-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Dropped"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:H})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-yellow-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Suspended"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:X})]})]})})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{children:["Enrollments (",R.length,")"]}),(0,r.jsx)(l.BT,{children:"Student course enrollments and their current status"})]}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)(o.XI,{children:[(0,r.jsx)(o.A0,{children:(0,r.jsxs)(o.Hj,{children:[(0,r.jsx)(o.nd,{children:"Student"}),(0,r.jsx)(o.nd,{children:"Course"}),(0,r.jsx)(o.nd,{children:"Group"}),(0,r.jsx)(o.nd,{children:"Teacher"}),(0,r.jsx)(o.nd,{children:"Duration"}),(0,r.jsx)(o.nd,{children:"Price"}),(0,r.jsx)(o.nd,{children:"Start Date"}),(0,r.jsx)(o.nd,{children:"Status"}),(0,r.jsx)(o.nd,{children:"Actions"})]})}),(0,r.jsx)(o.BF,{children:R.map(e=>(0,r.jsxs)(o.Hj,{children:[(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-600"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student.user.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.student.user.phone})]})]})}),(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.group.course.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500",children:["Level: ",e.group.course.level]})]})}),(0,r.jsx)(o.nA,{children:(0,r.jsx)("span",{className:"font-medium",children:e.group.name})}),(0,r.jsx)(o.nA,{children:(0,r.jsx)("span",{className:"text-sm",children:e.group.teacher.user.name})}),(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"h-4 w-4 mr-1 text-gray-400"}),(0,r.jsxs)("span",{className:"text-sm",children:[e.group.course.duration," weeks"]})]})}),(0,r.jsx)(o.nA,{children:(0,r.jsx)("span",{className:"text-sm font-medium",children:$(e.group.course.price)})}),(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-1 text-gray-400"}),(0,r.jsx)("span",{className:"text-sm",children:(0,m.Yq)(e.startDate)})]})}),(0,r.jsx)(o.nA,{children:(0,r.jsx)(d.E,{className:G(e.status),children:(0,r.jsxs)("div",{className:"flex items-center",children:[z(e.status),(0,r.jsx)("span",{className:"ml-1",children:e.status})]})})}),(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{_(e),q(!0)},children:(0,r.jsx)(b.A,{className:"h-4 w-4"})}),(0,r.jsx)(n.$,{variant:"outline",size:"sm",onClick:async()=>{if(confirm("Are you sure you want to delete this enrollment?"))try{(await fetch(`/api/enrollments/${e.id}`,{method:"DELETE"})).ok&&Z()}catch(e){console.error("Error deleting enrollment:",e)}},className:"text-red-600 hover:text-red-700",children:(0,r.jsx)(w.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]})]})}},72730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},83281:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},88015:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>i});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),d=t(30893),c={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>d[e]);t.d(s,c);let i={children:["",{children:["(dashboard)",{children:["dashboard",{children:["enrollments",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,53396)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\enrollments\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\enrollments\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/enrollments/page",pathname:"/dashboard/enrollments",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:i}})},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},97795:(e,s,t)=>{Promise.resolve().then(t.bind(t,53396))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,8706,7825,3039,2671],()=>t(88015));module.exports=r})();