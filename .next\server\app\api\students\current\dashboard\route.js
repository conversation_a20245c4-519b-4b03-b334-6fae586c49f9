(()=>{var e={};e.id=1725,e.ids=[1725],e.modules={1049:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>E,routeModule:()=>l,serverHooks:()=>m,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),u=t(19854),c=t(41098),d=t(79464);async function p(e){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let r=await d.z.student.findUnique({where:{userId:e.user.id},include:{user:{select:{name:!0,email:!0,phone:!0}}}});if(!r)return i.NextResponse.json({error:"Student profile not found"},{status:404});let t=Math.round(90),s={A1:"A2",A2:"B1",B1:"B2",B2:"IELTS",IELTS:"Advanced",SAT:"SAT Advanced",MATH:"Advanced Math",KIDS:"Kids Advanced"}[r.level]||"Advanced",n={student:{name:r.user.name,level:r.level,nextLevel:s,branch:r.branch},progress:{overall:75,attendance:t,averageScore:75},stats:{totalClasses:20,attendedClasses:18,upcomingClasses:3,pendingAssignments:2},payments:{totalPayments:24e5,paidAmount:18e5,pendingAmount:6e5},recentClasses:[{date:"2024-01-15",topic:"Grammar Practice",status:"present"},{date:"2024-01-13",topic:"Vocabulary Building",status:"present"},{date:"2024-01-11",topic:"Reading Comprehension",status:"absent"},{date:"2024-01-09",topic:"Speaking Practice",status:"present"}],currentEnrollment:{groupName:"B1 Morning Group",courseName:"General English B1",teacherName:"Ms. Sarah Johnson"}};return i.NextResponse.json(n)}catch(e){return console.error("Error fetching student dashboard data:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let l=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/current/dashboard/route",pathname:"/api/students/current/dashboard",filename:"route",bundlePath:"app/api/students/current/dashboard/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\current\\dashboard\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:v,serverHooks:m}=l;function E(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:v})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=o.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},a=await fetch(s,{method:r.method,headers:n,body:r.data?JSON.stringify(r.data):void 0}),i=await a.json();return{success:a.ok,data:i,status:a.status,error:a.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>o,cU:()=>a,g2:()=>s});class a{static async authenticateUser(e,r){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class o{static logRequest(e,r,t,s){let n=new Date().toISOString(),a=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:a,success:t,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41098:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(13581),n=t(7786);let a={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let r=await n.cU.authenticateUser(e.phone,e.password);if(!r.success)return console.error("Authentication failed:",r.error),null;let t=r.data.user;if(!t)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(t.role))return console.error("User role not allowed on staff server:",t.role),null;return{id:t.id,phone:t.phone,name:t.name,email:t.email,role:t.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role||null),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3412],()=>t(1049));module.exports=s})();