(()=>{var e={};e.id=8497,e.ids=[8497],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15616:(e,s,a)=>{"use strict";a.d(s,{T:()=>n});var t=a(60687),r=a(43210),l=a(96241);let n=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("textarea",{className:(0,l.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...s}));n.displayName="Textarea"},18847:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var t=a(60687),r=a(43210),l=a(96545),n=a(24934),i=a(26269),d=a(3018),c=a(78122),o=a(63851),h=a(18962);let x=(0,h.A)("Archive",[["rect",{width:"20",height:"5",x:"2",y:"3",rx:"1",key:"1wp1u1"}],["path",{d:"M4 8v11a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8",key:"1s80jp"}],["path",{d:"M10 12h4",key:"a56b0p"}]]);var u=a(68988),m=a(39390),p=a(55192),g=a(40228),v=a(78272),j=a(11860),f=a(96241);let b=[{value:"today",label:"Today"},{value:"yesterday",label:"Yesterday"},{value:"last7days",label:"Last 7 days"},{value:"last30days",label:"Last 30 days"},{value:"custom",label:"Custom range"}];function N({onFilterChange:e,currentFilter:s}){let[a,l]=(0,r.useState)(!1),[i,d]=(0,r.useState)(s||""),[c,o]=(0,r.useState)(""),[h,x]=(0,r.useState)(""),[N,y]=(0,r.useState)(!1),w=s=>{if(d(s),"custom"===s)return void y(!0);y(!1),e({dateFilter:s}),l(!1)};return(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsxs)(n.$,{variant:"outline",onClick:()=>l(!a),className:"flex items-center gap-2 min-w-[200px] justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"truncate",children:(()=>{if(!i)return"All time";let e=b.find(e=>e.value===i);return e&&"custom"!==e.value?e.label:c&&h?`${(0,f.Yq)(c)} - ${(0,f.Yq)(h)}`:"Select date range"})()})]}),(0,t.jsx)(v.A,{className:"h-4 w-4"})]}),a&&(0,t.jsx)(p.Zp,{className:"absolute top-full left-0 mt-1 z-50 min-w-[300px]",children:(0,t.jsx)(p.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("h4",{className:"font-medium",children:"Filter by date"}),(0,t.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>l(!1),className:"h-6 w-6 p-0",children:(0,t.jsx)(j.A,{className:"h-4 w-4"})})]}),(0,t.jsx)("div",{className:"space-y-2",children:b.map(e=>(0,t.jsx)(n.$,{variant:i===e.value?"default":"ghost",className:"w-full justify-start",onClick:()=>w(e.value),children:e.label},e.value))}),N&&(0,t.jsxs)("div",{className:"space-y-3 pt-3 border-t",children:[(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"start-date",className:"text-xs",children:"Start Date"}),(0,t.jsx)(u.p,{id:"start-date",type:"date",value:c,onChange:e=>o(e.target.value),className:"text-sm"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"end-date",className:"text-xs",children:"End Date"}),(0,t.jsx)(u.p,{id:"end-date",type:"date",value:h,onChange:e=>x(e.target.value),className:"text-sm"})]})]}),(0,t.jsx)(n.$,{onClick:()=>{c&&h&&(e({startDate:c,endDate:h}),l(!1),y(!1))},disabled:!c||!h,className:"w-full",size:"sm",children:"Apply Custom Range"})]}),i&&(0,t.jsx)("div",{className:"pt-3 border-t",children:(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{d(""),o(""),x(""),y(!1),e({}),l(!1)},className:"w-full",size:"sm",children:"Clear Filter"})})]})})})]})}var y=a(59821),w=a(37826),A=a(96752),C=a(58869),E=a(48340),k=a(48730),L=a(36058);let S=(0,h.A)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]]);var $=a(31158),D=a(41312),F=a(15616);let T=(0,h.A)("PhoneOff",[["path",{d:"M10.68 13.31a16 16 0 0 0 3.41 2.6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7 2 2 0 0 1 1.72 2v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.42 19.42 0 0 1-3.33-2.67m-2.67-3.34a19.79 19.79 0 0 1-3.07-8.63A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91",key:"z86iuo"}],["line",{x1:"22",x2:"2",y1:"2",y2:"22",key:"11kh81"}]]),P=(0,h.A)("Square",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}]]),_=(0,h.A)("Mic",[["path",{d:"M12 2a3 3 0 0 0-3 3v7a3 3 0 0 0 6 0V5a3 3 0 0 0-3-3Z",key:"131961"}],["path",{d:"M19 10v2a7 7 0 0 1-14 0v-2",key:"1vc78b"}],["line",{x1:"12",x2:"12",y1:"19",y2:"22",key:"x3vr5v"}]]);function R({leadId:e,leadName:s,leadPhone:a,onCallComplete:l,onError:i}){let[d,c]=(0,r.useState)(!1),[o,h]=(0,r.useState)(0),[x,u]=(0,r.useState)(""),[g,v]=(0,r.useState)(!1),[j,f]=(0,r.useState)(!1),b=(0,r.useRef)(null),N=()=>{b.current=setInterval(()=>{h(e=>e+1)},1e3)},w=()=>{b.current&&(clearInterval(b.current),b.current=null)},A=(0,r.useCallback)(async()=>{v(!0),w(),f(!1);try{let s=j?`https://example.com/recordings/call-${e}-${Date.now()}.mp3`:void 0,a=await fetch(`/api/leads/${e}/call`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({duration:o,notes:x,recordingUrl:s})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to end call")}c(!1),h(0),l()}catch(e){i(e instanceof Error?e.message:"Failed to end call"),d&&N()}finally{v(!1)}},[e,o,x,l,i,d,j]),C=async()=>{v(!0);try{let s=await fetch(`/api/leads/${e}/call`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:x})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to start call")}c(!0),h(0),f(!0),N()}catch(e){i(e instanceof Error?e.message:"Failed to start call")}finally{v(!1)}},L=e=>{let s=Math.floor(e/60);return`${s}:${(e%60).toString().padStart(2,"0")}`};return(0,t.jsxs)(p.Zp,{className:"w-full max-w-md",children:[(0,t.jsx)(p.aR,{children:(0,t.jsxs)(p.ZB,{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-5 w-5"}),"Call Management"]}),d&&(0,t.jsx)(y.E,{className:o>=270?"bg-red-100 text-red-800":o>=240?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-current rounded-full animate-pulse"}),j?"Recording":"Active"]})})]})}),(0,t.jsxs)(p.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h3",{className:"font-semibold text-lg",children:s}),(0,t.jsx)("p",{className:"text-gray-600",children:a})]}),d&&(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2 text-2xl font-mono",children:[(0,t.jsx)(k.A,{className:"h-6 w-6"}),L(o)]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Auto-end in ",L(300-o)]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(m.J,{htmlFor:"call-notes",children:"Call Notes"}),(0,t.jsx)(F.T,{id:"call-notes",placeholder:"Add notes about the call...",value:x,onChange:e=>u(e.target.value),disabled:g,rows:3})]}),(0,t.jsx)("div",{className:"flex gap-2",children:d?(0,t.jsx)(n.$,{onClick:A,disabled:g,className:"flex-1 bg-red-600 hover:bg-red-700",children:g?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Ending..."]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(T,{className:"h-4 w-4"}),"End Call"]})}):(0,t.jsx)(n.$,{onClick:C,disabled:g,className:"flex-1 bg-green-600 hover:bg-green-700",children:g?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Starting..."]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(E.A,{className:"h-4 w-4"}),"Start Call"]})})}),d&&(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>f(!j),className:j?"bg-red-50 border-red-200":"",children:j?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(P,{className:"h-3 w-3 text-red-600"}),"Stop Recording"]}):(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(_,{className:"h-3 w-3"}),"Start Recording"]})})}),j&&(0,t.jsx)("div",{className:"text-xs text-gray-500 text-center",children:(0,t.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,t.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),"Call is being recorded"]})})]})]})]})}var G=a(63974),M=a(99270),I=a(97992);let O=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},U=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";case"NEW":return"New";default:return"Unknown"}};function V({isOpen:e,onClose:s,leadId:a,leadName:i,onAssignmentComplete:d,onError:c}){let{currentBranch:o}=(0,l.O)(),[h,x]=(0,r.useState)([]),[v,j]=(0,r.useState)([]),[f,b]=(0,r.useState)([]),[N,A]=(0,r.useState)(""),[E,L]=(0,r.useState)(""),[S,$]=(0,r.useState)(""),[T,P]=(0,r.useState)(""),[_,R]=(0,r.useState)(""),[V,q]=(0,r.useState)(""),[z,W]=(0,r.useState)(!1),[J,B]=(0,r.useState)(!1),[H,Z]=(0,r.useState)(!1),X=Array.from(new Set(h.map(e=>e.teacher.user.name))).sort(),K=Array.from(new Set(h.map(e=>e.course.level))).sort();(0,r.useCallback)(async()=>{W(!0);try{let e=await fetch(`/api/leads/${a}/assign-group?branch=${o.id}`);if(!e.ok)throw Error("Failed to fetch available groups");let s=await e.json();x(s.groups),b(s.slotAnalysis||[])}catch(e){c(e instanceof Error?e.message:"Failed to fetch groups")}finally{W(!1)}},[a,o.id,c]),(0,r.useCallback)(()=>{let e=h;E&&(e=e.filter(e=>e.name.toLowerCase().includes(E.toLowerCase())||e.course.name.toLowerCase().includes(E.toLowerCase())||e.teacher.user.name.toLowerCase().includes(E.toLowerCase()))),S&&(e=e.filter(e=>e.teacher.user.name===S)),T&&(e=e.filter(e=>e.course.level===T)),_&&(e=e.filter(e=>e.teacher.tier===_)),j(e)},[h,E,S,T,_]);let Y=async()=>{if(N){B(!0);try{let e=await fetch(`/api/leads/${a}/assign-group`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({groupId:N,notes:V})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to assign group")}d(),s()}catch(e){c(e instanceof Error?e.message:"Failed to assign group")}finally{B(!1)}}},Q=e=>{try{let s=JSON.parse(e);return Array.isArray(s)?s.join(", "):e}catch{return e}},ee=(e,s)=>{let a=e/s*100;return a>=90?"bg-red-100 text-red-800":a>=70?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"};return H?(0,t.jsx)(w.lG,{open:e,onOpenChange:s,children:(0,t.jsxs)(w.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,t.jsx)(w.c7,{children:(0,t.jsxs)(w.L3,{children:["Assign Group to ",i]})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"search",children:"Search Groups"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(M.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(u.p,{id:"search",placeholder:"Search by name, course, teacher...",value:E,onChange:e=>L(e.target.value),className:"pl-10"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"teacher-filter",children:"Filter by Teacher"}),(0,t.jsxs)(G.l6,{value:S||"all",onValueChange:e=>$("all"===e?"":e),children:[(0,t.jsx)(G.bq,{children:(0,t.jsx)(G.yv,{placeholder:"All teachers"})}),(0,t.jsxs)(G.gC,{children:[(0,t.jsx)(G.eb,{value:"all",children:"All teachers"}),X.map(e=>(0,t.jsx)(G.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"level-filter",children:"Filter by Level"}),(0,t.jsxs)(G.l6,{value:T||"all",onValueChange:e=>P("all"===e?"":e),children:[(0,t.jsx)(G.bq,{children:(0,t.jsx)(G.yv,{placeholder:"All levels"})}),(0,t.jsxs)(G.gC,{children:[(0,t.jsx)(G.eb,{value:"all",children:"All levels"}),K.map(e=>(0,t.jsx)(G.eb,{value:e,children:e},e))]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"tier-filter",children:"Filter by Teacher Tier"}),(0,t.jsxs)(G.l6,{value:_||"all",onValueChange:e=>R("all"===e?"":e),children:[(0,t.jsx)(G.bq,{children:(0,t.jsx)(G.yv,{placeholder:"All tiers"})}),(0,t.jsxs)(G.gC,{children:[(0,t.jsx)(G.eb,{value:"all",children:"All tiers"}),(0,t.jsx)(G.eb,{value:"A_LEVEL",children:"A-Level Teachers"}),(0,t.jsx)(G.eb,{value:"B_LEVEL",children:"B-Level Teachers"}),(0,t.jsx)(G.eb,{value:"C_LEVEL",children:"C-Level Teachers"}),(0,t.jsx)(G.eb,{value:"NEW",children:"New Teachers"})]})]})]}),(0,t.jsx)("div",{className:"flex items-end",children:(0,t.jsx)(n.$,{variant:"outline",onClick:()=>{L(""),$(""),P(""),R("")},className:"w-full",children:"Clear Filters"})})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("h3",{className:"font-medium",children:["Available Groups (",v.length,")"]}),z?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,t.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading groups..."})]}):0===v.length?(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No available groups found"}):(0,t.jsx)("div",{className:"grid gap-3 max-h-60 overflow-y-auto",children:v.map(e=>(0,t.jsx)(p.Zp,{className:`cursor-pointer transition-colors ${N===e.id?"ring-2 ring-blue-500 bg-blue-50":"hover:bg-gray-50"}`,onClick:()=>A(e.id),children:(0,t.jsx)(p.Wu,{className:"p-4",children:(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("h4",{className:"font-medium",children:e.name}),(0,t.jsxs)(y.E,{variant:"outline",children:[e.course.name," - ",e.course.level]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(C.A,{className:"h-3 w-3"}),e.teacher.user.name]}),(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${O(e.teacher.tier||"NEW")}`,children:U(e.teacher.tier||"NEW")})]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(I.A,{className:"h-3 w-3"}),e.room||"TBA"," - ",e.branch]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),Q(e.schedule)]}),(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"h-3 w-3"}),new Date(e.startDate).toLocaleDateString()]})]})]}),(0,t.jsxs)(y.E,{className:ee(e._count.enrollments,e.capacity),children:[(0,t.jsx)(D.A,{className:"h-3 w-3 mr-1"}),e._count.enrollments,"/",e.capacity]})]})})},e.id))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(m.J,{htmlFor:"assignment-notes",children:"Assignment Notes (Optional)"}),(0,t.jsx)(F.T,{id:"assignment-notes",placeholder:"Add any notes about this group assignment...",value:V,onChange:e=>q(e.target.value),rows:3})]}),(0,t.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,t.jsx)(n.$,{variant:"outline",onClick:s,children:"Cancel"}),(0,t.jsx)(n.$,{onClick:Y,disabled:!N||J,children:J?(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Assigning..."]}):"Assign Group"})]})]})]})}):null}function q({leads:e,onLeadUpdate:s,onError:a,isArchiveView:l=!1}){let[i,d]=(0,r.useState)(null),[c,o]=(0,r.useState)(null),[h,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),[v,j]=(0,r.useState)(null),b=e=>{switch(e){case"NEW":return"bg-blue-100 text-blue-800";case"CALLING":return"bg-yellow-100 text-yellow-800";case"CALL_COMPLETED":return"bg-green-100 text-green-800";case"GROUP_ASSIGNED":return"bg-purple-100 text-purple-800";case"ARCHIVED":default:return"bg-gray-100 text-gray-800";case"NOT_INTERESTED":return"bg-red-100 text-red-800"}},N=e=>e.replace("_"," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()),F=e=>"NEW"===e.status&&!l,T=e=>"CALL_COMPLETED"===e.status&&!l,P=async e=>{try{let a=await fetch(`/api/leads/${e}/archive`,{method:"POST"});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to archive lead")}s()}catch(e){a(e instanceof Error?e.message:"Failed to archive lead")}},_=e=>{if(!e)return"N/A";let s=Math.floor(e/60);return`${s}m ${e%60}s`},G=e=>{if(v===e&&m){m.pause(),p(null),j(null);return}m&&(m.pause(),m.currentTime=0);let s=new Audio(e);p(s),j(e),s.play().catch(e=>{console.error("Error playing audio:",e),a("Failed to play recording: "+e.message),p(null),j(null)}),s.addEventListener("ended",()=>{p(null),j(null)}),s.addEventListener("error",()=>{p(null),j(null),a("Error loading audio file")})},M=(e,s,a)=>{let t=document.createElement("a");t.href=e,t.download=`call-recording-${s}-${a}.mp3`,document.body.appendChild(t),t.click(),document.body.removeChild(t)};return h?0===e.length?(0,t.jsx)("div",{className:"text-center py-12",children:(0,t.jsx)("div",{className:"text-gray-500",children:l?"No archived leads found":"No leads found"})}):(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsxs)(A.XI,{children:[(0,t.jsx)(A.A0,{children:(0,t.jsxs)(A.Hj,{children:[(0,t.jsx)(A.nd,{children:"Lead Info"}),(0,t.jsx)(A.nd,{children:"Status"}),(0,t.jsx)(A.nd,{children:"Call Info"}),(0,t.jsx)(A.nd,{children:"Recordings"}),(0,t.jsx)(A.nd,{children:"Assigned Group"}),(0,t.jsx)(A.nd,{children:"Actions"})]})}),(0,t.jsx)(A.BF,{children:e.map(e=>(0,t.jsxs)(A.Hj,{className:"hover:bg-gray-50",children:[(0,t.jsx)(A.nA,{children:(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"font-medium flex items-center gap-2",children:[(0,t.jsx)(C.A,{className:"h-4 w-4"}),e.name]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600 flex items-center gap-1",children:[(0,t.jsx)(E.A,{className:"h-3 w-3"}),e.phone]}),(0,t.jsxs)("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,t.jsx)(g.A,{className:"h-3 w-3"}),(0,f.Yq)(e.createdAt)]}),e.source&&(0,t.jsxs)("div",{className:"text-xs text-gray-500",children:["Source: ",e.source]})]})}),(0,t.jsx)(A.nA,{children:(0,t.jsx)(y.E,{className:b(e.status),children:N(e.status)})}),(0,t.jsx)(A.nA,{children:e.callDuration?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsxs)("div",{className:"text-sm flex items-center gap-1",children:[(0,t.jsx)(k.A,{className:"h-3 w-3"}),_(e.callDuration)]}),e.callStartedAt&&(0,t.jsx)("div",{className:"text-xs text-gray-500",children:(0,f.r6)(e.callStartedAt)})]}):(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"No calls"})}),(0,t.jsx)(A.nA,{children:e.callRecords&&e.callRecords.length>0?(0,t.jsx)("div",{className:"space-y-2",children:e.callRecords.map(s=>(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[s.recordingUrl?(0,t.jsxs)("div",{className:"flex gap-1",children:[(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>G(s.recordingUrl),className:`h-6 w-6 p-0 ${v===s.recordingUrl?"bg-green-100 border-green-300":""}`,children:v===s.recordingUrl?(0,t.jsx)(L.A,{className:"h-3 w-3"}):(0,t.jsx)(S,{className:"h-3 w-3"})}),(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>M(s.recordingUrl,e.name,s.id),className:"h-6 w-6 p-0",children:(0,t.jsx)($.A,{className:"h-3 w-3"})})]}):(0,t.jsx)("span",{className:"text-xs text-gray-400",children:"No recording"}),(0,t.jsx)("div",{className:"text-xs text-gray-500",children:_(s.duration)})]},s.id))}):(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"No recordings"})}),(0,t.jsx)(A.nA,{children:e.assignedGroup?(0,t.jsxs)("div",{className:"space-y-1",children:[(0,t.jsx)("div",{className:"text-sm font-medium",children:e.assignedGroup.name}),(0,t.jsxs)("div",{className:"text-xs text-gray-600",children:[e.assignedGroup.course.name," - ",e.assignedGroup.course.level]}),(0,t.jsxs)("div",{className:"text-xs text-gray-600 flex items-center gap-1",children:[(0,t.jsx)(C.A,{className:"h-3 w-3"}),e.assignedGroup.teacher.user.name]}),e.assignedAt&&(0,t.jsx)("div",{className:"text-xs text-gray-500",children:(0,f.r6)(e.assignedAt)})]}):(0,t.jsx)("span",{className:"text-gray-400 text-sm",children:"Not assigned"})}),(0,t.jsx)(A.nA,{children:(0,t.jsxs)("div",{className:"flex gap-1",children:[F(e)&&(0,t.jsxs)(w.lG,{children:[(0,t.jsx)(w.zM,{asChild:!0,children:(0,t.jsx)(n.$,{size:"sm",className:"bg-green-600 hover:bg-green-700",children:(0,t.jsx)(E.A,{className:"h-3 w-3"})})}),(0,t.jsx)(w.Cf,{children:(0,t.jsx)(R,{leadId:e.id,leadName:e.name,leadPhone:e.phone,onCallComplete:()=>{s(),d(null)},onError:a})})]}),T(e)&&(0,t.jsx)(n.$,{size:"sm",onClick:()=>o(e),className:"bg-purple-600 hover:bg-purple-700",children:(0,t.jsx)(D.A,{className:"h-3 w-3"})}),"GROUP_ASSIGNED"===e.status&&!l&&(0,t.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>P(e.id),children:(0,t.jsx)(x,{className:"h-3 w-3"})})]})})]},e.id))})]})}),c&&(0,t.jsx)(V,{isOpen:!!c,onClose:()=>o(null),leadId:c.id,leadName:c.name,onAssignmentComplete:s,onError:a})]}):(0,t.jsx)("div",{className:"space-y-4",children:(0,t.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,t.jsx)("div",{className:"p-8 text-center text-gray-500",children:"Loading leads..."})})})}function z(){let{currentBranch:e}=(0,l.O)(),[s,a]=(0,r.useState)([]),[h,u]=(0,r.useState)([]),[m,p]=(0,r.useState)(!0),[g,v]=(0,r.useState)("ALL"),[j,f]=(0,r.useState)({}),[b,y]=(0,r.useState)(null),[w,A]=(0,r.useState)("active"),C=(0,r.useCallback)(async(s=!1)=>{try{p(!0);let t=new URLSearchParams;"ALL"!==g&&t.append("status",g),j.dateFilter?t.append("dateFilter",j.dateFilter):j.startDate&&j.endDate&&(t.append("startDate",j.startDate),t.append("endDate",j.endDate)),t.append("archived",s.toString()),t.append("branch",e.id);let r=`/api/leads?${t.toString()}`,l=await fetch(r);if(!l.ok)throw Error("Failed to fetch leads");let n=await l.json();s?u(n.leads||[]):a(n.leads||[])}catch(e){console.error("Error fetching leads:",e),y(e instanceof Error?e.message:"Failed to fetch leads")}finally{p(!1)}},[g,j,e.id]),E=()=>{C("archived"===w),y(null)},k=e=>{y(e)};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Leads Management - ",e.name]}),(0,t.jsx)("p",{className:"text-gray-600",children:"Comprehensive lead tracking with call management and group assignment"})]}),(0,t.jsxs)(n.$,{onClick:()=>{C("archived"===w)},variant:"outline",size:"sm",children:[(0,t.jsx)(c.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),b&&(0,t.jsxs)(d.Fc,{variant:"destructive",children:[(0,t.jsx)(o.A,{className:"h-4 w-4"}),(0,t.jsx)(d.TN,{children:b})]}),(0,t.jsxs)(i.tU,{value:w,onValueChange:A,className:"space-y-6",children:[(0,t.jsxs)(i.j7,{children:[(0,t.jsx)(i.Xi,{value:"active",children:"Active Leads"}),(0,t.jsxs)(i.Xi,{value:"archived",className:"flex items-center gap-2",children:[(0,t.jsx)(x,{className:"h-4 w-4"}),"Archive"]})]}),(0,t.jsxs)(i.av,{value:"active",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,t.jsx)(N,{onFilterChange:e=>{f(e)},currentFilter:j.dateFilter}),(0,t.jsx)("div",{className:"flex gap-2",children:["ALL","NEW","CALLING","CALL_COMPLETED","GROUP_ASSIGNED","NOT_INTERESTED"].map(e=>(0,t.jsx)(n.$,{variant:g===e?"default":"outline",onClick:()=>v(e),size:"sm",children:e.replace("_"," ")},e))})]}),m?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading leads..."})]})}):(0,t.jsx)(q,{leads:s,onLeadUpdate:E,onError:k,isArchiveView:!1})]}),(0,t.jsxs)(i.av,{value:"archived",className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold",children:"Archived Leads"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Leads that have been successfully assigned to groups"})]})}),m?(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Loading archived leads..."})]})}):(0,t.jsx)(q,{leads:h,onLeadUpdate:E,onError:k,isArchiveView:!0})]})]})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26269:(e,s,a)=>{"use strict";a.d(s,{tU:()=>S,av:()=>F,j7:()=>$,Xi:()=>D});var t=a(60687),r=a(43210),l=a(70569),n=a(11273),i=a(72942),d=a(46059),c=a(14163),o=a(43),h=a(65551),x=a(96963),u="Tabs",[m,p]=(0,n.A)(u,[i.RG]),g=(0,i.RG)(),[v,j]=m(u),f=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:l,defaultValue:n,orientation:i="horizontal",dir:d,activationMode:m="automatic",...p}=e,g=(0,o.jH)(d),[j,f]=(0,h.i)({prop:r,onChange:l,defaultProp:n??"",caller:u});return(0,t.jsx)(v,{scope:a,baseId:(0,x.B)(),value:j,onValueChange:f,orientation:i,dir:g,activationMode:m,children:(0,t.jsx)(c.sG.div,{dir:g,"data-orientation":i,...p,ref:s})})});f.displayName=u;var b="TabsList",N=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...l}=e,n=j(b,a),d=g(a);return(0,t.jsx)(i.bL,{asChild:!0,...d,orientation:n.orientation,dir:n.dir,loop:r,children:(0,t.jsx)(c.sG.div,{role:"tablist","aria-orientation":n.orientation,...l,ref:s})})});N.displayName=b;var y="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:n=!1,...d}=e,o=j(y,a),h=g(a),x=E(o.baseId,r),u=k(o.baseId,r),m=r===o.value;return(0,t.jsx)(i.q7,{asChild:!0,...h,focusable:!n,active:m,children:(0,t.jsx)(c.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":n?"":void 0,disabled:n,id:x,...d,ref:s,onMouseDown:(0,l.m)(e.onMouseDown,e=>{n||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,l.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,l.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;m||n||!e||o.onValueChange(r)})})})});w.displayName=y;var A="TabsContent",C=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:l,forceMount:n,children:i,...o}=e,h=j(A,a),x=E(h.baseId,l),u=k(h.baseId,l),m=l===h.value,p=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(d.C,{present:n||m,children:({present:a})=>(0,t.jsx)(c.sG.div,{"data-state":m?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&i})})});function E(e,s){return`${e}-trigger-${s}`}function k(e,s){return`${e}-content-${s}`}C.displayName=A;var L=a(96241);let S=f,$=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(N,{ref:a,className:(0,L.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));$.displayName=N.displayName;let D=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(w,{ref:a,className:(0,L.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));D.displayName=w.displayName;let F=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(C,{ref:a,className:(0,L.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));F.displayName=C.displayName},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31158:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},33873:e=>{"use strict";e.exports=require("path")},48730:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},52337:(e,s,a)=>{Promise.resolve().then(a.bind(a,18847))},53526:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\leads\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\leads\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78122:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},78148:(e,s,a)=>{"use strict";a.d(s,{b:()=>i});var t=a(43210),r=a(14163),l=a(60687),n=t.forwardRef((e,s)=>(0,l.jsx)(r.sG.label,{...e,ref:s,onMouseDown:s=>{s.target.closest("button, input, select, textarea")||(e.onMouseDown?.(s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));n.displayName="Label";var i=n},87185:(e,s,a)=>{Promise.resolve().then(a.bind(a,53526))},95615:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>n.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>x,tree:()=>c});var t=a(65239),r=a(48088),l=a(88170),n=a.n(l),i=a(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["leads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,53526)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\leads\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\leads\\page.tsx"],h={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/leads/page",pathname:"/dashboard/leads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},97992:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,8706,3039,2671],()=>a(95615));module.exports=t})();