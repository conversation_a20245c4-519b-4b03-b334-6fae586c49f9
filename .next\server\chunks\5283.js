"use strict";exports.id=5283,exports.ids=[5283],exports.modules={25283:(e,s,a)=>{a.d(s,{A:()=>w});var l=a(60687),r=a(43210),t=a(27605),c=a(63442),n=a(9275),i=a(24934),d=a(68988),m=a(39390),h=a(63974),o=a(15616),x=a(55192),j=a(3018),p=a(58869),u=a(48340),v=a(41550),N=a(40228),b=a(97992),y=a(27351),g=a(72730),f=a(96545);let A=n.Ik({name:n.Yj().min(2,"Name must be at least 2 characters"),phone:n.Yj().min(9,"Phone number must be at least 9 characters"),email:n.Yj().email("Invalid email address").optional().or(n.eu("")),level:n.k5(["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"]),branch:n.Yj().min(1,"Branch is required"),emergencyContact:n.Yj().optional(),dateOfBirth:n.Yj().optional(),address:n.Yj().optional()}),B=[{value:"A1",label:"A1 - Beginner"},{value:"A2",label:"A2 - Elementary"},{value:"B1",label:"B1 - Intermediate"},{value:"B2",label:"B2 - Upper Intermediate"},{value:"IELTS",label:"IELTS"},{value:"SAT",label:"SAT Preparation"},{value:"MATH",label:"Mathematics"},{value:"KIDS",label:"Kids English"}],S=["Main Branch","Branch"];function w({initialData:e,onSubmit:s,onCancel:a,isEditing:n=!1}){let{currentBranch:w}=(0,f.O)(),[E,C]=(0,r.useState)(!1),[F,T]=(0,r.useState)(null),{register:I,handleSubmit:k,setValue:O,watch:J,formState:{errors:M}}=(0,t.mN)({resolver:(0,c.u)(A),defaultValues:{name:e?.name||"",phone:e?.phone||"",email:e?.email||"",level:e?.level||"A1",branch:e?.branch||w.name,emergencyContact:e?.emergencyContact||"",dateOfBirth:e?.dateOfBirth||"",address:e?.address||""}}),Y=J("level"),P=J("branch"),q=async e=>{C(!0),T(null);try{if(!e.branch)throw Error("Branch is required");await s(e)}catch(e){console.error("Form submission error:",e),T(e instanceof Error?e.message:"An error occurred")}finally{C(!1)}};return(0,l.jsxs)(x.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,l.jsxs)(x.aR,{children:[(0,l.jsxs)(x.ZB,{className:"flex items-center",children:[(0,l.jsx)(p.A,{className:"h-5 w-5 mr-2"}),n?"Edit Student":"Add New Student"]}),(0,l.jsx)(x.BT,{children:n?"Update student information":"Enter student details to create a new profile"})]}),(0,l.jsx)(x.Wu,{children:(0,l.jsxs)("form",{onSubmit:k(q),className:"space-y-6",children:[F&&(0,l.jsx)(j.Fc,{variant:"destructive",children:(0,l.jsx)(j.TN,{children:F})}),Object.keys(M).length>0&&(0,l.jsx)(j.Fc,{variant:"destructive",children:(0,l.jsxs)(j.TN,{children:["Please fix the following errors:",(0,l.jsx)("ul",{className:"mt-2 list-disc list-inside",children:Object.entries(M).map(([e,s])=>(0,l.jsx)("li",{children:s?.message},e))})]})}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(p.A,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Personal Information"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"name",children:"Full Name *"}),(0,l.jsx)(d.p,{id:"name",...I("name"),placeholder:"Enter full name",className:M.name?"border-red-500":""}),M.name&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:M.name.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"phone",children:"Phone Number *"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"phone",...I("phone"),placeholder:"+998 90 123 45 67",className:`pl-10 ${M.phone?"border-red-500":""}`})]}),M.phone&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:M.phone.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"email",children:"Email Address"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"email",type:"email",...I("email"),placeholder:"<EMAIL>",className:`pl-10 ${M.email?"border-red-500":""}`})]}),M.email&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:M.email.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"dateOfBirth",children:"Date of Birth"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(N.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"dateOfBirth",type:"date",...I("dateOfBirth"),className:"pl-10"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"address",children:"Address"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(b.A,{className:"absolute left-3 top-3 h-4 w-4 text-gray-400"}),(0,l.jsx)(o.T,{id:"address",...I("address"),placeholder:"Enter full address",className:"pl-10 min-h-[80px]"})]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"emergencyContact",children:"Emergency Contact"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(u.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(d.p,{id:"emergencyContact",...I("emergencyContact"),placeholder:"Emergency contact number",className:"pl-10"})]})]})]}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,l.jsx)(y.A,{className:"h-4 w-4 text-gray-500"}),(0,l.jsx)("h3",{className:"text-lg font-medium",children:"Academic Information"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"level",children:"English Level *"}),(0,l.jsxs)(h.l6,{value:Y,onValueChange:e=>O("level",e),children:[(0,l.jsx)(h.bq,{className:M.level?"border-red-500":"",children:(0,l.jsx)(h.yv,{placeholder:"Select level"})}),(0,l.jsx)(h.gC,{children:B.map(e=>(0,l.jsx)(h.eb,{value:e.value,children:e.label},e.value))})]}),M.level&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:M.level.message})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"branch",children:"Branch *"}),(0,l.jsxs)(h.l6,{value:P,onValueChange:e=>O("branch",e),children:[(0,l.jsx)(h.bq,{className:M.branch?"border-red-500":"",children:(0,l.jsx)(h.yv,{placeholder:"Select branch"})}),(0,l.jsx)(h.gC,{children:S.map(e=>(0,l.jsx)(h.eb,{value:e,children:e},e))})]}),M.branch&&(0,l.jsx)("p",{className:"text-sm text-red-500",children:M.branch.message})]})]})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[a&&(0,l.jsx)(i.$,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,l.jsxs)(i.$,{type:"submit",disabled:E,children:[E&&(0,l.jsx)(g.A,{className:"mr-2 h-4 w-4 animate-spin"}),n?"Update Student":"Create Student"]})]})]})})]})}},41550:(e,s,a)=>{a.d(s,{A:()=>l});let l=(0,a(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},97992:(e,s,a)=>{a.d(s,{A:()=>l});let l=(0,a(18962).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};