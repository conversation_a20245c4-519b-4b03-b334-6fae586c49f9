{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/middleware.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api\\/auth|_next\\/static|_next\\/image|favicon.ico|public).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api/auth|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "r_7aIim_9tja0d2szQM3k", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Ch1cDn+BG5IYPLdK2u8ZKmurL9LkvXtyk0kVrS8G9wk=", "__NEXT_PREVIEW_MODE_ID": "1bb56eee7f4a5fbb264f691a6bd99cbe", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "989d3acf05f4375941def9de52c7fc67fcf565b9afa9ff860ef38f2e4315eca8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "718c3fc2e5df613ca69fad338ad6843c29e4c607c1d0fe7f66ed4c2b57226307"}}}, "functions": {}, "sortedMiddleware": ["/"]}