(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{2657:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2714:(e,r,t)=>{"use strict";t.d(r,{J:()=>d});var s=t(5155),a=t(2115),n=t(968),l=t(2085),i=t(3999);let o=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)(n.b,{ref:r,className:(0,i.cn)(o(),t),...a})});d.displayName=n.b.displayName},2895:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,r)=>{let t=(0,s.forwardRef)((t,l)=>{let{color:i="currentColor",size:o=24,strokeWidth:d=2,absoluteStrokeWidth:c,className:u="",children:m,...f}=t;return(0,s.createElement)("svg",{ref:l,...a,width:o,height:o,stroke:i,strokeWidth:c?24*Number(d)/Number(o):d,className:["lucide","lucide-".concat(n(e)),u].join(" "),...f},[...r.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(m)?m:[m]])});return t.displayName="".concat(e),t}},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>o,sG:()=>i});var s=t(2115),a=t(7650),n=t(9708),l=t(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?t:r,{...n,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function o(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},3999:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>i,cn:()=>n,r6:()=>o,vv:()=>l});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}function l(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)}function o(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}},5040:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},5695:(e,r,t)=>{"use strict";var s=t(8999);t.o(s,"useParams")&&t.d(r,{useParams:function(){return s.useParams}}),t.o(s,"usePathname")&&t.d(r,{usePathname:function(){return s.usePathname}}),t.o(s,"useRouter")&&t.d(r,{useRouter:function(){return s.useRouter}}),t.o(s,"useSearchParams")&&t.d(r,{useSearchParams:function(){return s.useSearchParams}})},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>l,t:()=>n});var s=t(2115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function l(...e){return s.useCallback(n(...e),e)}},7168:(e,r,t)=>{"use strict";t.d(r,{$:()=>d,r:()=>o});var s=t(5155),a=t(2115),n=t(9708),l=t(2085),i=t(3999);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,r)=>{let{className:t,variant:a,size:l,asChild:d=!1,...c}=e,u=d?n.DX:"button";return(0,s.jsx)(u,{className:(0,i.cn)(o({variant:a,size:l,className:t})),ref:r,...c})});d.displayName="Button"},7624:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},8341:(e,r,t)=>{Promise.resolve().then(t.bind(t,9724))},8482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>l,aR:()=>i});var s=t(5155),a=t(2115),n=t(3999);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...a})});l.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});o.displayName="CardTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});d.displayName="CardDescription";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});c.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},8533:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("AlertCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8749:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},9026:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>o,TN:()=>d});var s=t(5155),a=t(2115),n=t(2085),l=t(3999);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),o=a.forwardRef((e,r)=>{let{className:t,variant:a,...n}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(i({variant:a}),t),...n})});o.displayName="Alert",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",t),...a})});d.displayName="AlertDescription"},9420:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,Dc:()=>d,TL:()=>l});var s=t(2115),a=t(6101),n=t(5155);function l(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var l;let e,i,o=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(d.ref=r?(0,a.t)(r,o):o),s.cloneElement(t,d)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...l}=e,i=s.Children.toArray(a),o=i.find(c);if(o){let e=o.props.children,a=i.map(r=>r!==o?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...l,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(r,{...l,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var i=l("Slot"),o=Symbol("radix.slottable");function d(e){let r=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=o,r}function c(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===o}},9724:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>C});var s=t(5155),a=t(2115),n=t(2108),l=t(5695),i=t(2177),o=t(221),d=t(1153),c=t(6874),u=t.n(c),m=t(7168),f=t(9852),h=t(2714),p=t(8482),x=t(9026),v=t(5040),g=t(8533),y=t(9420);let b=(0,t(2895).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]);var w=t(8749),N=t(2657),j=t(7624);let k=d.Ik({phone:d.Yj().min(9,"Please enter a valid phone number"),password:d.Yj().min(6,"Password must be at least 6 characters")});function A(){let[e,r]=(0,a.useState)(!1),[t,d]=(0,a.useState)(""),[c,A]=(0,a.useState)(!1),C=(0,l.useRouter)(),R=(0,l.useSearchParams)(),S=R.get("callbackUrl")||"/dashboard",E=R.get("error"),{register:P,handleSubmit:F,formState:{errors:I}}=(0,i.mN)({resolver:(0,o.u)(k)});(0,a.useEffect)(()=>{if(E)switch(E){case"CredentialsSignin":d("Invalid phone number or password");break;case"AccessDenied":d("Access denied. Please contact administrator.");break;default:d("An error occurred during sign in")}},[E]);let D=async e=>{r(!0),d("");try{let r=await (0,n.signIn)("credentials",{phone:e.phone,password:e.password,redirect:!1});if(null==r?void 0:r.error)d("Invalid phone number or password");else{let e=await (0,n.getSession)();if(null==e?void 0:e.user)switch(e.user.role){case"STUDENT":C.push("/dashboard/student");break;case"TEACHER":C.push("/dashboard/teacher");break;case"RECEPTION":C.push("/dashboard/leads");break;case"CASHIER":C.push("/dashboard/payments");break;case"ACADEMIC_MANAGER":C.push("/dashboard/assessments");break;default:C.push(S)}}}catch(e){d("An error occurred. Please try again.")}finally{r(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsx)("div",{className:"text-center mb-8",children:(0,s.jsxs)(u(),{href:"/",className:"inline-flex items-center",children:[(0,s.jsx)(v.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,s.jsx)("span",{className:"text-2xl font-bold text-gray-900",children:"Innovative Centre"})]})}),(0,s.jsxs)(p.Zp,{children:[(0,s.jsxs)(p.aR,{children:[(0,s.jsx)(p.ZB,{children:"Staff Login"}),(0,s.jsx)(p.BT,{children:"Enter your credentials to access the CRM system"})]}),(0,s.jsxs)(p.Wu,{children:[(0,s.jsxs)("form",{onSubmit:F(D),className:"space-y-4",children:[t&&(0,s.jsxs)(x.Fc,{variant:"destructive",children:[(0,s.jsx)(g.A,{className:"h-4 w-4"}),(0,s.jsx)(x.TN,{children:t})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(h.J,{htmlFor:"phone",children:"Phone Number"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(f.p,{id:"phone",...P("phone"),placeholder:"+998 XX XXX XX XX",className:"pl-10",disabled:e})]}),I.phone&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:I.phone.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(h.J,{htmlFor:"password",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,s.jsx)(f.p,{id:"password",type:c?"text":"password",...P("password"),placeholder:"Enter your password",className:"pl-10 pr-10",disabled:e}),(0,s.jsx)("button",{type:"button",onClick:()=>A(!c),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600",disabled:e,children:c?(0,s.jsx)(w.A,{className:"h-4 w-4"}):(0,s.jsx)(N.A,{className:"h-4 w-4"})})]}),I.password&&(0,s.jsx)("p",{className:"text-red-500 text-sm mt-1",children:I.password.message})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)("input",{id:"remember",type:"checkbox",className:"rounded border-gray-300"}),(0,s.jsx)(h.J,{htmlFor:"remember",className:"text-sm",children:"Remember me"})]}),(0,s.jsx)(u(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot password?"})]}),(0,s.jsx)(m.$,{type:"submit",className:"w-full",disabled:e,children:e?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Signing in..."]}):"Sign In"})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",(0,s.jsx)(u(),{href:"/contact",className:"text-blue-600 hover:text-blue-500 font-medium",children:"Contact administrator"})]})}),(0,s.jsx)("div",{className:"mt-4 text-center",children:(0,s.jsx)(u(),{href:"/",className:"text-sm text-blue-600 hover:underline",children:"← Back to Homepage"})})]})]}),(0,s.jsxs)("div",{className:"text-center mt-8 text-sm text-gray-500",children:[(0,s.jsx)("p",{children:"\xa9 2024 Innovative Centre. All rights reserved."}),(0,s.jsxs)("p",{className:"mt-1",children:["Need help?"," ",(0,s.jsx)(u(),{href:"/contact",className:"text-blue-600 hover:text-blue-500",children:"Contact Support"})]})]})]})})}function C(){return(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)("div",{children:"Loading..."}),children:(0,s.jsx)(A,{})})}},9852:(e,r,t)=>{"use strict";t.d(r,{p:()=>l});var s=t(5155),a=t(2115),n=t(3999);let l=a.forwardRef((e,r)=>{let{className:t,type:a,...l}=e;return(0,s.jsx)("input",{type:a,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:r,...l})});l.displayName="Input"}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,2356,6874,2108,8441,1684,7358],()=>r(8341)),_N_E=e.O()}]);