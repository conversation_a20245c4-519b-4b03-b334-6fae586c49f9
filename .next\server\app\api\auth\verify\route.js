(()=>{var e={};e.id=2588,e.ids=[2588],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,s)=>{"use strict";s.d(r,{z:()=>o});var t=s(96330);let o=globalThis.prisma??new t.PrismaClient},88799:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>R,routeModule:()=>x,serverHooks:()=>f,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{POST:()=>v});var o=s(96559),n=s(48088),i=s(37719),a=s(32190),u=s(79464),p=s(97110),c=s.n(p),d=s(45697);let l=d.z.object({phone:d.z.string().min(9,"Phone number must be at least 9 characters"),password:d.z.string().min(1,"Password is required"),serverKey:d.z.string().min(1,"Server key is required")});async function v(e){try{console.log("Verification endpoint called");let r=await e.json();console.log("Request body:",{phone:r.phone,serverKey:r.serverKey?"present":"missing"});let{phone:s,password:t,serverKey:o}=l.parse(r),n=process.env.INTER_SERVER_SECRET;if(console.log("Server key check:",{expected:n?"present":"missing",received:o}),!n||o!==n)return console.log("Server key mismatch"),a.NextResponse.json({error:"Unauthorized server access"},{status:401});let i=await u.z.user.findUnique({where:{phone:s},select:{id:!0,phone:!0,name:!0,email:!0,role:!0,password:!0,createdAt:!0,updatedAt:!0}});if(!i||!await c().compare(t,i.password))return a.NextResponse.json({error:"Invalid credentials"},{status:401});if(!["MANAGER","TEACHER","RECEPTION","STUDENT","ACADEMIC_MANAGER"].includes(i.role))return a.NextResponse.json({error:"Access denied: Role not allowed on staff server"},{status:403});let{password:p,...d}=i;return a.NextResponse.json({success:!0,user:d})}catch(e){if(console.error("Auth verification error:",e),e instanceof d.z.ZodError)return a.NextResponse.json({error:"Invalid request data",details:e.errors},{status:400});return a.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/auth/verify/route",pathname:"/api/auth/verify",filename:"route",bundlePath:"app/api/auth/verify/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\auth\\verify\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:f}=x;function R(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},97110:e=>{"use strict";e.exports=require("bcryptjs")}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[4243,580,5697],()=>s(88799));module.exports=t})();