(()=>{var e={};e.id=2122,e.ids=[2122],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},16099:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>K});var t=a(60687),r=a(43210),n=a(55192),l=a(24934),i=a(59821),c=a(68988),d=a(63974),o=a(37826),h=a(3018),x=a(26269),u=a(96241),m=a(9923),p=a(88233),j=a(58869),g=a(48730),y=a(40228),f=a(41312),v=a(23026),N=a(3589),b=a(78272),w=a(72730),C=a(82080),A=a(99270),E=a(96474),S=a(27605),L=a(63442),T=a(9275),D=a(39390),k=a(42902),I=a(97992),$=a(96545);let _=T.Ik({name:T.Yj().min(2,"Group name must be at least 2 characters"),courseId:T.Yj().min(1,"Course is required"),teacherId:T.Yj().min(1,"Teacher is required"),capacity:T.ai().min(1,"Capacity must be at least 1").max(50,"Capacity cannot exceed 50"),schedule:T.Yj().min(1,"Schedule is required"),room:T.Yj().optional(),cabinetId:T.Yj().optional(),branch:T.Yj().min(1,"Branch is required"),startDate:T.Yj().min(1,"Start date is required"),endDate:T.Yj().min(1,"End date is required"),isActive:T.zM().default(!0)}),F=[{value:"Main Branch",label:"Main Branch"},{value:"Branch",label:"Branch"}],V=["Monday, Wednesday, Friday - 9:00-11:00","Monday, Wednesday, Friday - 11:00-13:00","Monday, Wednesday, Friday - 14:00-16:00","Monday, Wednesday, Friday - 16:00-18:00","Monday, Wednesday, Friday - 18:00-20:00","Tuesday, Thursday, Saturday - 9:00-11:00","Tuesday, Thursday, Saturday - 11:00-13:00","Tuesday, Thursday, Saturday - 14:00-16:00","Tuesday, Thursday, Saturday - 16:00-18:00","Tuesday, Thursday, Saturday - 18:00-20:00","Daily - 9:00-10:30","Daily - 10:30-12:00","Daily - 14:00-15:30","Daily - 15:30-17:00","Daily - 17:00-18:30","Daily - 18:30-20:00"],G=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},B=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";case"NEW":return"New";default:return"Unknown"}};function W({initialData:e,onSubmit:s,onCancel:a,isEditing:i=!1}){let{currentBranch:o}=(0,$.O)(),[x,u]=(0,r.useState)(!1),[m,p]=(0,r.useState)(null),[j,v]=(0,r.useState)([]),[N,b]=(0,r.useState)([]),[A,E]=(0,r.useState)([]),[T,W]=(0,r.useState)([]),[O,M]=(0,r.useState)([]),{register:P,handleSubmit:q,setValue:U,watch:J,formState:{errors:R}}=(0,S.mN)({resolver:(0,L.u)(_),defaultValues:{name:e?.name||"",courseId:e?.courseId||"",teacherId:e?.teacherId||"",capacity:e?.capacity||15,schedule:e?.schedule||"",room:e?.room||"",cabinetId:e?.cabinetId||"",branch:e?.branch||o.name,startDate:e?.startDate||"",endDate:e?.endDate||"",isActive:e?.isActive??!0}}),z=J("courseId"),Z=J("teacherId"),K=J("cabinetId"),Y=J("branch"),H=J("schedule"),X=J("isActive"),Q=async e=>{u(!0),p(null);try{await s(e)}catch(e){p(e instanceof Error?e.message:"An error occurred")}finally{u(!1)}},ee=j.find(e=>e.id===z);return(0,t.jsxs)(n.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-5 w-5 mr-2"}),i?"Edit Group":"Create New Group"]}),(0,t.jsx)(n.BT,{children:i?"Update group information":"Enter group details to create a new class group"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("form",{onSubmit:q(Q),className:"space-y-6",children:[m&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:m})}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"name",children:"Group Name *"}),(0,t.jsx)(c.p,{id:"name",...P("name"),placeholder:"e.g., A1-Morning-01",className:R.name?"border-red-500":""}),R.name&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"capacity",children:"Capacity *"}),(0,t.jsx)(c.p,{id:"capacity",type:"number",min:"1",max:"50",...P("capacity",{valueAsNumber:!0}),className:R.capacity?"border-red-500":""}),R.capacity&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.capacity.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"branch",children:"Branch *"}),(0,t.jsxs)(d.l6,{value:Y,onValueChange:e=>U("branch",e),children:[(0,t.jsx)(d.bq,{className:R.branch?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select branch"})}),(0,t.jsx)(d.gC,{children:F.map(e=>(0,t.jsx)(d.eb,{value:e.value,children:e.label},e.value))})]}),R.branch&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.branch.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"room",children:"Room"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(I.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"room",...P("room"),placeholder:"e.g., Room 101",className:"pl-10"})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"cabinetId",children:"Cabinet"}),(0,t.jsxs)(d.l6,{value:K,onValueChange:e=>U("cabinetId",e),children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"Select cabinet"})}),(0,t.jsx)(d.gC,{children:O.map(e=>(0,t.jsxs)(d.eb,{value:e.id,children:[e.name," (#",e.number,") - Capacity: ",e.capacity]},e.id))})]}),Y&&0===O.length&&(0,t.jsx)("p",{className:"text-sm text-yellow-600",children:"No cabinets available for selected branch"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(k.d,{id:"isActive",checked:X,onCheckedChange:e=>U("isActive",e)}),(0,t.jsx)(D.J,{htmlFor:"isActive",children:"Active Group"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Course & Teacher"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"courseId",children:"Course *"}),(0,t.jsxs)(d.l6,{value:z,onValueChange:e=>U("courseId",e),children:[(0,t.jsx)(d.bq,{className:R.courseId?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select course"})}),(0,t.jsx)(d.gC,{children:j.map(e=>(0,t.jsxs)(d.eb,{value:e.id,children:[e.name," - ",e.level]},e.id))})]}),R.courseId&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.courseId.message}),ee&&(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Duration: ",ee.duration," weeks | Price: $",(ee.price/12500).toFixed(0)]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"teacherId",children:"Teacher *"}),(0,t.jsxs)(d.l6,{value:Z,onValueChange:e=>U("teacherId",e),children:[(0,t.jsx)(d.bq,{className:R.teacherId?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select teacher"})}),(0,t.jsx)(d.gC,{children:T.map(e=>(0,t.jsx)(d.eb,{value:e.id,children:(0,t.jsxs)("div",{className:"flex items-center justify-between w-full",children:[(0,t.jsxs)("span",{children:[e.user.name," - ",e.subject]}),(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${G(e.tier||"NEW")}`,children:B(e.tier||"NEW")})]})},e.id))})]}),R.teacherId&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.teacherId.message}),Y&&0===T.length&&(0,t.jsx)("p",{className:"text-sm text-yellow-600",children:"No teachers available for selected branch"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Schedule & Duration"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"schedule",children:"Schedule *"}),(0,t.jsxs)(d.l6,{value:H,onValueChange:e=>U("schedule",e),children:[(0,t.jsx)(d.bq,{className:R.schedule?"border-red-500":"",children:(0,t.jsx)(d.yv,{placeholder:"Select schedule"})}),(0,t.jsx)(d.gC,{children:V.map(e=>(0,t.jsx)(d.eb,{value:e,children:e},e))})]}),R.schedule&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.schedule.message})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"startDate",children:"Start Date *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"startDate",type:"date",...P("startDate"),className:`pl-10 ${R.startDate?"border-red-500":""}`})]}),R.startDate&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.startDate.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(D.J,{htmlFor:"endDate",children:"End Date *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(y.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{id:"endDate",type:"date",...P("endDate"),className:`pl-10 ${R.endDate?"border-red-500":""}`})]}),R.endDate&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:R.endDate.message})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[a&&(0,t.jsx)(l.$,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,t.jsxs)(l.$,{type:"submit",disabled:x,children:[x&&(0,t.jsx)(w.A,{className:"mr-2 h-4 w-4 animate-spin"}),i?"Update Group":"Create Group"]})]})]})})]})}var O=a(25283),M=a(96752),P=a(23928),q=a(94053);function U(){let[e,s]=(0,r.useState)([]),[a,d]=(0,r.useState)(!0),[x,u]=(0,r.useState)(""),[j,g]=(0,r.useState)(!1),[y,v]=(0,r.useState)(!1),[N,b]=(0,r.useState)(null),[S,L]=(0,r.useState)(!1),[T,D]=(0,r.useState)(null),k=async()=>{try{d(!0);let e=await fetch("/api/courses"),a=await e.json();s(a.courses||[]),D(null)}catch(e){console.error("Error fetching courses:",e),D("Failed to fetch courses")}finally{d(!1)}},I=async e=>{L(!0),D(null);try{let s=await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create course")}g(!1),k()}catch(e){D(e instanceof Error?e.message:"An error occurred")}finally{L(!1)}},$=async e=>{if(N){L(!0),D(null);try{let s=await fetch(`/api/courses/${N.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update course")}v(!1),b(null),k()}catch(e){D(e instanceof Error?e.message:"An error occurred")}finally{L(!1)}}},_=async e=>{if(confirm("Are you sure you want to delete this course? This action cannot be undone."))try{let s=await fetch(`/api/courses/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete course")}k()}catch(e){D(e instanceof Error?e.message:"An error occurred")}},F=e.filter(e=>e.name.toLowerCase().includes(x.toLowerCase())||e.level.toLowerCase().includes(x.toLowerCase())||e.description?.toLowerCase().includes(x.toLowerCase())),V=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800";return a?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(w.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading courses..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[T&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:T})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:"Courses"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage course catalog and pricing"})]}),(0,t.jsxs)(o.lG,{open:j,onOpenChange:g,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsxs)(l.$,{children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Add Course"]})}),(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Add New Course"}),(0,t.jsx)(o.rr,{children:"Create a new course offering with pricing and duration details."})]}),(0,t.jsx)(q.A,{onSubmit:I,onCancel:()=>g(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Search Courses"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{placeholder:"Search by course name, level, or description...",value:x,onChange:e=>u(e.target.value),className:"pl-10"})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{children:["Courses (",F.length,")"]}),(0,t.jsx)(n.BT,{children:"Complete list of available courses"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsxs)(M.XI,{children:[(0,t.jsx)(M.A0,{children:(0,t.jsxs)(M.Hj,{children:[(0,t.jsx)(M.nd,{children:"Course"}),(0,t.jsx)(M.nd,{children:"Level"}),(0,t.jsx)(M.nd,{children:"Duration"}),(0,t.jsx)(M.nd,{children:"Price"}),(0,t.jsx)(M.nd,{children:"Groups"}),(0,t.jsx)(M.nd,{children:"Students"}),(0,t.jsx)(M.nd,{children:"Status"}),(0,t.jsx)(M.nd,{children:"Actions"})]})}),(0,t.jsx)(M.BF,{children:F.map(e=>(0,t.jsxs)(M.Hj,{children:[(0,t.jsx)(M.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,t.jsx)(C.A,{className:"h-5 w-5 text-blue-600"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),e.description&&(0,t.jsx)("div",{className:"text-sm text-gray-500 truncate max-w-xs",children:e.description})]})]})}),(0,t.jsx)(M.nA,{children:(0,t.jsx)(i.E,{className:V(e.level),children:e.level.replace("_"," ")})}),(0,t.jsxs)(M.nA,{children:[e.duration," weeks"]}),(0,t.jsx)(M.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(P.A,{className:"h-4 w-4 text-gray-400 mr-1"}),e.price.toLocaleString()," UZS"]})}),(0,t.jsx)(M.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400 mr-1"}),e._count.groups]})}),(0,t.jsx)(M.nA,{children:e.groups.reduce((e,s)=>e+s._count.enrollments,0)}),(0,t.jsx)(M.nA,{children:(0,t.jsx)(i.E,{className:e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(M.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{b(e),v(!0)},children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>_(e.id),children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===F.length&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No courses found matching your search."})})]})]}),(0,t.jsx)(o.lG,{open:y,onOpenChange:v,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Edit Course"}),(0,t.jsx)(o.rr,{children:"Update course information, pricing, and availability."})]}),N&&(0,t.jsx)(q.A,{initialData:{name:N.name,level:N.level,description:N.description||"",duration:N.duration,price:N.price,isActive:N.isActive},onSubmit:$,onCancel:()=>{v(!1),b(null)},isEditing:!0})]})})]})}var J=a(71702);let R=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},z=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";case"NEW":return"New";default:return"Unknown"}};function Z({group:e,onEdit:s,onDelete:a,onAddStudent:r,onToggleExpansion:c,isExpanded:d}){return(0,t.jsx)(n.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(i.E,{className:{A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"}[e.course.level]||"bg-gray-100 text-gray-800",variant:"secondary",children:e.course.level.replace("_"," ")}),(0,t.jsxs)(i.E,{className:"bg-blue-100 text-blue-800",variant:"secondary",children:[e._count.enrollments,"/",e.capacity," students"]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>s(e),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>a(e.id),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(p.A,{className:"h-4 w-4"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.course.name})]}),(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Teacher: ",e.teacher.user.name]})]}),(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${R(e.teacher.tier||"NEW")}`,children:z(e.teacher.tier||"NEW")})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(g.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{className:"text-sm",children:(e=>{let s=e.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/);return s?`${s[1]}:${s[2]} - ${s[3]}:${s[4]}`:""})(e.schedule)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm",children:["Start date: ",(0,u.Yq)(new Date(e.startDate))]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(i.E,{className:e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",variant:"secondary",children:e.isActive?"Started":"Not Started"}),(0,t.jsx)(i.E,{className:"bg-blue-100 text-blue-800",variant:"secondary",children:"UZBEK"})]}),(0,t.jsxs)("div",{className:"space-y-3 pt-4 border-t",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm font-medium",children:["Students (",e._count.enrollments,"/",e.capacity,")"]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)(l.$,{variant:"outline",size:"sm",onClick:()=>r(e),disabled:e._count.enrollments>=e.capacity,className:"text-green-600 hover:text-green-700",children:[(0,t.jsx)(v.A,{className:"h-4 w-4 mr-1"}),"Add Student"]}),(0,t.jsx)(l.$,{variant:"ghost",size:"sm",onClick:()=>c(e.id),children:d?(0,t.jsx)(N.A,{className:"h-4 w-4"}):(0,t.jsx)(b.A,{className:"h-4 w-4"})})]})]}),d&&(0,t.jsx)("div",{className:"bg-gray-50 rounded-lg p-3 space-y-2",children:e.enrollments.length>0?e.enrollments.filter(e=>"ACTIVE"===e.status).map(e=>(0,t.jsxs)("div",{className:"flex justify-between items-center text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:e.student.user.name}),(0,t.jsx)("span",{className:"text-gray-500 ml-2",children:e.student.user.phone})]}),(0,t.jsx)(i.E,{variant:"outline",className:"text-xs",children:e.status})]},e.id)):(0,t.jsx)("p",{className:"text-sm text-gray-500 text-center py-2",children:"No students enrolled"})})]})]})})})}function K(){let{currentBranch:e}=(0,$.O)(),{toast:s}=(0,J.dj)(),[a,i]=(0,r.useState)([]),[u,m]=(0,r.useState)([]),[p,j]=(0,r.useState)(!0),[g,y]=(0,r.useState)(""),[v,N]=(0,r.useState)(""),[b,S]=(0,r.useState)(""),[L,T]=(0,r.useState)(""),[D,k]=(0,r.useState)(""),[I,_]=(0,r.useState)(!1),[F,V]=(0,r.useState)(!1),[G,B]=(0,r.useState)(null),[M,P]=(0,r.useState)(!1),[q,K]=(0,r.useState)(null),[Y,H]=(0,r.useState)(new Set),[X,Q]=(0,r.useState)(!1),[ee,es]=(0,r.useState)(null),[ea,et]=(0,r.useState)([]),[er,en]=(0,r.useState)([]),[el,ei]=(0,r.useState)(""),[ec,ed]=(0,r.useState)(!1),[eo,eh]=(0,r.useState)(!1),ex=async()=>{if(e?.id)try{j(!0);let s=await fetch(`/api/groups?branch=${e.id}`),a=await s.json();i(a.groups||[]),m(a.slotTierAnalysis||[]),K(null)}catch(e){console.error("Error fetching groups:",e),K("Failed to fetch groups")}finally{j(!1)}},eu=async e=>{P(!0),K(null);try{let s=await fetch("/api/groups",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create group")}_(!1),ex()}catch(e){K(e instanceof Error?e.message:"An error occurred")}finally{P(!1)}},em=async e=>{if(G){P(!0),K(null);try{let s=await fetch(`/api/groups/${G.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update group")}V(!1),B(null),ex()}catch(e){K(e instanceof Error?e.message:"An error occurred")}finally{P(!1)}}},ep=async e=>{if(confirm("Are you sure you want to delete this group? This action cannot be undone."))try{let s=await fetch(`/api/groups/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete group")}ex()}catch(e){K(e instanceof Error?e.message:"An error occurred")}},ej=e=>{let s=new Set(Y);s.has(e)?s.delete(e):s.add(e),H(s)},eg=async a=>{try{ed(!0);let s=await fetch(`/api/students?available=true&branch=${e?.id}`),a=await s.json();et(a.students||[]),en(a.students||[]),ei("")}catch(e){console.error("Error fetching students:",e),s({title:"Error",description:"Failed to fetch available students",variant:"destructive"})}finally{ed(!1)}},ey=e=>{try{let s=e;try{let a=JSON.parse(e);s=Array.isArray(a)?a.join(" "):e}catch{s=e}let a=s.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/),t=a?`${a[1]}:${a[2]}-${a[3]}:${a[4]}`:"Unknown",r=s.toLowerCase(),n=r.includes("monday")&&r.includes("wednesday")&&r.includes("friday")?"MWF":r.includes("tuesday")&&r.includes("thursday")&&r.includes("saturday")?"TTS":"Other";return{time:t,days:n}}catch(s){return console.error("Error parsing schedule:",s,"Schedule:",e),{time:"Unknown",days:"Unknown"}}},ef=async s=>{try{let a=await fetch(`/api/groups?branch=${e?.id}`),t=await a.json();if(t.slotTierAnalysis){let{time:e,days:a}=ey(s.schedule),r=`${s.course.level}-${a}-${e}`,n=t.slotTierAnalysis.find(e=>e.slotKey===r);if(n)return n.availableTiers.includes(s.teacher.tier)}return!0}catch(e){return console.error("Error checking tier availability:",e),!0}},ev=async e=>{let a=e.teacher.tier||"NEW";if("A_LEVEL"!==a&&!await ef(e))return void s({title:"Teacher Tier Restriction",description:`${z(a)} teachers are not available for new students yet. Higher tier teachers must reach 80% capacity first.`,variant:"destructive"});es(e),await eg(e.id),Q(!0)},eN=e=>{ei(e),e.trim()?en(ea.filter(s=>s.user.name.toLowerCase().includes(e.toLowerCase())||s.user.phone.includes(e)||s.level.toLowerCase().includes(e.toLowerCase()))):en(ea)},eb=async e=>{if(ee)try{P(!0);let a=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:e,groupId:ee.id,startDate:new Date().toISOString(),status:"ACTIVE"})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to enroll student")}await ex(),Q(!1),es(null),s({title:"Success",description:"Student enrolled successfully",variant:"success"})}catch(e){console.error("Error enrolling student:",e),s({title:"Error",description:e instanceof Error?e.message:"Failed to enroll student",variant:"destructive"})}finally{P(!1)}},ew=async a=>{if(ee&&e)try{P(!0);let t=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:a.name,phone:a.phone,email:a.email||void 0,role:"STUDENT"})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create user")}let r=await t.json(),n=await fetch("/api/students",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:r.id,level:a.level,branch:e.id,emergencyContact:a.emergencyContact,dateOfBirth:a.dateOfBirth,address:a.address})});if(!n.ok){let e=await n.json();throw Error(e.error||"Failed to create student")}let l=await n.json(),i=await fetch("/api/enrollments",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({studentId:l.id,groupId:ee.id,startDate:new Date().toISOString(),status:"ACTIVE"})});if(!i.ok){let e=await i.json();throw Error(e.error||"Failed to enroll student")}await ex(),eh(!1),Q(!1),es(null),s({title:"Success",description:"Student created and enrolled successfully",variant:"success"})}catch(e){console.error("Error creating and enrolling student:",e),s({title:"Error",description:e instanceof Error?e.message:"Failed to create and enroll student",variant:"destructive"})}finally{P(!1)}},eC=a.filter(e=>{let s=e.name.toLowerCase().includes(g.toLowerCase())||e.course.name.toLowerCase().includes(g.toLowerCase())||e.teacher.user.name.toLowerCase().includes(g.toLowerCase()),a=!v||"all"===v||e.course.level===v,t=!b||"all"===b||e.course.name.toLowerCase().includes(b.toLowerCase()),r=!D||"all"===D||e.teacher.tier===D,n=!0;return L&&(n=(function(e){let s=e.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/);if(!s)return"";let a=parseInt(s[1]);return a<12?"Morning":a<17?"Afternoon":"Evening"})(e.schedule).toLowerCase()===L),s&&a&&t&&n&&r}).sort((e,s)=>{let a={A_LEVEL:1,B_LEVEL:2,C_LEVEL:3,NEW:4};return(a[e.teacher.tier||"NEW"]||4)-(a[s.teacher.tier||"NEW"]||4)}),eA=eC.filter(e=>e.schedule.toLowerCase().includes("monday")&&e.schedule.toLowerCase().includes("wednesday")&&e.schedule.toLowerCase().includes("friday")),eE=eC.filter(e=>e.schedule.toLowerCase().includes("tuesday")&&e.schedule.toLowerCase().includes("thursday")&&e.schedule.toLowerCase().includes("saturday"));return p?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(w.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading groups..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[q&&(0,t.jsx)(h.Fc,{variant:"destructive",children:(0,t.jsx)(h.TN,{children:q})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Groups"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage class groups and schedules"})]}),(0,t.jsxs)(o.lG,{open:I,onOpenChange:_,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsx)(l.$,{className:"bg-blue-600 hover:bg-blue-700",children:"Add New Group"})}),(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Create New Group"}),(0,t.jsx)(o.rr,{children:"Set up a new class group with course, teacher, and schedule details."})]}),(0,t.jsx)(W,{onSubmit:eu,onCancel:()=>_(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(x.tU,{defaultValue:"groups",className:"space-y-6",children:[(0,t.jsxs)(x.j7,{className:"grid w-full grid-cols-2",children:[(0,t.jsxs)(x.Xi,{value:"groups",className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Groups"]}),(0,t.jsxs)(x.Xi,{value:"courses",className:"flex items-center",children:[(0,t.jsx)(C.A,{className:"h-4 w-4 mr-2"}),"Courses"]})]}),(0,t.jsxs)(x.av,{value:"groups",className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Select Level"}),(0,t.jsxs)(d.l6,{value:v,onValueChange:N,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"All Levels"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"All Levels"}),(0,t.jsx)(d.eb,{value:"A1",children:"A1"}),(0,t.jsx)(d.eb,{value:"A2",children:"A2"}),(0,t.jsx)(d.eb,{value:"B1",children:"B1"}),(0,t.jsx)(d.eb,{value:"B2",children:"B2"}),(0,t.jsx)(d.eb,{value:"IELTS",children:"IELTS"}),(0,t.jsx)(d.eb,{value:"SAT",children:"SAT"}),(0,t.jsx)(d.eb,{value:"MATH",children:"MATH"}),(0,t.jsx)(d.eb,{value:"KIDS",children:"KIDS"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Language"}),(0,t.jsxs)(d.l6,{value:b,onValueChange:S,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"All Languages"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"All Languages"}),(0,t.jsx)(d.eb,{value:"english",children:"English"}),(0,t.jsx)(d.eb,{value:"russian",children:"Russian"}),(0,t.jsx)(d.eb,{value:"uzbek",children:"Uzbek"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Teacher Tier"}),(0,t.jsxs)(d.l6,{value:D,onValueChange:k,children:[(0,t.jsx)(d.bq,{children:(0,t.jsx)(d.yv,{placeholder:"All Tiers"})}),(0,t.jsxs)(d.gC,{children:[(0,t.jsx)(d.eb,{value:"all",children:"All Tiers"}),(0,t.jsx)(d.eb,{value:"A_LEVEL",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${R("A_LEVEL")}`,children:"A-Level"}),(0,t.jsx)("span",{children:"A-Level Teachers"})]})}),(0,t.jsx)(d.eb,{value:"B_LEVEL",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${R("B_LEVEL")}`,children:"B-Level"}),(0,t.jsx)("span",{children:"B-Level Teachers"})]})}),(0,t.jsx)(d.eb,{value:"C_LEVEL",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${R("C_LEVEL")}`,children:"C-Level"}),(0,t.jsx)("span",{children:"C-Level Teachers"})]})}),(0,t.jsx)(d.eb,{value:"NEW",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("span",{className:`text-xs px-2 py-1 rounded-full ${R("NEW")}`,children:"New"}),(0,t.jsx)("span",{children:"New Teachers"})]})})]})]})]}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-8 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)("label",{className:"text-sm font-medium text-gray-700",children:"Search"}),(0,t.jsx)(c.p,{placeholder:"Search by teacher or group name",value:g,onChange:e=>y(e.target.value),className:"pl-10"})]})]}),(0,t.jsxs)("div",{className:"flex flex-wrap gap-2",children:[(0,t.jsx)(l.$,{variant:""===L?"default":"outline",onClick:()=>T(""),className:"text-sm",children:"Available Groups"}),(0,t.jsx)(l.$,{variant:"morning"===L?"default":"outline",onClick:()=>T("morning"),className:"text-sm",children:"Morning Groups"}),(0,t.jsx)(l.$,{variant:"afternoon"===L?"default":"outline",onClick:()=>T("afternoon"),className:"text-sm",children:"Afternoon Groups"}),(0,t.jsx)(l.$,{variant:"evening"===L?"default":"outline",onClick:()=>T("evening"),className:"text-sm",children:"Evening Groups"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-blue-600",children:"Monday/Wednesday/Friday"}),(0,t.jsxs)("div",{className:"space-y-4",children:[eA.map(e=>(0,t.jsx)(Z,{group:e,onEdit:e=>{B(e),V(!0)},onDelete:ep,onAddStudent:ev,onToggleExpansion:ej,isExpanded:Y.has(e.id)},e.id)),0===eA.length&&(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No M/W/F groups found"})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsx)("h3",{className:"text-xl font-semibold text-blue-600",children:"Tuesday/Thursday/Saturday"}),(0,t.jsxs)("div",{className:"space-y-4",children:[eE.map(e=>(0,t.jsx)(Z,{group:e,onEdit:e=>{B(e),V(!0)},onDelete:ep,onAddStudent:ev,onToggleExpansion:ej,isExpanded:Y.has(e.id)},e.id)),0===eE.length&&(0,t.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No T/T/S groups found"})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.length})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.filter(e=>e.isActive).length})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-yellow-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Students"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.reduce((e,s)=>e+s._count.enrollments,0)})]})]})})}),(0,t.jsx)(n.Zp,{children:(0,t.jsx)(n.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg. Group Size"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:a.length>0?Math.round(a.reduce((e,s)=>e+s._count.enrollments,0)/a.length):0})]})]})})})]})]}),(0,t.jsx)(x.av,{value:"courses",className:"space-y-6",children:(0,t.jsx)(U,{})})]}),(0,t.jsx)(o.lG,{open:F,onOpenChange:V,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Edit Group"}),(0,t.jsx)(o.rr,{children:"Update group information, schedule, and settings."})]}),G&&(0,t.jsx)(W,{initialData:{name:G.name,courseId:"",teacherId:"",capacity:G.capacity,schedule:G.schedule,room:G.room||"",branch:G.branch,startDate:new Date(G.startDate).toISOString().split("T")[0],endDate:new Date(G.endDate).toISOString().split("T")[0],isActive:G.isActive},onSubmit:em,onCancel:()=>{V(!1),B(null)},isEditing:!0})]})}),(0,t.jsx)(o.lG,{open:X,onOpenChange:Q,children:(0,t.jsxs)(o.Cf,{className:"max-w-lg",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Add Student to Group"}),(0,t.jsx)(o.rr,{children:ee&&(0,t.jsxs)(t.Fragment,{children:["Select a student to enroll in ",(0,t.jsx)("strong",{children:ee.name})]})})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(A.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(c.p,{placeholder:"Search students by name, phone, or level...",value:el,onChange:e=>eN(e.target.value),className:"pl-10"})]}),(0,t.jsxs)(l.$,{variant:"outline",onClick:()=>eh(!0),className:"w-full text-blue-600 border-blue-200 hover:bg-blue-50",children:[(0,t.jsx)(E.A,{className:"h-4 w-4 mr-2"}),"Create New Student"]})]}),ec?(0,t.jsxs)("div",{className:"flex items-center justify-center py-8",children:[(0,t.jsx)(w.A,{className:"h-6 w-6 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading students..."})]}):er.length>0?(0,t.jsx)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:er.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer",onClick:()=>eb(e.id),children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium",children:e.user.name}),(0,t.jsx)("p",{className:"text-sm text-gray-500",children:e.user.phone}),(0,t.jsxs)("p",{className:"text-xs text-gray-400",children:["Level: ",e.level]})]}),(0,t.jsx)(l.$,{size:"sm",disabled:M,children:M?(0,t.jsx)(w.A,{className:"h-4 w-4 animate-spin"}):"Enroll"})]},e.id))}):ea.length>0?(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(A.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No students found matching your search"}),(0,t.jsx)("p",{className:"text-sm",children:"Try a different search term or create a new student"})]}):(0,t.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,t.jsx)(f.A,{className:"h-12 w-12 mx-auto mb-4 text-gray-300"}),(0,t.jsx)("p",{children:"No available students found"}),(0,t.jsx)("p",{className:"text-sm",children:"All students may already be enrolled in groups"})]}),(0,t.jsx)("div",{className:"flex justify-end space-x-2 pt-4 border-t",children:(0,t.jsx)(l.$,{variant:"outline",onClick:()=>{Q(!1),es(null),ei(""),en([])},children:"Cancel"})})]})]})}),(0,t.jsx)(o.lG,{open:eo,onOpenChange:eh,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Create New Student"}),(0,t.jsxs)(o.rr,{children:["Create a new student and automatically enroll them in ",ee?.name]})]}),(0,t.jsx)(O.A,{initialData:{branch:e?.id||""},onSubmit:ew,onCancel:()=>eh(!1),isEditing:!1})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26269:(e,s,a)=>{"use strict";a.d(s,{tU:()=>T,av:()=>I,j7:()=>D,Xi:()=>k});var t=a(60687),r=a(43210),n=a(70569),l=a(11273),i=a(72942),c=a(46059),d=a(14163),o=a(43),h=a(65551),x=a(96963),u="Tabs",[m,p]=(0,l.A)(u,[i.RG]),j=(0,i.RG)(),[g,y]=m(u),f=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,onValueChange:n,defaultValue:l,orientation:i="horizontal",dir:c,activationMode:m="automatic",...p}=e,j=(0,o.jH)(c),[y,f]=(0,h.i)({prop:r,onChange:n,defaultProp:l??"",caller:u});return(0,t.jsx)(g,{scope:a,baseId:(0,x.B)(),value:y,onValueChange:f,orientation:i,dir:j,activationMode:m,children:(0,t.jsx)(d.sG.div,{dir:j,"data-orientation":i,...p,ref:s})})});f.displayName=u;var v="TabsList",N=r.forwardRef((e,s)=>{let{__scopeTabs:a,loop:r=!0,...n}=e,l=y(v,a),c=j(a);return(0,t.jsx)(i.bL,{asChild:!0,...c,orientation:l.orientation,dir:l.dir,loop:r,children:(0,t.jsx)(d.sG.div,{role:"tablist","aria-orientation":l.orientation,...n,ref:s})})});N.displayName=v;var b="TabsTrigger",w=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:r,disabled:l=!1,...c}=e,o=y(b,a),h=j(a),x=E(o.baseId,r),u=S(o.baseId,r),m=r===o.value;return(0,t.jsx)(i.q7,{asChild:!0,...h,focusable:!l,active:m,children:(0,t.jsx)(d.sG.button,{type:"button",role:"tab","aria-selected":m,"aria-controls":u,"data-state":m?"active":"inactive","data-disabled":l?"":void 0,disabled:l,id:x,...c,ref:s,onMouseDown:(0,n.m)(e.onMouseDown,e=>{l||0!==e.button||!1!==e.ctrlKey?e.preventDefault():o.onValueChange(r)}),onKeyDown:(0,n.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&o.onValueChange(r)}),onFocus:(0,n.m)(e.onFocus,()=>{let e="manual"!==o.activationMode;m||l||!e||o.onValueChange(r)})})})});w.displayName=b;var C="TabsContent",A=r.forwardRef((e,s)=>{let{__scopeTabs:a,value:n,forceMount:l,children:i,...o}=e,h=y(C,a),x=E(h.baseId,n),u=S(h.baseId,n),m=n===h.value,p=r.useRef(m);return r.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(c.C,{present:l||m,children:({present:a})=>(0,t.jsx)(d.sG.div,{"data-state":m?"active":"inactive","data-orientation":h.orientation,role:"tabpanel","aria-labelledby":x,hidden:!a,id:u,tabIndex:0,...o,ref:s,style:{...e.style,animationDuration:p.current?"0s":void 0},children:a&&i})})});function E(e,s){return`${e}-trigger-${s}`}function S(e,s){return`${e}-content-${s}`}A.displayName=C;var L=a(96241);let T=f,D=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(N,{ref:a,className:(0,L.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...s}));D.displayName=N.displayName;let k=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(w,{ref:a,className:(0,L.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...s}));k.displayName=w.displayName;let I=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(A,{ref:a,className:(0,L.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...s}));I.displayName=A.displayName},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55630:(e,s,a)=>{Promise.resolve().then(a.bind(a,16099))},58869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85894:(e,s,a)=>{Promise.resolve().then(a.bind(a,94369))},94369:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\groups\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\page.tsx","default")},95929:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>h,pages:()=>o,routeModule:()=>x,tree:()=>d});var t=a(65239),r=a(48088),n=a(88170),l=a.n(n),i=a(30893),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);a.d(s,c);let d={children:["",{children:["(dashboard)",{children:["dashboard",{children:["groups",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,94369)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\page.tsx"],h={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/groups/page",pathname:"/dashboard/groups",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,8706,7825,6027,3039,9251,5283],()=>a(95929));module.exports=t})();