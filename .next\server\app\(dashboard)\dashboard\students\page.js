(()=>{var e={};e.id=4074,e.ids=[4074],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15616:(e,s,t)=>{"use strict";t.d(s,{T:()=>l});var r=t(60687),a=t(43210),n=t(96241);let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...s}));l.displayName="Textarea"},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27601:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>h,tree:()=>c});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(s,i);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["students",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,50009)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/students/page",pathname:"/dashboard/students",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},50009:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\students\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\students\\page.tsx","default")},50188:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var r=t(60687),a=t(43210),n=t(55192),l=t(24934),d=t(59821),i=t(68988),c=t(63974),o=t(96752),x=t(37826),h=t(3018),u=t(96241),m=t(23689);let p=(0,t(18962).A)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var j=t(36058),f=t(93508),g=t(58869),y=t(72730),b=t(96474),v=t(41312),N=t(85778),w=t(99270),A=t(48340),S=t(41550),C=t(9923),P=t(88233),E=t(25283),D=t(96545);function k(){let{currentBranch:e}=(0,D.O)(),[s,t]=(0,a.useState)([]),[k,L]=(0,a.useState)({ACTIVE:0,DROPPED:0,PAUSED:0,COMPLETED:0}),[T,O]=(0,a.useState)(!0),[U,M]=(0,a.useState)(""),[B,R]=(0,a.useState)("ALL"),[I,_]=(0,a.useState)("ALL"),[q,Z]=(0,a.useState)(!1),[W,z]=(0,a.useState)(null),[F,V]=(0,a.useState)(!1),[$,G]=(0,a.useState)(!1),[H,J]=(0,a.useState)(null),[X,K]=(0,a.useState)(!1),[Y,Q]=(0,a.useState)(null),ee=async()=>{if(e?.id)try{O(!0);let s=new URLSearchParams({branch:e.id});"ALL"!==B&&s.append("status",B),"ALL"!==I&&s.append("paymentStatus",I),q&&s.append("includeDropped","true");let r=await fetch(`/api/students?${s.toString()}`),a=await r.json();t(a.students||[]),L(a.statusCounts||{}),Q(null)}catch(e){console.error("Error fetching students:",e),Q("Failed to fetch students")}finally{O(!1)}},es=s.filter(e=>e.user.name.toLowerCase().includes(U.toLowerCase())||e.user.phone.includes(U)||e.user.email?.toLowerCase().includes(U.toLowerCase())),et=Object.values(k).reduce((e,s)=>e+s,0),er=k.ACTIVE||0,ea=k.DROPPED||0,en=k.PAUSED||0,el=s.filter(e=>"UNPAID"===e.paymentStatus).length,ed=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800",ei=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"COMPLETED":return"bg-blue-100 text-blue-800";case"DROPPED":return"bg-red-100 text-red-800";case"PAUSED":return"bg-yellow-100 text-yellow-800";case"SUSPENDED":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},ec=e=>{switch(e){case"ACTIVE":return(0,r.jsx)(m.A,{className:"h-4 w-4 text-green-600"});case"DROPPED":return(0,r.jsx)(p,{className:"h-4 w-4 text-red-600"});case"PAUSED":return(0,r.jsx)(j.A,{className:"h-4 w-4 text-yellow-600"});case"COMPLETED":return(0,r.jsx)(f.A,{className:"h-4 w-4 text-blue-600"});default:return(0,r.jsx)(g.A,{className:"h-4 w-4 text-gray-600"})}},eo=e=>"UNPAID"===e?"bg-red-100 text-red-800":"bg-green-100 text-green-800",ex=async e=>{K(!0),Q(null);try{let s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,phone:e.phone,email:e.email||null,role:"STUDENT",password:"defaultPassword123"})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create user")}let t=await s.json(),r=await fetch("/api/students",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t.id,level:e.level,branch:e.branch,emergencyContact:e.emergencyContact,dateOfBirth:e.dateOfBirth,address:e.address})});if(!r.ok){let e=await r.json();throw Error(e.error||"Failed to create student")}V(!1),ee()}catch(e){Q(e instanceof Error?e.message:"An error occurred")}finally{K(!1)}},eh=async e=>{if(H){K(!0),Q(null);try{let s=await fetch(`/api/students/${H.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({level:e.level,branch:e.branch,emergencyContact:e.emergencyContact,dateOfBirth:e.dateOfBirth,address:e.address})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update student")}G(!1),J(null),ee()}catch(e){Q(e instanceof Error?e.message:"An error occurred")}finally{K(!1)}}},eu=async e=>{if(confirm("Are you sure you want to delete this student? This action cannot be undone."))try{let s=await fetch(`/api/students/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete student")}ee()}catch(e){Q(e instanceof Error?e.message:"An error occurred")}};return T?(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading students..."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[Y&&(0,r.jsx)(h.Fc,{variant:"destructive",children:(0,r.jsx)(h.TN,{children:Y})}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:[q?"Dropped Students Management":"Students Management"," - ",e?.name]}),(0,r.jsxs)("p",{className:"text-gray-600",children:[q?"Contact and re-enroll dropped students - Lead-like functionality":"Unified student management with status tracking and payment monitoring"," for ",e?.name]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(l.$,{variant:q?"default":"outline",onClick:()=>Z(!q),children:[(0,r.jsx)(p,{className:"h-4 w-4 mr-2"}),q?"Show Regular Students":"Show Dropped Students"]}),(0,r.jsxs)(x.lG,{open:F,onOpenChange:V,children:[(0,r.jsx)(x.zM,{asChild:!0,children:(0,r.jsxs)(l.$,{children:[(0,r.jsx)(b.A,{className:"h-4 w-4 mr-2"}),"Add Student"]})}),(0,r.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(x.c7,{children:[(0,r.jsx)(x.L3,{children:"Add New Student"}),(0,r.jsx)(x.rr,{children:"Create a new student profile with their personal and academic information."})]}),(0,r.jsx)(E.A,{onSubmit:ex,onCancel:()=>V(!1),isEditing:!1})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Students"}),(0,r.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold",children:et}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"All registered students"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Active Students"}),(0,r.jsx)(m.A,{className:"h-4 w-4 text-green-600"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:er}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently enrolled"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Unpaid Students"}),(0,r.jsx)(N.A,{className:"h-4 w-4 text-red-600"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:el}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Have pending payments"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Dropped Students"}),(0,r.jsx)(p,{className:"h-4 w-4 text-red-600"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-red-600",children:ea}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for re-enrollment"})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,r.jsx)(n.ZB,{className:"text-sm font-medium",children:"Paused Students"}),(0,r.jsx)(j.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:en}),(0,r.jsx)("p",{className:"text-xs text-muted-foreground",children:"Temporarily inactive"})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Search and Filter Students"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(i.p,{placeholder:"Search by name, phone, or email...",value:U,onChange:e=>M(e.target.value),className:"pl-10"})]}),(0,r.jsxs)(c.l6,{value:B,onValueChange:R,children:[(0,r.jsx)(c.bq,{className:"w-full md:w-48",children:(0,r.jsx)(c.yv,{placeholder:"Filter by status"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"ALL",children:"All Statuses"}),(0,r.jsx)(c.eb,{value:"ACTIVE",children:"Active"}),(0,r.jsx)(c.eb,{value:"DROPPED",children:"Dropped"}),(0,r.jsx)(c.eb,{value:"PAUSED",children:"Paused"}),(0,r.jsx)(c.eb,{value:"COMPLETED",children:"Completed"})]})]}),(0,r.jsxs)(c.l6,{value:I,onValueChange:_,children:[(0,r.jsx)(c.bq,{className:"w-full md:w-48",children:(0,r.jsx)(c.yv,{placeholder:"Filter by payment"})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"ALL",children:"All Payments"}),(0,r.jsx)(c.eb,{value:"PAID",children:"Paid"}),(0,r.jsx)(c.eb,{value:"UNPAID",children:"Unpaid"})]})]})]})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{children:["Students (",es.length,")"]}),(0,r.jsx)(n.BT,{children:"Unified student management with status and payment tracking"})]}),(0,r.jsxs)(n.Wu,{children:[(0,r.jsxs)(o.XI,{children:[(0,r.jsx)(o.A0,{children:(0,r.jsxs)(o.Hj,{children:[(0,r.jsx)(o.nd,{children:"Student"}),(0,r.jsx)(o.nd,{children:"Status"}),(0,r.jsx)(o.nd,{children:"Current Group"}),(0,r.jsx)(o.nd,{children:"Level"}),(0,r.jsx)(o.nd,{children:"Payment Status"}),(0,r.jsx)(o.nd,{children:"Branch"}),(0,r.jsx)(o.nd,{children:"Actions"})]})}),(0,r.jsx)(o.BF,{children:es.map(e=>(0,r.jsxs)(o.Hj,{children:[(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:ec(e.status)})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.user.name}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,r.jsx)(A.A,{className:"h-3 w-3 mr-1"}),e.user.phone]}),e.user.email&&(0,r.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,r.jsx)(S.A,{className:"h-3 w-3 mr-1"}),e.user.email]})]})]})}),(0,r.jsxs)(o.nA,{children:[(0,r.jsx)(d.E,{className:ei(e.status),children:e.status}),"DROPPED"===e.status&&e.droppedAt&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Dropped: ",(0,u.Yq)(new Date(e.droppedAt))]}),"PAUSED"===e.status&&e.pausedAt&&(0,r.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Paused: ",(0,u.Yq)(new Date(e.pausedAt))]})]}),(0,r.jsx)(o.nA,{children:e.enrollments.length>0?(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium",children:e.enrollments[0].group.name}),(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.enrollments[0].group.course.name}),(0,r.jsxs)("div",{className:"text-xs text-gray-500",children:["Status: ",e.enrollments[0].status]})]}):(0,r.jsx)("span",{className:"text-gray-400",children:"No enrollments"})}),(0,r.jsx)(o.nA,{children:(0,r.jsx)(d.E,{className:ed(e.level),children:e.level.replace("_"," ")})}),(0,r.jsxs)(o.nA,{children:[(0,r.jsx)(d.E,{className:eo(e.paymentStatus),children:e.paymentStatus}),"UNPAID"===e.paymentStatus&&e.unpaidAmount>0&&(0,r.jsxs)("div",{className:"text-xs text-red-600 mt-1",children:["Unpaid: $",e.unpaidAmount.toLocaleString()]})]}),(0,r.jsx)(o.nA,{children:e.branch}),(0,r.jsx)(o.nA,{children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(x.lG,{children:[(0,r.jsx)(x.zM,{asChild:!0,children:(0,r.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>z(e),children:"View Details"})}),(0,r.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(x.c7,{children:[(0,r.jsx)(x.L3,{children:"Student Details"}),(0,r.jsxs)(x.rr,{children:["Complete information about ",W?.user.name]})]}),W&&(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:W.user.name})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:W.user.phone})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:W.user.email||"Not provided"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Level"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:W.level})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Branch"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:W.branch})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Emergency Contact"}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:W.emergencyContact||"Not provided"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"text-sm font-medium",children:"Current Enrollments"}),(0,r.jsx)("div",{className:"mt-2 space-y-2",children:W.enrollments.map(e=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded",children:[(0,r.jsxs)("span",{children:[e.group.course.name," - ",e.group.name]}),(0,r.jsx)(d.E,{className:ei(e.status),children:e.status})]},e.id))})]})]})]})]}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{J(e),G(!0)},children:(0,r.jsx)(C.A,{className:"h-4 w-4"})}),(0,r.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>eu(e.id),className:"text-red-600 hover:text-red-700",children:(0,r.jsx)(P.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===es.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No students found matching your search."})})]})]}),(0,r.jsx)(x.lG,{open:$,onOpenChange:G,children:(0,r.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(x.c7,{children:[(0,r.jsx)(x.L3,{children:"Edit Student"}),(0,r.jsx)(x.rr,{children:"Update student information and academic details."})]}),H&&(0,r.jsx)(E.A,{initialData:{name:H.user.name,phone:H.user.phone,email:H.user.email||"",level:H.level,branch:H.branch,emergencyContact:H.emergencyContact||"",dateOfBirth:H.dateOfBirth?new Date(H.dateOfBirth).toISOString().split("T")[0]:"",address:H.address||""},onSubmit:eh,onCancel:()=>{G(!1),J(null)},isEditing:!0})]})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64934:(e,s,t)=>{Promise.resolve().then(t.bind(t,50188))},72730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},88233:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},96474:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96478:(e,s,t)=>{Promise.resolve().then(t.bind(t,50009))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,8706,7825,3039,2671,5283],()=>t(27601));module.exports=r})();