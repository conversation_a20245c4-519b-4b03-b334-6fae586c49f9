(()=>{var e={};e.id=318,e.ids=[318],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4809:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>A,routeModule:()=>R,serverHooks:()=>j,workAsyncStorage:()=>E,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{DELETE:()=>x,GET:()=>m,POST:()=>w,PUT:()=>f});var a=t(96559),n=t(48088),i=t(37719),o=t(32190),u=t(79464),l=t(45697),d=t(97110),p=t.n(d);let c=l.z.object({name:l.z.string().min(2,"Name must be at least 2 characters"),phone:l.z.string().min(9,"Phone number must be at least 9 characters"),email:l.z.string().email().optional(),role:l.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]),password:l.z.string().min(6,"Password must be at least 6 characters")}),h=l.z.object({name:l.z.string().min(2,"Name must be at least 2 characters").optional(),phone:l.z.string().min(9,"Phone number must be at least 9 characters").optional(),email:l.z.string().email().optional(),role:l.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]).optional(),password:l.z.string().min(6,"Password must be at least 6 characters").optional()});async function m(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"10"),a=r.get("search")||"",n=r.get("role")||"",i=(t-1)*s,l={};a&&(l.OR=[{name:{contains:a,mode:"insensitive"}},{phone:{contains:a}},{email:{contains:a,mode:"insensitive"}}]),n&&(l.role=n);let[d,p]=await Promise.all([u.z.user.findMany({where:l,skip:i,take:s,select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0,updatedAt:!0,studentProfile:{select:{id:!0,level:!0,branch:!0}},teacherProfile:{select:{id:!0,subject:!0,experience:!0,branch:!0}}},orderBy:{createdAt:"desc"}}),u.z.user.count({where:l})]);return o.NextResponse.json({users:d,pagination:{page:t,limit:s,total:p,pages:Math.ceil(p/s)}})}catch(e){return console.error("Error fetching users:",e),o.NextResponse.json({error:"Failed to fetch users"},{status:500})}}async function w(e){try{let r=await e.json(),t=c.parse(r);if(await u.z.user.findUnique({where:{phone:t.phone}}))return o.NextResponse.json({error:"User with this phone number already exists"},{status:400});if(t.email&&await u.z.user.findUnique({where:{email:t.email}}))return o.NextResponse.json({error:"User with this email already exists"},{status:400});let s=await p().hash(t.password,12),a=await u.z.user.create({data:{name:t.name,phone:t.phone,email:t.email,role:t.role,password:s},select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0,updatedAt:!0}});return"STUDENT"===t.role?await u.z.student.create({data:{userId:a.id,level:"A1",branch:"Main"}}):"TEACHER"===t.role&&await u.z.teacher.create({data:{userId:a.id,subject:"English",branch:"Main"}}),o.NextResponse.json(a,{status:201})}catch(e){if(e instanceof l.z.ZodError)return o.NextResponse.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error creating user:",e),o.NextResponse.json({error:"Failed to create user"},{status:500})}}async function f(e){try{let{id:r,...t}=await e.json();if(!r)return o.NextResponse.json({error:"User ID is required"},{status:400});let s=h.parse(t),a=await u.z.user.findUnique({where:{id:r}});if(!a)return o.NextResponse.json({error:"User not found"},{status:404});if(s.phone&&s.phone!==a.phone&&await u.z.user.findUnique({where:{phone:s.phone}}))return o.NextResponse.json({error:"Phone number already exists"},{status:400});if(s.email&&s.email!==a.email&&await u.z.user.findUnique({where:{email:s.email}}))return o.NextResponse.json({error:"Email already exists"},{status:400});let n={};s.name&&(n.name=s.name),s.phone&&(n.phone=s.phone),void 0!==s.email&&(n.email=s.email),s.role&&(n.role=s.role),s.password&&(n.password=await p().hash(s.password,12));let i=await u.z.user.update({where:{id:r},data:n,select:{id:!0,name:!0,phone:!0,email:!0,role:!0,createdAt:!0,updatedAt:!0}});return o.NextResponse.json(i)}catch(e){if(e instanceof l.z.ZodError)return o.NextResponse.json({error:"Validation failed",details:e.errors},{status:400});return console.error("Error updating user:",e),o.NextResponse.json({error:"Failed to update user"},{status:500})}}async function x(e){try{let{searchParams:r}=new URL(e.url),t=r.get("id");if(!t)return o.NextResponse.json({error:"User ID is required"},{status:400});let s=await u.z.user.findUnique({where:{id:t},include:{studentProfile:!0,teacherProfile:!0}});if(!s)return o.NextResponse.json({error:"User not found"},{status:404});return await u.z.$transaction(async e=>{if(s.studentProfile){let r=s.studentProfile.id;await e.payment.deleteMany({where:{studentId:r}}),await e.enrollment.deleteMany({where:{studentId:r}}),await e.attendance.deleteMany({where:{studentId:r}}),await e.assessment.deleteMany({where:{studentId:r}}),await e.student.delete({where:{id:r}})}if(s.teacherProfile){let r=s.teacherProfile.id;await e.group.updateMany({where:{teacherId:r},data:{teacherId:null}}),await e.teacher.delete({where:{id:r}})}await e.user.delete({where:{id:t}})}),o.NextResponse.json({message:"User deleted successfully"})}catch(e){return console.error("Error deleting user:",e),o.NextResponse.json({error:"Failed to delete user"},{status:500})}}let R=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/users/route",pathname:"/api/users",filename:"route",bundlePath:"app/api/users/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\users\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:E,workUnitAsyncStorage:g,serverHooks:j}=R;function A(){return(0,i.patchFetch)({workAsyncStorage:E,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>a});var s=t(96330);let a=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{},97110:e=>{"use strict";e.exports=require("bcryptjs")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697],()=>t(4809));module.exports=s})();