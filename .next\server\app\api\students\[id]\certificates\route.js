(()=>{var e={};e.id=8127,e.ids=[8127],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=a.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},o=await fetch(s,{method:r.method,headers:n,body:r.data?JSON.stringify(r.data):void 0}),i=await o.json();return{success:o.ok,data:i,status:o.status,error:o.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>a,cU:()=>o,g2:()=>s});class o{static async authenticateUser(e,r){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class a{static logRequest(e,r,t,s){let n=new Date().toISOString(),o=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:o,success:t,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36514:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>d});var n=t(96559),o=t(48088),a=t(37719),i=t(32190),u=t(19854),c=t(41098),l=t(79464);async function d(e,{params:r}){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let{id:t}=await r;if("STUDENT"===e.user.role&&e.user.id!==t)return i.NextResponse.json({error:"Forbidden"},{status:403});let s=await l.z.student.findUnique({where:{userId:t},include:{user:{select:{name:!0,email:!0}},enrollments:{include:{group:{include:{course:{select:{name:!0,level:!0,duration:!0}}}}},orderBy:{createdAt:"desc"}},currentGroup:{include:{course:{select:{name:!0,level:!0,duration:!0}}}},assessments:{where:{type:"FINAL_EXAM",passed:!0},include:{group:{include:{course:{select:{name:!0,level:!0}}}}},orderBy:{completedAt:"desc"}}}});if(!s)return i.NextResponse.json({error:"Student not found"},{status:404});let n=[],o=[],a=[];for(let e of s.assessments)if(e.group&&e.completedAt){let r=`INN-${e.group.course.level}-${new Date(e.completedAt).getFullYear()}-${String(Math.floor(1e3*Math.random())).padStart(3,"0")}`;n.push({id:e.id,name:`${e.group.course.name} Certificate`,course:e.group.course.name,completionDate:e.completedAt,grade:e.score&&e.maxScore?e.score>=.9*e.maxScore?"Excellent":e.score>=.8*e.maxScore?"Very Good":e.score>=.7*e.maxScore?"Good":"Satisfactory":"Pass",score:e.score||0,issueDate:new Date(new Date(e.completedAt).getTime()+432e6).toISOString(),certificateNumber:r,status:"issued"})}if(s.currentGroup){let e=new Date(s.currentGroup.startDate),r=7*s.currentGroup.course.duration*864e5,t=Date.now()-e.getTime(),n=Math.min(95,Math.max(5,Math.floor(t/r*100)));o.push({id:s.currentGroup.id,name:`${s.currentGroup.course.name} Certificate`,course:s.currentGroup.course.name,expectedCompletion:new Date(e.getTime()+r).toISOString(),progress:n,status:"in_progress"})}let d=["A1","A2","B1","B2","IELTS"],p=d.indexOf(s.level);if(p>=0&&p<d.length-1){let e=d[p+1];a.push({id:`upcoming-${e}`,name:`General English ${e} Certificate`,course:`General English ${e}`,prerequisite:`Complete ${s.level} level`,estimatedDuration:"12 weeks",status:"upcoming"})}!n.some(e=>e.course.includes("IELTS"))&&["B2","IELTS"].includes(s.level)&&a.push({id:"upcoming-ielts",name:"IELTS Preparation Certificate",course:"IELTS Preparation",prerequisite:"B2 level completion",estimatedDuration:"8 weeks",status:"upcoming"});let m={student:{name:s.user.name,email:s.user.email,level:s.level,branch:s.branch},completed:n,inProgress:o,upcoming:a,statistics:{totalCompleted:n.length,inProgress:o.length,averageGrade:n.length>0?n.reduce((e,r)=>{let t="Excellent"===r.grade?4:"Very Good"===r.grade?3.5:"Good"===r.grade?3:2.5;return e+t},0)/n.length:0}};return i.NextResponse.json(m)}catch(e){return console.error("Error fetching student certificates:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/students/[id]/certificates/route",pathname:"/api/students/[id]/certificates",filename:"route",bundlePath:"app/api/students/[id]/certificates/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\[id]\\certificates\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:g}=p;function v(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},41098:(e,r,t)=>{"use strict";t.d(r,{N:()=>o});var s=t(13581),n=t(7786);let o={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let r=await n.cU.authenticateUser(e.phone,e.password);if(!r.success)return console.error("Authentication failed:",r.error),null;let t=r.data.user;if(!t)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(t.role))return console.error("User role not allowed on staff server:",t.role),null;return{id:t.id,phone:t.phone,name:t.name,email:t.email,role:t.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role||null),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3412],()=>t(36514));module.exports=s})();