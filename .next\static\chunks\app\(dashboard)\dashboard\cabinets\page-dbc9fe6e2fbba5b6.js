(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1779],{88:(e,a,s)=>{"use strict";s.d(a,{d:()=>n});var t=s(5155),r=s(2115),l=s(4884),i=s(3999);let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.bL,{className:(0,i.cn)("peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",s),...r,ref:a,children:(0,t.jsx)(l.zi,{className:(0,i.cn)("pointer-events-none block h-5 w-5 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0")})})});n.displayName=l.bL.displayName},2525:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2714:(e,a,s)=>{"use strict";s.d(a,{J:()=>d});var t=s(5155),r=s(2115),l=s(968),i=s(2085),n=s(3999);let c=(0,i.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(l.b,{ref:a,className:(0,n.cn)(c(),s),...r})});d.displayName=l.b.displayName},4516:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},4884:(e,a,s)=>{"use strict";s.d(a,{bL:()=>g,zi:()=>w});var t=s(2115),r=s(5185),l=s(6101),i=s(6081),n=s(5845),c=s(5503),d=s(1275),o=s(3655),h=s(5155),m="Switch",[u,x]=(0,i.A)(m),[p,j]=u(m),b=t.forwardRef((e,a)=>{let{__scopeSwitch:s,name:i,checked:c,defaultChecked:d,required:u,disabled:x,value:j="on",onCheckedChange:b,form:f,...y}=e,[g,w]=t.useState(null),k=(0,l.s)(a,e=>w(e)),A=t.useRef(!1),C=!g||f||!!g.closest("form"),[M,S]=(0,n.i)({prop:c,defaultProp:null!=d&&d,onChange:b,caller:m});return(0,h.jsxs)(p,{scope:s,checked:M,disabled:x,children:[(0,h.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":M,"aria-required":u,"data-state":N(M),"data-disabled":x?"":void 0,disabled:x,value:j,...y,ref:k,onClick:(0,r.m)(e.onClick,e=>{S(e=>!e),C&&(A.current=e.isPropagationStopped(),A.current||e.stopPropagation())})}),C&&(0,h.jsx)(v,{control:g,bubbles:!A.current,name:i,value:j,checked:M,required:u,disabled:x,form:f,style:{transform:"translateX(-100%)"}})]})});b.displayName=m;var f="SwitchThumb",y=t.forwardRef((e,a)=>{let{__scopeSwitch:s,...t}=e,r=j(f,s);return(0,h.jsx)(o.sG.span,{"data-state":N(r.checked),"data-disabled":r.disabled?"":void 0,...t,ref:a})});y.displayName=f;var v=t.forwardRef((e,a)=>{let{__scopeSwitch:s,control:r,checked:i,bubbles:n=!0,...o}=e,m=t.useRef(null),u=(0,l.s)(m,a),x=(0,c.Z)(i),p=(0,d.X)(r);return t.useEffect(()=>{let e=m.current;if(!e)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(x!==i&&a){let s=new Event("click",{bubbles:n});a.call(e,i),e.dispatchEvent(s)}},[x,i,n]),(0,h.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i,...o,tabIndex:-1,ref:u,style:{...o.style,...p,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}v.displayName="SwitchBubbleInput";var g=b,w=y},5673:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>z});var t=s(5155),r=s(2115),l=s(8482),i=s(7168),n=s(9852),c=s(8145),d=s(8524),o=s(9840),h=s(5784),m=s(9026),u=s(7705),x=s(7624),p=s(4616),j=s(7924),b=s(4516),f=s(7580),y=s(2657),v=s(4621),N=s(2525),g=s(2177),w=s(221),k=s(1153),A=s(2714),C=s(9474),M=s(88),S=s(2895);let E=(0,S.A)("Building",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["path",{d:"M9 22v-4h6v4",key:"r93iot"}],["path",{d:"M8 6h.01",key:"1dz90k"}],["path",{d:"M16 6h.01",key:"1x0f13"}],["path",{d:"M12 6h.01",key:"1vi96p"}],["path",{d:"M12 10h.01",key:"1nrarc"}],["path",{d:"M12 14h.01",key:"1etili"}],["path",{d:"M16 10h.01",key:"1m94wz"}],["path",{d:"M16 14h.01",key:"1gbofw"}],["path",{d:"M8 10h.01",key:"19clt8"}],["path",{d:"M8 14h.01",key:"6423bh"}]]),B=(0,S.A)("Hash",[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]]);var F=s(7108);let T=k.Ik({name:k.Yj().min(2,"Cabinet name must be at least 2 characters"),number:k.Yj().min(1,"Cabinet number is required"),capacity:k.ai().min(1,"Capacity must be at least 1").max(100,"Capacity cannot exceed 100"),floor:k.ai().optional(),building:k.Yj().optional(),branch:k.Yj().min(1,"Branch is required"),equipment:k.Yj().optional(),notes:k.Yj().optional(),isActive:k.zM().default(!0)}),q=[{value:"Main Branch",label:"Main Branch"},{value:"Branch",label:"Branch"}];function L(e){var a;let{initialData:s,onSubmit:l,onCancel:c,isEditing:d=!1}=e,[o,u]=(0,r.useState)(!1),[p,j]=(0,r.useState)(null),{register:y,handleSubmit:v,setValue:N,watch:k,formState:{errors:S}}=(0,g.mN)({resolver:(0,w.u)(T),defaultValues:{name:(null==s?void 0:s.name)||"",number:(null==s?void 0:s.number)||"",capacity:(null==s?void 0:s.capacity)||20,floor:(null==s?void 0:s.floor)||void 0,building:(null==s?void 0:s.building)||"",branch:(null==s?void 0:s.branch)||"",equipment:(null==s?void 0:s.equipment)||"",notes:(null==s?void 0:s.notes)||"",isActive:null==(a=null==s?void 0:s.isActive)||a}}),L=k("isActive"),R=async e=>{try{u(!0),j(null),await l(e)}catch(e){j(e instanceof Error?e.message:"An error occurred")}finally{u(!1)}};return(0,t.jsxs)("form",{onSubmit:v(R),className:"space-y-6",children:[p&&(0,t.jsx)(m.Fc,{variant:"destructive",children:(0,t.jsx)(m.TN,{children:p})}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(E,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Basic Information"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"name",children:"Cabinet Name *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(E,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{id:"name",...y("name"),placeholder:"e.g., Computer Lab 1",className:"pl-10"})]}),S.name&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:S.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"number",children:"Cabinet Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(B,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{id:"number",...y("number"),placeholder:"e.g., 101, A-205",className:"pl-10"})]}),S.number&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:S.number.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"capacity",children:"Capacity *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(f.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{id:"capacity",type:"number",min:"1",max:"100",...y("capacity",{valueAsNumber:!0}),placeholder:"20",className:"pl-10"})]}),S.capacity&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:S.capacity.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"branch",children:"Branch *"}),(0,t.jsxs)(h.l6,{value:k("branch"),onValueChange:e=>N("branch",e),children:[(0,t.jsx)(h.bq,{children:(0,t.jsx)(h.yv,{placeholder:"Select branch"})}),(0,t.jsx)(h.gC,{children:q.map(e=>(0,t.jsx)(h.eb,{value:e.value,children:e.label},e.value))})]}),S.branch&&(0,t.jsx)("p",{className:"text-sm text-red-600",children:S.branch.message})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(b.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Location Details"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"floor",children:"Floor"}),(0,t.jsx)(n.p,{id:"floor",type:"number",min:"0",...y("floor",{valueAsNumber:!0}),placeholder:"e.g., 1, 2, 3"})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"building",children:"Building"}),(0,t.jsx)(n.p,{id:"building",...y("building"),placeholder:"e.g., Main Building, Block A"})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(F.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Additional Information"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"equipment",children:"Equipment"}),(0,t.jsx)(C.T,{id:"equipment",...y("equipment"),placeholder:"List available equipment (projector, whiteboard, computers, etc.)",rows:3})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(A.J,{htmlFor:"notes",children:"Notes"}),(0,t.jsx)(C.T,{id:"notes",...y("notes"),placeholder:"Additional notes about the cabinet",rows:3})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(M.d,{id:"isActive",checked:L,onCheckedChange:e=>N("isActive",e)}),(0,t.jsx)(A.J,{htmlFor:"isActive",children:"Active Cabinet"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-2 pt-6 border-t",children:[c&&(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:c,children:"Cancel"}),(0,t.jsxs)(i.$,{type:"submit",disabled:o,children:[o&&(0,t.jsx)(x.A,{className:"mr-2 h-4 w-4 animate-spin"}),d?"Update Cabinet":"Create Cabinet"]})]})]})}var R=s(6874),P=s.n(R);function z(){let{currentBranch:e}=(0,u.O)(),[a,s]=(0,r.useState)([]),[g,w]=(0,r.useState)(!0),[k,A]=(0,r.useState)(null),[C,M]=(0,r.useState)(""),[S,E]=(0,r.useState)("all"),[B,F]=(0,r.useState)(!1),[T,q]=(0,r.useState)(!1),[R,z]=(0,r.useState)(null),O=(0,r.useCallback)(async()=>{try{w(!0);let a=new URLSearchParams({branch:e.id,..."all"!==S&&{isActive:S},...C&&{search:C}}),t=await fetch("/api/cabinets?".concat(a));if(!t.ok)throw Error("Failed to fetch cabinets");let r=await t.json();s(r.cabinets)}catch(e){A(e instanceof Error?e.message:"An error occurred")}finally{w(!1)}},[e.id,S,C]);(0,r.useEffect)(()=>{O()},[O]);let I=async a=>{let s=await fetch("/api/cabinets",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...a,branch:e.id})});if(!s.ok)throw Error((await s.json()).error||"Failed to create cabinet");F(!1),O()},J=async e=>{if(!R)return;let a=await fetch("/api/cabinets/".concat(R.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!a.ok)throw Error((await a.json()).error||"Failed to update cabinet");q(!1),z(null),O()},H=async e=>{if(confirm("Are you sure you want to delete this cabinet? This action cannot be undone."))try{let a=await fetch("/api/cabinets/".concat(e),{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to delete cabinet")}O()}catch(e){A(e instanceof Error?e.message:"An error occurred")}},Z=a.filter(e=>{var a;let s=e.name.toLowerCase().includes(C.toLowerCase())||e.number.toLowerCase().includes(C.toLowerCase())||(null==(a=e.building)?void 0:a.toLowerCase().includes(C.toLowerCase())),t="all"===S||"true"===S&&e.isActive||"false"===S&&!e.isActive;return s&&t});return g?(0,t.jsx)("div",{className:"flex items-center justify-center h-64",children:(0,t.jsx)(x.A,{className:"h-8 w-8 animate-spin"})}):(0,t.jsxs)("div",{className:"space-y-6",children:[k&&(0,t.jsx)(m.Fc,{variant:"destructive",children:(0,t.jsx)(m.TN,{children:k})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Cabinets Management"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Manage classroom cabinets and their schedules"})]}),(0,t.jsxs)(o.lG,{open:B,onOpenChange:F,children:[(0,t.jsx)(o.zM,{asChild:!0,children:(0,t.jsxs)(i.$,{children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Cabinet"]})}),(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Add New Cabinet"}),(0,t.jsx)(o.rr,{children:"Create a new cabinet for classroom management."})]}),(0,t.jsx)(L,{onSubmit:I,onCancel:()=>F(!1),isEditing:!1})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsx)(l.ZB,{children:"Filters"})}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(n.p,{placeholder:"Search cabinets...",value:C,onChange:e=>M(e.target.value),className:"pl-10"})]})}),(0,t.jsxs)(h.l6,{value:S,onValueChange:E,children:[(0,t.jsx)(h.bq,{className:"w-full sm:w-48",children:(0,t.jsx)(h.yv,{placeholder:"Filter by status"})}),(0,t.jsxs)(h.gC,{children:[(0,t.jsx)(h.eb,{value:"all",children:"All Cabinets"}),(0,t.jsx)(h.eb,{value:"true",children:"Active Only"}),(0,t.jsx)(h.eb,{value:"false",children:"Inactive Only"})]})]})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Cabinets (",Z.length,")"]}),(0,t.jsx)(l.BT,{children:"Manage classroom cabinets and their assignments"})]}),(0,t.jsxs)(l.Wu,{children:[(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nd,{children:"Cabinet"}),(0,t.jsx)(d.nd,{children:"Location"}),(0,t.jsx)(d.nd,{children:"Capacity"}),(0,t.jsx)(d.nd,{children:"Groups"}),(0,t.jsx)(d.nd,{children:"Schedules"}),(0,t.jsx)(d.nd,{children:"Status"}),(0,t.jsx)(d.nd,{children:"Actions"})]})}),(0,t.jsx)(d.BF,{children:Z.map(e=>(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500",children:["#",e.number]})]})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm",children:[e.building&&"".concat(e.building,", "),void 0!==e.floor&&"Floor ".concat(e.floor)]})]})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 text-gray-400"}),(0,t.jsx)("span",{children:e.capacity})]})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)(c.E,{variant:"outline",children:[e._count.groups," groups"]})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)(c.E,{variant:"outline",children:[e._count.schedules," schedules"]})}),(0,t.jsx)(d.nA,{children:(0,t.jsx)(c.E,{variant:e.isActive?"default":"secondary",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(P(),{href:"/dashboard/cabinets/".concat(e.id),children:(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:(0,t.jsx)(y.A,{className:"h-4 w-4"})})}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",onClick:()=>{z(e),q(!0)},children:(0,t.jsx)(v.A,{className:"h-4 w-4"})}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",onClick:()=>H(e.id),children:(0,t.jsx)(N.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===Z.length&&(0,t.jsx)("div",{className:"text-center py-8",children:(0,t.jsx)("p",{className:"text-gray-500",children:"No cabinets found matching your criteria."})})]})]}),(0,t.jsx)(o.lG,{open:T,onOpenChange:q,children:(0,t.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(o.c7,{children:[(0,t.jsx)(o.L3,{children:"Edit Cabinet"}),(0,t.jsx)(o.rr,{children:"Update cabinet information and settings."})]}),R&&(0,t.jsx)(L,{initialData:R,onSubmit:J,onCancel:()=>{q(!1),z(null)},isEditing:!0})]})})]})}},7108:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]])},7580:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7624:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7705:(e,a,s)=>{"use strict";s.d(a,{BranchProvider:()=>n,O:()=>c,Z:()=>d});var t=s(5155),r=s(2115);let l=(0,r.createContext)(void 0),i=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function n(e){let{children:a}=e,[s,n]=(0,r.useState)(i[0]),[c]=(0,r.useState)(i),[d,o]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let a=c.find(a=>a.id===e);a&&n(a)}o(!1)},[c]),(0,t.jsx)(l.Provider,{value:{currentBranch:s,branches:c,switchBranch:e=>{let a=c.find(a=>a.id===e);a&&(n(a),localStorage.setItem("selectedBranch",e))},isLoading:d},children:a})}function c(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,r.useContext)(l)||null}},7883:(e,a,s)=>{Promise.resolve().then(s.bind(s,5673))},7924:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8524:(e,a,s)=>{"use strict";s.d(a,{A0:()=>n,BF:()=>c,Hj:()=>d,XI:()=>i,nA:()=>h,nd:()=>o});var t=s(5155),r=s(2115),l=s(3999);let i=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",s),...r})})});i.displayName="Table";let n=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",s),...r})});n.displayName="TableHeader";let c=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",s),...r})});c.displayName="TableBody",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",s),...r})}).displayName="TableFooter";let d=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",s),...r})});d.displayName="TableRow";let o=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",s),...r})});o.displayName="TableHead";let h=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",s),...r})});h.displayName="TableCell",r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",s),...r})}).displayName="TableCaption"}},e=>{var a=a=>e(e.s=a);e.O(0,[5003,6221,4358,1071,2356,6874,7968,8441,1684,7358],()=>a(7883)),_N_E=e.O()}]);