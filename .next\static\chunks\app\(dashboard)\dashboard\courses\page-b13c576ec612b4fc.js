(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[980],{2525:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},4030:(e,s,t)=>{Promise.resolve().then(t.bind(t,7559))},4186:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},4884:(e,s,t)=>{"use strict";t.d(s,{bL:()=>A,zi:()=>w});var r=t(2115),a=t(5185),l=t(6101),c=t(6081),i=t(5845),n=t(5503),d=t(1275),o=t(3655),x=t(5155),h="Switch",[u,m]=(0,c.A)(h),[j,p]=u(h),y=r.forwardRef((e,s)=>{let{__scopeSwitch:t,name:c,checked:n,defaultChecked:d,required:u,disabled:m,value:p="on",onCheckedChange:y,form:f,...v}=e,[A,w]=r.useState(null),b=(0,l.s)(s,e=>w(e)),k=r.useRef(!1),C=!A||f||!!A.closest("form"),[S,E]=(0,i.i)({prop:n,defaultProp:null!=d&&d,onChange:y,caller:h});return(0,x.jsxs)(j,{scope:t,checked:S,disabled:m,children:[(0,x.jsx)(o.sG.button,{type:"button",role:"switch","aria-checked":S,"aria-required":u,"data-state":N(S),"data-disabled":m?"":void 0,disabled:m,value:p,...v,ref:b,onClick:(0,a.m)(e.onClick,e=>{E(e=>!e),C&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})}),C&&(0,x.jsx)(g,{control:A,bubbles:!k.current,name:c,value:p,checked:S,required:u,disabled:m,form:f,style:{transform:"translateX(-100%)"}})]})});y.displayName=h;var f="SwitchThumb",v=r.forwardRef((e,s)=>{let{__scopeSwitch:t,...r}=e,a=p(f,t);return(0,x.jsx)(o.sG.span,{"data-state":N(a.checked),"data-disabled":a.disabled?"":void 0,...r,ref:s})});v.displayName=f;var g=r.forwardRef((e,s)=>{let{__scopeSwitch:t,control:a,checked:c,bubbles:i=!0,...o}=e,h=r.useRef(null),u=(0,l.s)(h,s),m=(0,n.Z)(c),j=(0,d.X)(a);return r.useEffect(()=>{let e=h.current;if(!e)return;let s=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==c&&s){let t=new Event("click",{bubbles:i});s.call(e,c),e.dispatchEvent(t)}},[m,c,i]),(0,x.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:c,...o,tabIndex:-1,ref:u,style:{...o.style,...j,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})});function N(e){return e?"checked":"unchecked"}g.displayName="SwitchBubbleInput";var A=y,w=v},5040:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},5868:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},7559:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>w});var r=t(5155),a=t(2115),l=t(8482),c=t(7168),i=t(8145),n=t(9852),d=t(8524),o=t(9840),x=t(9026),h=t(3999),u=t(7624),m=t(4616),j=t(7924),p=t(5040),y=t(4186),f=t(5868),v=t(7580),g=t(4621),N=t(2525),A=t(1039);function w(){let[e,s]=(0,a.useState)([]),[t,w]=(0,a.useState)(!0),[b,k]=(0,a.useState)(""),[C,S]=(0,a.useState)(!1),[E,M]=(0,a.useState)(!1),[L,T]=(0,a.useState)(null),[P,H]=(0,a.useState)(!1),[O,Z]=(0,a.useState)(null);(0,a.useEffect)(()=>{_()},[]);let _=async()=>{try{w(!0);let e=await fetch("/api/courses"),t=await e.json();s(t.courses||[]),Z(null)}catch(e){console.error("Error fetching courses:",e),Z("Failed to fetch courses")}finally{w(!1)}},q=e.filter(e=>{var s;return e.name.toLowerCase().includes(b.toLowerCase())||e.level.toLowerCase().includes(b.toLowerCase())||(null==(s=e.description)?void 0:s.toLowerCase().includes(b.toLowerCase()))}),B=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800",z=e=>e?"bg-green-100 text-green-800":"bg-red-100 text-red-800",I=e=>e.groups.reduce((e,s)=>e+s._count.enrollments,0),R=async e=>{H(!0),Z(null);try{let s=await fetch("/api/courses",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create course")}S(!1),_()}catch(e){Z(e instanceof Error?e.message:"An error occurred")}finally{H(!1)}},D=async e=>{if(L){H(!0),Z(null);try{let s=await fetch("/api/courses/".concat(L.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update course")}M(!1),T(null),_()}catch(e){Z(e instanceof Error?e.message:"An error occurred")}finally{H(!1)}}},F=async e=>{if(confirm("Are you sure you want to delete this course? This action cannot be undone."))try{let s=await fetch("/api/courses/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete course")}_()}catch(e){Z(e instanceof Error?e.message:"An error occurred")}};return t?(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading courses..."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[O&&(0,r.jsx)(x.Fc,{variant:"destructive",children:(0,r.jsx)(x.TN,{children:O})}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Courses Management"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage course catalog and pricing"})]}),(0,r.jsxs)(o.lG,{open:C,onOpenChange:S,children:[(0,r.jsx)(o.zM,{asChild:!0,children:(0,r.jsxs)(c.$,{children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Add Course"]})}),(0,r.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(o.c7,{children:[(0,r.jsx)(o.L3,{children:"Add New Course"}),(0,r.jsx)(o.rr,{children:"Create a new course offering with pricing and duration details."})]}),(0,r.jsx)(A.A,{onSubmit:R,onCancel:()=>S(!1),isEditing:!1})]})]})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsx)(l.aR,{children:(0,r.jsx)(l.ZB,{children:"Search Courses"})}),(0,r.jsx)(l.Wu,{children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(n.p,{placeholder:"Search by course name, level, or description...",value:b,onChange:e=>k(e.target.value),className:"pl-10"})]})})]}),(0,r.jsxs)(l.Zp,{children:[(0,r.jsxs)(l.aR,{children:[(0,r.jsxs)(l.ZB,{children:["Courses (",q.length,")"]}),(0,r.jsx)(l.BT,{children:"Complete list of available courses"})]}),(0,r.jsxs)(l.Wu,{children:[(0,r.jsxs)(d.XI,{children:[(0,r.jsx)(d.A0,{children:(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nd,{children:"Course"}),(0,r.jsx)(d.nd,{children:"Level"}),(0,r.jsx)(d.nd,{children:"Duration"}),(0,r.jsx)(d.nd,{children:"Price"}),(0,r.jsx)(d.nd,{children:"Groups"}),(0,r.jsx)(d.nd,{children:"Students"}),(0,r.jsx)(d.nd,{children:"Status"}),(0,r.jsx)(d.nd,{children:"Actions"})]})}),(0,r.jsx)(d.BF,{children:q.map(e=>(0,r.jsxs)(d.Hj,{children:[(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-blue-600"})})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"font-medium text-gray-900",children:e.name}),e.description&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,r.jsx)(d.nA,{children:(0,r.jsx)(i.E,{className:B(e.level),children:e.level.replace("_"," ")})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(y.A,{className:"h-3 w-3 mr-1 text-gray-500"}),e.duration," weeks"]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex items-center text-sm font-medium",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1 text-gray-500"}),(0,h.vv)(Number(e.price))]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(v.A,{className:"h-3 w-3 mr-1 text-gray-500"}),e._count.groups," groups"]})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex items-center text-sm",children:[(0,r.jsx)(v.A,{className:"h-3 w-3 mr-1 text-gray-500"}),I(e)," students"]})}),(0,r.jsx)(d.nA,{children:(0,r.jsx)(i.E,{className:z(e.isActive),children:e.isActive?"Active":"Inactive"})}),(0,r.jsx)(d.nA,{children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>{T(e),M(!0)},children:(0,r.jsx)(g.A,{className:"h-4 w-4"})}),(0,r.jsx)(c.$,{variant:"outline",size:"sm",onClick:()=>F(e.id),className:"text-red-600 hover:text-red-700",children:(0,r.jsx)(N.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===q.length&&(0,r.jsx)("div",{className:"text-center py-12",children:(0,r.jsx)("p",{className:"text-gray-500",children:"No courses found matching your search."})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Courses"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Courses"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.filter(e=>e.isActive).length})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"h-8 w-8 text-yellow-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Groups"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.reduce((e,s)=>e+s._count.groups,0)})]})]})})}),(0,r.jsx)(l.Zp,{children:(0,r.jsx)(l.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Avg. Price"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.length>0?(0,h.vv)(e.reduce((e,s)=>e+Number(s.price),0)/e.length):(0,h.vv)(0)})]})]})})})]}),(0,r.jsx)(o.lG,{open:E,onOpenChange:M,children:(0,r.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,r.jsxs)(o.c7,{children:[(0,r.jsx)(o.L3,{children:"Edit Course"}),(0,r.jsx)(o.rr,{children:"Update course information, pricing, and availability."})]}),L&&(0,r.jsx)(A.A,{initialData:{name:L.name,level:L.level,description:L.description||"",duration:L.duration,price:L.price,isActive:L.isActive},onSubmit:D,onCancel:()=>{M(!1),T(null)},isEditing:!0})]})})]})}},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7624:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,2356,7968,5362,8441,1684,7358],()=>s(4030)),_N_E=e.O()}]);