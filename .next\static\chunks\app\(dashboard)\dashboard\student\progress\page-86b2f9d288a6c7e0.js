(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1153],{2895:(e,r,t)=>{"use strict";t.d(r,{A:()=>l});var s=t(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,r)=>{let t=(0,s.forwardRef)((t,l)=>{let{color:i="currentColor",size:c=24,strokeWidth:o=2,absoluteStrokeWidth:d,className:u="",children:m,...x}=t;return(0,s.createElement)("svg",{ref:l,...a,width:c,height:c,stroke:i,strokeWidth:d?24*Number(o)/Number(c):o,className:["lucide","lucide-".concat(n(e)),u].join(" "),...x},[...r.map(e=>{let[r,t]=e;return(0,s.createElement)(r,t)}),...Array.isArray(m)?m:[m]])});return t.displayName="".concat(e),t}},2915:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3655:(e,r,t)=>{"use strict";t.d(r,{hO:()=>c,sG:()=>i});var s=t(2115),a=t(7650),n=t(9708),l=t(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let t=(0,n.TL)(`Primitive.${r}`),a=s.forwardRef((e,s)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?t:r,{...n,ref:s})});return a.displayName=`Primitive.${r}`,{...e,[r]:a}},{});function c(e,r){e&&a.flushSync(()=>e.dispatchEvent(r))}},3999:(e,r,t)=>{"use strict";t.d(r,{Yq:()=>i,cn:()=>n,r6:()=>c,vv:()=>l});var s=t(2596),a=t(9688);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,s.$)(r))}function l(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(r)}function c(e){let r="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(r)}},5040:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},5947:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>T});var s=t(5155),a=t(2115),n=t(8482),l=t(8145),i=t(6081),c=t(3655),o="Progress",[d,u]=(0,i.A)(o),[m,x]=d(o),p=a.forwardRef((e,r)=>{var t,a,n,l;let{__scopeProgress:i,value:o=null,max:d,getValueLabel:u=h,...x}=e;(d||0===d)&&!j(d)&&console.error((t="".concat(d),a="Progress","Invalid prop `max` of value `".concat(t,"` supplied to `").concat(a,"`. Only numbers greater than 0 are valid max values. Defaulting to `").concat(100,"`.")));let p=j(d)?d:100;null===o||N(o,p)||console.error((n="".concat(o),l="Progress","Invalid prop `value` of value `".concat(n,"` supplied to `").concat(l,"`. The `value` prop must be:\n  - a positive number\n  - less than the value passed to `max` (or ").concat(100," if no `max` prop is set)\n  - `null` or `undefined` if the progress is indeterminate.\n\nDefaulting to `null`.")));let f=N(o,p)?o:null,v=y(f)?u(f,p):void 0;return(0,s.jsx)(m,{scope:i,value:f,max:p,children:(0,s.jsx)(c.sG.div,{"aria-valuemax":p,"aria-valuemin":0,"aria-valuenow":y(f)?f:void 0,"aria-valuetext":v,role:"progressbar","data-state":g(f,p),"data-value":null!=f?f:void 0,"data-max":p,...x,ref:r})})});p.displayName=o;var f="ProgressIndicator",v=a.forwardRef((e,r)=>{var t;let{__scopeProgress:a,...n}=e,l=x(f,a);return(0,s.jsx)(c.sG.div,{"data-state":g(l.value,l.max),"data-value":null!=(t=l.value)?t:void 0,"data-max":l.max,...n,ref:r})});function h(e,r){return"".concat(Math.round(e/r*100),"%")}function g(e,r){return null==e?"indeterminate":e===r?"complete":"loading"}function y(e){return"number"==typeof e}function j(e){return y(e)&&!isNaN(e)&&e>0}function N(e,r){return y(e)&&!isNaN(e)&&e<=r&&e>=0}v.displayName=f;var b=t(3999);let w=a.forwardRef((e,r)=>{let{className:t,value:a,...n}=e;return(0,s.jsx)(p,{ref:r,className:(0,b.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",t),...n,children:(0,s.jsx)(v,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:"translateX(-".concat(100-(a||0),"%)")}})})});w.displayName=p.displayName;var k=t(9026),A=t(7624),R=t(6785),C=t(5040),S=t(2915),B=t(9037);let E=(0,t(2895).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);function T(){var e,r,t,i,c,o,d,u,m,x;let[p,f]=(0,a.useState)(null),[v,h]=(0,a.useState)(!0),[g,y]=(0,a.useState)(null);(0,a.useEffect)(()=>{j()},[]);let j=async()=>{try{h(!0);let e=await fetch("/api/students/current/progress");if(!e.ok)throw Error("Failed to fetch progress data");let r=await e.json();f(r)}catch(e){console.error("Error fetching progress data:",e),y("Failed to load progress data"),f({student:{name:"Student",level:"B1",nextLevel:"B2",branch:"Main Branch"},progress:{overall:65,attendance:85,averageScore:78},skills:[{name:"Speaking",progress:70,level:"B1+"},{name:"Listening",progress:65,level:"B1"},{name:"Reading",progress:75,level:"B1+"},{name:"Writing",progress:55,level:"B1-"},{name:"Grammar",progress:80,level:"B2-"},{name:"Vocabulary",progress:60,level:"B1"}],achievements:[{name:"Perfect Attendance",date:"2024-01-10",icon:"\uD83C\uDFAF"},{name:"Grammar Master",date:"2024-01-05",icon:"\uD83D\uDCDA"},{name:"Speaking Champion",date:"2023-12-20",icon:"\uD83D\uDDE3️"}],recentActivity:{assessments:[{testName:"Unit 5 Test",score:85,maxScore:100,completedAt:"2024-01-12"},{testName:"Speaking Assessment",score:78,maxScore:100,completedAt:"2024-01-08"},{testName:"Vocabulary Quiz",score:92,maxScore:100,completedAt:"2024-01-05"}]}})}finally{h(!1)}},N=e=>e>=80?"bg-green-500":e>=60?"bg-yellow-500":"bg-red-500",b=(e,r)=>{let t=e/r*100;return t>=80?"text-green-600":t>=60?"text-yellow-600":"text-red-600"};return v?(0,s.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,s.jsx)(A.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading progress data..."})]}):g?(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)(k.Fc,{variant:"destructive",children:(0,s.jsx)(k.TN,{children:g})})}):p?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Progress"}),(0,s.jsx)("p",{className:"text-gray-600",children:"Track your learning journey and achievements"})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(R.A,{className:"h-5 w-5 mr-2"}),"Level Progress"]}),(0,s.jsxs)(n.BT,{children:["Your progress from ",null==(e=p.student)?void 0:e.level," to ",null==(r=p.student)?void 0:r.nextLevel]})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(l.E,{className:"bg-yellow-100 text-yellow-800",children:["Current: ",null==(t=p.student)?void 0:t.level]}),(0,s.jsx)("span",{className:"text-gray-400",children:"→"}),(0,s.jsxs)(l.E,{className:"bg-blue-100 text-blue-800",children:["Target: ",null==(i=p.student)?void 0:i.nextLevel]})]}),(0,s.jsxs)("span",{className:"text-2xl font-bold",children:[null==(c=p.progress)?void 0:c.overall,"%"]})]}),(0,s.jsx)(w,{value:null==(o=p.progress)?void 0:o.overall,className:"h-3"}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["You're ",100-((null==(d=p.progress)?void 0:d.overall)||0),"% away from reaching ",null==(u=p.student)?void 0:u.nextLevel," level!"]})]})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(C.A,{className:"h-5 w-5 mr-2"}),"Skills Assessment"]}),(0,s.jsx)(n.BT,{children:"Your performance across different language skills"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:p.skills.map((e,r)=>(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("span",{className:"font-medium",children:e.name}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(l.E,{variant:"outline",className:"text-xs",children:e.level}),(0,s.jsxs)("span",{className:"text-sm font-medium",children:[e.progress,"%"]})]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,s.jsx)("div",{className:"h-2 rounded-full ".concat(N(e.progress)),style:{width:"".concat(e.progress,"%")}})})]},r))})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(S.A,{className:"h-5 w-5 mr-2"}),"Recent Test Results"]}),(0,s.jsx)(n.BT,{children:"Your latest assessment scores"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"space-y-4",children:(null==(x=p.recentActivity)||null==(m=x.assessments)?void 0:m.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:e.testName}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.completedAt).toLocaleDateString()})]}),(0,s.jsxs)("div",{className:"text-right",children:[(0,s.jsxs)("div",{className:"text-lg font-bold ".concat(b(e.score,e.maxScore)),children:[e.score,"/",e.maxScore]}),(0,s.jsxs)("div",{className:"text-sm text-gray-500",children:[Math.round(e.score/e.maxScore*100),"%"]})]})]},r)))||(0,s.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent assessments available."})})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsxs)(n.aR,{children:[(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(B.A,{className:"h-5 w-5 mr-2"}),"Achievements"]}),(0,s.jsx)(n.BT,{children:"Your learning milestones and accomplishments"})]}),(0,s.jsx)(n.Wu,{children:(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:p.achievements.map((e,r)=>(0,s.jsxs)("div",{className:"flex items-center space-x-3 p-4 border rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50",children:[(0,s.jsx)("div",{className:"text-2xl",children:e.icon}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-medium",children:e.name}),(0,s.jsx)("p",{className:"text-sm text-gray-500",children:e.date})]})]},r))})})]}),(0,s.jsxs)(n.Zp,{children:[(0,s.jsx)(n.aR,{children:(0,s.jsxs)(n.ZB,{className:"flex items-center",children:[(0,s.jsx)(E,{className:"h-5 w-5 mr-2"}),"Personalized Learning Tips"]})}),(0,s.jsx)(n.Wu,{children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("div",{className:"p-3 bg-blue-50 border-l-4 border-blue-400 rounded",children:(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Focus on Writing:"})," Your writing skills need improvement. Practice daily writing exercises to reach B1+ level."]})}),(0,s.jsx)("div",{className:"p-3 bg-green-50 border-l-4 border-green-400 rounded",children:(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Great Grammar Progress:"})," You're excelling in grammar! Keep up the excellent work."]})}),(0,s.jsx)("div",{className:"p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded",children:(0,s.jsxs)("p",{className:"text-sm",children:[(0,s.jsx)("strong",{children:"Vocabulary Building:"})," Expand your vocabulary by reading more English texts and using new words in conversations."]})})]})})]})]}):(0,s.jsx)("div",{className:"space-y-6",children:(0,s.jsx)(k.Fc,{children:(0,s.jsx)(k.TN,{children:"No progress data available."})})})}},6081:(e,r,t)=>{"use strict";t.d(r,{A:()=>l,q:()=>n});var s=t(2115),a=t(5155);function n(e,r){let t=s.createContext(r),n=e=>{let{children:r,...n}=e,l=s.useMemo(()=>n,Object.values(n));return(0,a.jsx)(t.Provider,{value:l,children:r})};return n.displayName=e+"Provider",[n,function(a){let n=s.useContext(t);if(n)return n;if(void 0!==r)return r;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function l(e,r=[]){let t=[],n=()=>{let r=t.map(e=>s.createContext(e));return function(t){let a=t?.[e]||r;return s.useMemo(()=>({[`__scope${e}`]:{...t,[e]:a}}),[t,a])}};return n.scopeName=e,[function(r,n){let l=s.createContext(n),i=t.length;t=[...t,n];let c=r=>{let{scope:t,children:n,...c}=r,o=t?.[e]?.[i]||l,d=s.useMemo(()=>c,Object.values(c));return(0,a.jsx)(o.Provider,{value:d,children:n})};return c.displayName=r+"Provider",[c,function(t,a){let c=a?.[e]?.[i]||l,o=s.useContext(c);if(o)return o;if(void 0!==n)return n;throw Error(`\`${t}\` must be used within \`${r}\``)}]},function(...e){let r=e[0];if(1===e.length)return r;let t=()=>{let t=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=t.reduce((r,{useScope:t,scopeName:s})=>{let a=t(e)[`__scope${s}`];return{...r,...a}},{});return s.useMemo(()=>({[`__scope${r.scopeName}`]:a}),[a])}};return t.scopeName=r.scopeName,t}(n,...r)]}},6101:(e,r,t)=>{"use strict";t.d(r,{s:()=>l,t:()=>n});var s=t(2115);function a(e,r){if("function"==typeof e)return e(r);null!=e&&(e.current=r)}function n(...e){return r=>{let t=!1,s=e.map(e=>{let s=a(e,r);return t||"function"!=typeof s||(t=!0),s});if(t)return()=>{for(let r=0;r<s.length;r++){let t=s[r];"function"==typeof t?t():a(e[r],null)}}}}function l(...e){return s.useCallback(n(...e),e)}},6785:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},7624:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7634:(e,r,t)=>{Promise.resolve().then(t.bind(t,5947))},8145:(e,r,t)=>{"use strict";t.d(r,{E:()=>i});var s=t(5155);t(2115);var a=t(2085),n=t(3999);let l=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:r,variant:t,...a}=e;return(0,s.jsx)("div",{className:(0,n.cn)(l({variant:t}),r),...a})}},8482:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>d,ZB:()=>c,Zp:()=>l,aR:()=>i});var s=t(5155),a=t(2115),n=t(3999);let l=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...a})});l.displayName="Card";let i=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",t),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",t),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",t),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",t),...a})});d.displayName="CardContent",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",t),...a})}).displayName="CardFooter"},9026:(e,r,t)=>{"use strict";t.d(r,{Fc:()=>c,TN:()=>o});var s=t(5155),a=t(2115),n=t(2085),l=t(3999);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=a.forwardRef((e,r)=>{let{className:t,variant:a,...n}=e;return(0,s.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(i({variant:a}),t),...n})});c.displayName="Alert",a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",t),...a})}).displayName="AlertTitle";let o=a.forwardRef((e,r)=>{let{className:t,...a}=e;return(0,s.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",t),...a})});o.displayName="AlertDescription"},9037:(e,r,t)=>{"use strict";t.d(r,{A:()=>s});let s=(0,t(2895).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9708:(e,r,t)=>{"use strict";t.d(r,{DX:()=>i,Dc:()=>o,TL:()=>l});var s=t(2115),a=t(6101),n=t(5155);function l(e){let r=function(e){let r=s.forwardRef((e,r)=>{let{children:t,...n}=e;if(s.isValidElement(t)){var l;let e,i,c=(l=t,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),o=function(e,r){let t={...r};for(let s in r){let a=e[s],n=r[s];/^on[A-Z]/.test(s)?a&&n?t[s]=(...e)=>{let r=n(...e);return a(...e),r}:a&&(t[s]=a):"style"===s?t[s]={...a,...n}:"className"===s&&(t[s]=[a,n].filter(Boolean).join(" "))}return{...e,...t}}(n,t.props);return t.type!==s.Fragment&&(o.ref=r?(0,a.t)(r,c):c),s.cloneElement(t,o)}return s.Children.count(t)>1?s.Children.only(null):null});return r.displayName=`${e}.SlotClone`,r}(e),t=s.forwardRef((e,t)=>{let{children:a,...l}=e,i=s.Children.toArray(a),c=i.find(d);if(c){let e=c.props.children,a=i.map(r=>r!==c?r:s.Children.count(e)>1?s.Children.only(null):s.isValidElement(e)?e.props.children:null);return(0,n.jsx)(r,{...l,ref:t,children:s.isValidElement(e)?s.cloneElement(e,void 0,a):null})}return(0,n.jsx)(r,{...l,ref:t,children:a})});return t.displayName=`${e}.Slot`,t}var i=l("Slot"),c=Symbol("radix.slottable");function o(e){let r=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return r.displayName=`${e}.Slottable`,r.__radixId=c,r}function d(e){return s.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var r=r=>e(e.s=r);e.O(0,[5003,8441,1684,7358],()=>r(7634)),_N_E=e.O()}]);