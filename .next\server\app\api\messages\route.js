"use strict";(()=>{var e={};e.id=6434,e.ids=[6434],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},76576:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>j,serverHooks:()=>I,workAsyncStorage:()=>R,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>A});var n=t(96559),o=t(48088),i=t(37719),a=t(32190),u=t(19854),p=t(41098),d=t(79464),c=t(99326),l=t(96330),x=t(45697);let g=x.Ik({subject:x.Yj().min(1,"Subject is required"),content:x.Yj().min(1,"Content is required"),recipientType:x.k5(["INDIVIDUAL","GROUP","ALL_STUDENTS","ALL_TEACHERS","ALL_ACADEMIC_MANAGERS"]),recipientIds:x.YO(x.Yj()).optional(),priority:x.k5(["LOW","MEDIUM","HIGH","URGENT"]).default("MEDIUM"),scheduledAt:x.Yj().optional()});async function m(e){try{let r=await (0,u.getServerSession)(p.N);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"20"),o=t.get("status"),i=t.get("priority"),c=(s-1)*n,l={};o&&"ALL"!==o&&(l.status=o),i&&"ALL"!==i&&(l.priority=i);let[x,g]=await Promise.all([d.z.message.findMany({where:l,include:{sender:{select:{name:!0,email:!0}}},orderBy:{createdAt:"desc"},skip:c,take:n}),d.z.message.count({where:l})]);return a.NextResponse.json({messages:x,pagination:{page:s,limit:n,total:g,pages:Math.ceil(g/n)}})}catch(e){return console.error("Error fetching messages:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}async function A(e){try{let r=await (0,u.getServerSession)(p.N);if(!r?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER","TEACHER"].includes(r.user.role))return a.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),s=g.parse(t),n=await d.z.message.create({data:{subject:s.subject,content:s.content,recipientType:s.recipientType,recipientIds:s.recipientIds||[],priority:s.priority,status:"SENT",sentAt:new Date,senderId:r.user.id},include:{sender:{select:{name:!0,email:!0}}}});return await c._.log({userId:r.user.id,userRole:r.user.role||l.Role.ADMIN,action:"CREATE",resource:"MESSAGE",resourceId:n.id,details:`Created message: ${s.subject}`}),a.NextResponse.json(n,{status:201})}catch(e){if(e instanceof x.G)return a.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating message:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let j=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/messages/route",pathname:"/api/messages",filename:"route",bundlePath:"app/api/messages/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\messages\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:R,workUnitAsyncStorage:y,serverHooks:I}=j;function q(){return(0,i.patchFetch)({workAsyncStorage:R,workUnitAsyncStorage:y})}},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(76576));module.exports=s})();