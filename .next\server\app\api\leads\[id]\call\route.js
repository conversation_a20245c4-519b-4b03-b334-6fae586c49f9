"use strict";(()=>{var e={};e.id=3474,e.ids=[3474],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")},97937:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>R,routeModule:()=>j,serverHooks:()=>q,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>g});var a={};t.r(a),t.d(a,{POST:()=>f,PUT:()=>h});var s=t(96559),o=t(48088),n=t(37719),i=t(32190),d=t(19854),u=t(41098),l=t(79464),p=t(99326),c=t(45697);let x=c.Ik({notes:c.Yj().optional()}),w=c.Ik({duration:c.ai().min(0),notes:c.Yj().optional(),recordingUrl:c.Yj().optional()});async function f(e,{params:r}){try{let{id:t}=await r,a=await (0,d.getServerSession)(u.N);if(!a?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),o=x.parse(s),n=await l.z.lead.findUnique({where:{id:t}});if(!n)return i.NextResponse.json({error:"Lead not found"},{status:404});if(await l.z.callRecord.findFirst({where:{leadId:t,endedAt:null}}))return i.NextResponse.json({error:"Call already in progress"},{status:400});let c=new Date,w=await l.z.callRecord.create({data:{leadId:t,userId:a.user.id,startedAt:c,notes:o.notes}}),f=await l.z.lead.update({where:{id:t},data:{status:"CALLING",callStartedAt:c,updatedAt:c}});return await p._.logLeadContacted(a.user.id,a.user.role,n.id,{leadName:n.name,leadPhone:n.phone,previousStatus:n.status,newStatus:"CALLING",notes:"Call started"},e),i.NextResponse.json({callRecord:w,lead:f})}catch(e){if(e instanceof c.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error starting call:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{id:t}=await r,a=await (0,d.getServerSession)(u.N);if(!a?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let s=await e.json(),o=w.parse(s),n=await l.z.callRecord.findFirst({where:{leadId:t,userId:a.user.id,endedAt:null}});if(!n)return i.NextResponse.json({error:"No active call found"},{status:404});let c=new Date,x=await l.z.callRecord.update({where:{id:n.id},data:{endedAt:c,duration:o.duration,notes:o.notes||n.notes,recordingUrl:o.recordingUrl,updatedAt:c}}),f=await l.z.lead.update({where:{id:t},data:{status:"CALL_COMPLETED",callEndedAt:c,callDuration:o.duration,updatedAt:c}}),h=await l.z.lead.findUnique({where:{id:t}});return h&&await p._.logLeadContacted(a.user.id,a.user.role,h.id,{leadName:h.name,leadPhone:h.phone,previousStatus:"CALLING",newStatus:"CALL_COMPLETED",notes:`Call completed (${Math.floor(o.duration/60)}m ${o.duration%60}s)`},e),i.NextResponse.json({callRecord:x,lead:f})}catch(e){if(e instanceof c.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error ending call:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let j=new s.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/leads/[id]/call/route",pathname:"/api/leads/[id]/call",filename:"route",bundlePath:"app/api/leads/[id]/call/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\leads\\[id]\\call\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:v,workUnitAsyncStorage:g,serverHooks:q}=j;function R(){return(0,n.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:g})}}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[4243,580,5697,3412,1971],()=>t(97937));module.exports=a})();