(()=>{var e={};e.id=5237,e.ids=[5237],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64583:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>f,routeModule:()=>u,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var n={};s.r(n),s.d(n,{GET:()=>c,POST:()=>d});var r=s(96559),o=s(48088),a=s(37719),i=s(32190);async function c(e){try{let{searchParams:t}=new URL(e.url),s=t.get("action");if("mock"===s){let e=[{id:"1",title:"New Student Enrollment",message:"John Doe has enrolled in IELTS course",type:"success",priority:"medium",read:!1,createdAt:new Date(Date.now()-18e5).toISOString(),actionUrl:"/dashboard/students"},{id:"2",title:"Payment Received",message:"Payment of $500 received from Sarah Smith",type:"success",priority:"medium",read:!1,createdAt:new Date(Date.now()-72e5).toISOString(),actionUrl:"/dashboard/payments"},{id:"3",title:"Class Reminder",message:"B2 class starts in 30 minutes",type:"info",priority:"high",read:!0,createdAt:new Date(Date.now()-144e5).toISOString(),actionUrl:"/dashboard/classes"},{id:"4",title:"Low Attendance Alert",message:"Student Mike Johnson has missed 3 consecutive classes",type:"warning",priority:"high",read:!1,createdAt:new Date(Date.now()-216e5).toISOString(),actionUrl:"/dashboard/attendance"},{id:"5",title:"System Maintenance",message:"Scheduled maintenance tonight at 2 AM",type:"info",priority:"low",read:!0,createdAt:new Date(Date.now()-864e5).toISOString()}];return i.NextResponse.json({notifications:e,unreadCount:e.filter(e=>!e.read).length,total:e.length})}if("status"===s)return i.NextResponse.json({status:"operational",services:{sms:{available:!0,provider:"mock"},email:{available:!0,provider:"mock"},push:{available:!1,provider:"none"}},stats:{totalSent:1247,sentToday:23,failureRate:.02}});return i.NextResponse.json({message:"Notification test endpoint",availableActions:["mock","status"],usage:{mock:"GET /api/notifications/test?action=mock - Returns mock notifications",status:"GET /api/notifications/test?action=status - Returns system status"}})}catch(e){return console.error("Error in notification test endpoint:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let t=await e.json();return console.log("Mock notification sent:",t),i.NextResponse.json({success:!0,message:"Mock notification sent successfully",data:{id:`mock-${Date.now()}`,sentAt:new Date().toISOString(),channels:t.channels||["mock"],recipient:t.recipientId||"test-user"}})}catch(e){return console.error("Error in mock notification sending:",e),i.NextResponse.json({error:"Failed to send mock notification"},{status:500})}}let u=new r.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/notifications/test/route",pathname:"/api/notifications/test",filename:"route",bundlePath:"app/api/notifications/test/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\notifications\\test\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:m}=u;function f(){return(0,a.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},78335:()=>{},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),n=t.X(0,[4243,580],()=>s(64583));module.exports=n})();