(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[861],{1007:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2895:(e,s,r)=>{"use strict";r.d(s,{A:()=>l});var t=r(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,s)=>{let r=(0,t.forwardRef)((r,l)=>{let{color:i="currentColor",size:c=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:x="",children:m,...u}=r;return(0,t.createElement)("svg",{ref:l,...a,width:c,height:c,stroke:i,strokeWidth:o?24*Number(d)/Number(c):d,className:["lucide","lucide-".concat(n(e)),x].join(" "),...u},[...s.map(e=>{let[s,r]=e;return(0,t.createElement)(s,r)}),...Array.isArray(m)?m:[m]])});return r.displayName="".concat(e),r}},3999:(e,s,r)=>{"use strict";r.d(s,{Yq:()=>i,cn:()=>n,r6:()=>c,vv:()=>l});var t=r(2596),a=r(9688);function n(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}function l(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s)}function c(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}},4186:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4516:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4621:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5040:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}}),r.o(t,"useSearchParams")&&r.d(s,{useSearchParams:function(){return t.useSearchParams}})},6090:(e,s,r)=>{Promise.resolve().then(r.bind(r,9302))},6101:(e,s,r)=>{"use strict";r.d(s,{s:()=>l,t:()=>n});var t=r(2115);function a(e,s){if("function"==typeof e)return e(s);null!=e&&(e.current=s)}function n(...e){return s=>{let r=!1,t=e.map(e=>{let t=a(e,s);return r||"function"!=typeof t||(r=!0),t});if(r)return()=>{for(let s=0;s<t.length;s++){let r=t[s];"function"==typeof r?r():a(e[s],null)}}}}function l(...e){return t.useCallback(n(...e),e)}},7168:(e,s,r)=>{"use strict";r.d(s,{$:()=>d,r:()=>c});var t=r(5155),a=r(2115),n=r(9708),l=r(2085),i=r(3999);let c=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,s)=>{let{className:r,variant:a,size:l,asChild:d=!1,...o}=e,x=d?n.DX:"button";return(0,t.jsx)(x,{className:(0,i.cn)(c({variant:a,size:l,className:r})),ref:s,...o})});d.displayName="Button"},7550:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7580:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7949:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},8145:(e,s,r)=>{"use strict";r.d(s,{E:()=>i});var t=r(5155);r(2115);var a=r(2085),n=r(3999);let l=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:s,variant:r,...a}=e;return(0,t.jsx)("div",{className:(0,n.cn)(l({variant:r}),s),...a})}},8482:(e,s,r)=>{"use strict";r.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>i});var t=r(5155),a=r(2115),n=r(3999);let l=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("h3",{ref:s,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("p",{ref:s,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("p-6 pt-0",r),...a})});o.displayName="CardContent",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{ref:s,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},8524:(e,s,r)=>{"use strict";r.d(s,{A0:()=>i,BF:()=>c,Hj:()=>d,XI:()=>l,nA:()=>x,nd:()=>o});var t=r(5155),a=r(2115),n=r(3999);let l=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:s,className:(0,n.cn)("w-full caption-bottom text-sm",r),...a})})});l.displayName="Table";let i=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("thead",{ref:s,className:(0,n.cn)("[&_tr]:border-b",r),...a})});i.displayName="TableHeader";let c=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("tbody",{ref:s,className:(0,n.cn)("[&_tr:last-child]:border-0",r),...a})});c.displayName="TableBody",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("tfoot",{ref:s,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",r),...a})}).displayName="TableFooter";let d=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("tr",{ref:s,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",r),...a})});d.displayName="TableRow";let o=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("th",{ref:s,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",r),...a})});o.displayName="TableHead";let x=a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("td",{ref:s,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",r),...a})});x.displayName="TableCell",a.forwardRef((e,s)=>{let{className:r,...a}=e;return(0,t.jsx)("caption",{ref:s,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",r),...a})}).displayName="TableCaption"},8883:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9037:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9074:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9302:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>k});var t=r(5155),a=r(2115),n=r(5695),l=r(8482),i=r(7168),c=r(8145),d=r(8524),o=r(3999),x=r(7550),m=r(4621),u=r(1007),h=r(9420),f=r(8883),p=r(4516),j=r(9074),y=r(9037),g=r(5040),v=r(7949),N=r(7580),b=r(4186),w=r(6874),A=r.n(w);function k(){var e,s;let r=(0,n.useParams)(),[w,k]=(0,a.useState)(null),[R,C]=(0,a.useState)(!0);(0,a.useEffect)(()=>{r.id&&T(r.id)},[r.id]);let T=async e=>{try{let s=await fetch("/api/teachers/".concat(e)),r=await s.json();k(r)}catch(e){console.error("Error fetching teacher:",e)}finally{C(!1)}};if(R)return(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:"Loading..."});if(!w)return(0,t.jsx)("div",{className:"flex justify-center items-center h-64",children:"Teacher not found"});let Z=w.groups.filter(e=>e.isActive).length,E=w.groups.reduce((e,s)=>e+s._count.enrollments,0),P=w._count.classes,S=Z>0?E/Z:0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(A(),{href:"/dashboard/teachers",children:(0,t.jsxs)(i.$,{variant:"outline",size:"sm",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back to Teachers"]})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:w.user.name}),(0,t.jsx)("p",{className:"text-gray-600",children:"Teacher Profile"})]})]}),(0,t.jsxs)(i.$,{children:[(0,t.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Edit Profile"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(u.A,{className:"h-5 w-5 mr-2"}),"Personal Information"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(h.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{children:w.user.phone})]}),w.user.email&&(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{children:w.user.email})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsx)("span",{children:w.branch})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)(j.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{children:["Joined ",(0,o.Yq)(w.user.createdAt)]})]}),(0,t.jsx)("div",{className:"pt-2 border-t",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,t.jsx)(y.A,{className:"h-4 w-4 text-gray-400"}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:["Role: ",w.user.role]})]})})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(g.A,{className:"h-5 w-5 mr-2"}),"Professional Details"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Subject"}),(0,t.jsx)("span",{className:"font-semibold",children:w.subject})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Experience"}),(0,t.jsxs)(c.E,{className:(e=w.experience)?e<2?"bg-yellow-100 text-yellow-800":e<5?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",children:[w.experience?"".concat(w.experience,"y"):"0y"," - ",(s=w.experience)?s<2?"Junior":s<5?"Mid-level":"Senior":"New"]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Branch"}),(0,t.jsx)("span",{className:"font-semibold",children:w.branch})]})]})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsx)(l.aR,{children:(0,t.jsxs)(l.ZB,{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-5 w-5 mr-2"}),"Teaching Overview"]})}),(0,t.jsxs)(l.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Active Groups"}),(0,t.jsx)("span",{className:"font-semibold",children:Z})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Total Students"}),(0,t.jsx)("span",{className:"font-semibold",children:E})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Total Classes"}),(0,t.jsx)("span",{className:"font-semibold",children:P})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-sm text-gray-600",children:"Avg. Class Size"}),(0,t.jsx)("span",{className:"font-semibold",children:S.toFixed(1)})]})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(v.A,{className:"h-8 w-8 text-blue-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Groups"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:Z})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-8 w-8 text-green-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Total Students"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"h-8 w-8 text-purple-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Classes Taught"}),(0,t.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:P})]})]})})}),(0,t.jsx)(l.Zp,{children:(0,t.jsx)(l.Wu,{className:"p-6",children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"h-8 w-8 text-yellow-600"}),(0,t.jsxs)("div",{className:"ml-4",children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Experience"}),(0,t.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[w.experience||0,"y"]})]})]})})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsxs)(l.ZB,{children:["Assigned Groups (",w.groups.length,")"]}),(0,t.jsx)(l.BT,{children:"Groups currently assigned to this teacher"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nd,{children:"Group"}),(0,t.jsx)(d.nd,{children:"Course"}),(0,t.jsx)(d.nd,{children:"Level"}),(0,t.jsx)(d.nd,{children:"Students"}),(0,t.jsx)(d.nd,{children:"Duration"}),(0,t.jsx)(d.nd,{children:"Status"}),(0,t.jsx)(d.nd,{children:"Actions"})]})}),(0,t.jsx)(d.BF,{children:w.groups.map(e=>(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nA,{children:(0,t.jsx)("div",{className:"font-medium",children:e.name})}),(0,t.jsx)(d.nA,{children:e.course.name}),(0,t.jsx)(d.nA,{children:(0,t.jsx)(c.E,{variant:"outline",children:e.course.level})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-1 text-blue-600"}),(0,t.jsxs)("span",{children:[e._count.enrollments,"/",e.capacity]})]})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"text-sm",children:[(0,o.Yq)(e.startDate)," - ",(0,o.Yq)(e.endDate)]})}),(0,t.jsx)(d.nA,{children:(0,t.jsx)(c.E,{className:e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:e.isActive?"Active":"Inactive"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"View"}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"Manage"})]})})]},e.id))})]})})]}),(0,t.jsxs)(l.Zp,{children:[(0,t.jsxs)(l.aR,{children:[(0,t.jsx)(l.ZB,{children:"Recent Classes"}),(0,t.jsx)(l.BT,{children:"Latest class sessions taught by this teacher"})]}),(0,t.jsx)(l.Wu,{children:(0,t.jsxs)(d.XI,{children:[(0,t.jsx)(d.A0,{children:(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nd,{children:"Date"}),(0,t.jsx)(d.nd,{children:"Group"}),(0,t.jsx)(d.nd,{children:"Topic"}),(0,t.jsx)(d.nd,{children:"Attendance"}),(0,t.jsx)(d.nd,{children:"Homework"}),(0,t.jsx)(d.nd,{children:"Actions"})]})}),(0,t.jsx)(d.BF,{children:w.classes.slice(0,10).map(e=>(0,t.jsxs)(d.Hj,{children:[(0,t.jsx)(d.nA,{children:(0,o.Yq)(e.date)}),(0,t.jsx)(d.nA,{children:e.group.name}),(0,t.jsx)(d.nA,{children:e.topic||"No topic set"}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("span",{className:"text-sm",children:[e._count.attendances," students"]})}),(0,t.jsx)(d.nA,{children:(0,t.jsx)("span",{className:"text-sm text-gray-600",children:e.homework?"Assigned":"None"})}),(0,t.jsx)(d.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"View"}),(0,t.jsx)(i.$,{variant:"outline",size:"sm",children:"Edit"})]})})]},e.id))})]})})]})]})}},9420:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(2895).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9708:(e,s,r)=>{"use strict";r.d(s,{DX:()=>i,Dc:()=>d,TL:()=>l});var t=r(2115),a=r(6101),n=r(5155);function l(e){let s=function(e){let s=t.forwardRef((e,s)=>{let{children:r,...n}=e;if(t.isValidElement(r)){var l;let e,i,c=(l=r,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,s){let r={...s};for(let t in s){let a=e[t],n=s[t];/^on[A-Z]/.test(t)?a&&n?r[t]=(...e)=>{let s=n(...e);return a(...e),s}:a&&(r[t]=a):"style"===t?r[t]={...a,...n}:"className"===t&&(r[t]=[a,n].filter(Boolean).join(" "))}return{...e,...r}}(n,r.props);return r.type!==t.Fragment&&(d.ref=s?(0,a.t)(s,c):c),t.cloneElement(r,d)}return t.Children.count(r)>1?t.Children.only(null):null});return s.displayName=`${e}.SlotClone`,s}(e),r=t.forwardRef((e,r)=>{let{children:a,...l}=e,i=t.Children.toArray(a),c=i.find(o);if(c){let e=c.props.children,a=i.map(s=>s!==c?s:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,n.jsx)(s,{...l,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,a):null})}return(0,n.jsx)(s,{...l,ref:r,children:a})});return r.displayName=`${e}.Slot`,r}var i=l("Slot"),c=Symbol("radix.slottable");function d(e){let s=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return s.displayName=`${e}.Slottable`,s.__radixId=c,s}function o(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6874,8441,1684,7358],()=>s(6090)),_N_E=e.O()}]);