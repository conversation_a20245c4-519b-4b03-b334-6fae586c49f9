(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1145],{1007:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2525:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2714:(e,a,t)=>{"use strict";t.d(a,{J:()=>d});var s=t(5155),r=t(2115),l=t(968),n=t(2085),i=t(3999);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.b,{ref:a,className:(0,i.cn)(c(),t),...r})});d.displayName=l.b.displayName},3730:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>V});var s=t(5155),r=t(2115),l=t(8482),n=t(7168),i=t(8145),c=t(9852),d=t(8524),o=t(9840),m=t(9026),u=t(7624),h=t(4616),x=t(1007),f=t(7949),p=t(7580),j=t(7924),g=t(9420),b=t(8883),v=t(5040),y=t(4621),N=t(2525),w=t(2177),A=t(221),E=t(1153),k=t(2714),C=t(5784),T=t(9037),L=t(7705);let S=E.Ik({name:E.Yj().min(2,"Name must be at least 2 characters").optional(),phone:E.Yj().min(9,"Phone number must be at least 9 characters").optional(),email:E.Yj().email("Invalid email address").optional().or(E.eu("")),userId:E.Yj().optional(),subject:E.Yj().min(1,"Subject is required"),experience:E.ai().min(0,"Experience cannot be negative").optional(),branch:E.Yj().min(1,"Branch is required"),tier:E.k5(["A_LEVEL","B_LEVEL","C_LEVEL","NEW"]).default("NEW")}),R=["English Language","IELTS Preparation","SAT Preparation","Mathematics","Kids English","Business English","Academic English","Conversation English","Grammar & Writing","Speaking & Listening"],B=["Main Branch","Branch"],F=[{value:"A_LEVEL",label:"A-level Teacher",description:"Highest priority, experienced teacher"},{value:"B_LEVEL",label:"B-level Teacher",description:"Experienced teacher"},{value:"C_LEVEL",label:"C-level Teacher",description:"Developing teacher"},{value:"NEW",label:"New Teacher",description:"New or trainee teacher"}];function M(e){let{initialData:a,onSubmit:t,onCancel:i,isEditing:d=!1,existingUsers:o=[]}=e,{currentBranch:h}=(0,L.O)(),[f,p]=(0,r.useState)(!1),[j,y]=(0,r.useState)(null),[N,E]=(0,r.useState)(!!(null==a?void 0:a.userId)),{register:M,handleSubmit:V,setValue:I,watch:z,formState:{errors:_}}=(0,w.mN)({resolver:(0,A.u)(S),defaultValues:{name:(null==a?void 0:a.name)||"",phone:(null==a?void 0:a.phone)||"",email:(null==a?void 0:a.email)||"",userId:(null==a?void 0:a.userId)||"",subject:(null==a?void 0:a.subject)||"",experience:(null==a?void 0:a.experience)||0,branch:(null==a?void 0:a.branch)||h.name,tier:(null==a?void 0:a.tier)||"NEW"}});(0,r.useEffect)(()=>{!(null==a?void 0:a.branch)&&(null==h?void 0:h.name)&&I("branch",h.name)},[h,null==a?void 0:a.branch,I]);let P=z("userId"),U=z("subject"),Z=z("branch"),J=z("tier"),q=async e=>{p(!0),y(null);try{await t(e)}catch(e){y(e instanceof Error?e.message:"An error occurred")}finally{p(!1)}};return(0,s.jsxs)(l.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{className:"flex items-center",children:[(0,s.jsx)(x.A,{className:"h-5 w-5 mr-2"}),d?"Edit Teacher":"Add New Teacher"]}),(0,s.jsx)(l.BT,{children:d?"Update teacher information":"Enter teacher details to create a new profile"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("form",{onSubmit:V(q),className:"space-y-6",children:[j&&(0,s.jsx)(m.Fc,{variant:"destructive",children:(0,s.jsx)(m.TN,{children:j})}),!d&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(x.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-medium",children:"User Account"})]}),(0,s.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,s.jsx)(n.$,{type:"button",variant:N?"outline":"default",onClick:()=>E(!1),children:"Create New User"}),(0,s.jsx)(n.$,{type:"button",variant:N?"default":"outline",onClick:()=>E(!0),children:"Use Existing User"})]}),N?(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"userId",children:"Select User *"}),(0,s.jsxs)(C.l6,{value:P,onValueChange:e=>I("userId",e),children:[(0,s.jsx)(C.bq,{children:(0,s.jsx)(C.yv,{placeholder:"Select an existing user"})}),(0,s.jsx)(C.gC,{children:o.map(e=>(0,s.jsxs)(C.eb,{value:e.id,children:[e.name," - ",e.phone]},e.id))})]})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"name",children:"Full Name *"}),(0,s.jsx)(c.p,{id:"name",...M("name"),placeholder:"Enter full name",className:_.name?"border-red-500":""}),_.name&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.name.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"phone",children:"Phone Number *"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.p,{id:"phone",...M("phone"),placeholder:"+998 90 123 45 67",className:"pl-10 ".concat(_.phone?"border-red-500":"")})]}),_.phone&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.phone.message})]}),(0,s.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,s.jsx)(k.J,{htmlFor:"email",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.p,{id:"email",type:"email",...M("email"),placeholder:"<EMAIL>",className:"pl-10 ".concat(_.email?"border-red-500":"")})]}),_.email&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.email.message})]})]})]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 text-gray-500"}),(0,s.jsx)("h3",{className:"text-lg font-medium",children:"Professional Information"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"subject",children:"Subject/Specialization *"}),(0,s.jsxs)(C.l6,{value:U,onValueChange:e=>I("subject",e),children:[(0,s.jsx)(C.bq,{className:_.subject?"border-red-500":"",children:(0,s.jsx)(C.yv,{placeholder:"Select subject"})}),(0,s.jsx)(C.gC,{children:R.map(e=>(0,s.jsx)(C.eb,{value:e,children:e},e))})]}),_.subject&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.subject.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"branch",children:"Branch *"}),(0,s.jsxs)(C.l6,{value:Z,onValueChange:e=>I("branch",e),children:[(0,s.jsx)(C.bq,{className:_.branch?"border-red-500":"",children:(0,s.jsx)(C.yv,{placeholder:"Select branch"})}),(0,s.jsx)(C.gC,{children:B.map(e=>(0,s.jsx)(C.eb,{value:e,children:e},e))})]}),_.branch&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.branch.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"experience",children:"Years of Experience"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(T.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.p,{id:"experience",type:"number",min:"0",step:"0.5",...M("experience",{valueAsNumber:!0}),placeholder:"0",className:"pl-10 ".concat(_.experience?"border-red-500":"")})]}),_.experience&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.experience.message})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(k.J,{htmlFor:"tier",children:"Teacher Tier *"}),(0,s.jsxs)(C.l6,{value:J,onValueChange:e=>I("tier",e),children:[(0,s.jsx)(C.bq,{className:_.tier?"border-red-500":"",children:(0,s.jsx)(C.yv,{placeholder:"Select tier"})}),(0,s.jsx)(C.gC,{children:F.map(e=>(0,s.jsx)(C.eb,{value:e.value,children:(0,s.jsxs)("div",{className:"flex flex-col",children:[(0,s.jsx)("span",{className:"font-medium",children:e.label}),(0,s.jsx)("span",{className:"text-xs text-gray-500",children:e.description})]})},e.value))})]}),_.tier&&(0,s.jsx)("p",{className:"text-sm text-red-500",children:_.tier.message})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[i&&(0,s.jsx)(n.$,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),(0,s.jsxs)(n.$,{type:"submit",disabled:f,children:[f&&(0,s.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),d?"Update Teacher":"Create Teacher"]})]})]})})]})}function V(){let{currentBranch:e}=(0,L.O)(),[a,t]=(0,r.useState)([]),[w,A]=(0,r.useState)({totalTeachers:0,totalGroups:0,totalClasses:0,totalStudents:0}),[E,k]=(0,r.useState)(!0),[C,T]=(0,r.useState)(""),[S,R]=(0,r.useState)(!1),[B,F]=(0,r.useState)(!1),[V,I]=(0,r.useState)(null),[z,_]=(0,r.useState)(!1),[P,U]=(0,r.useState)(null);(0,r.useEffect)(()=>{(null==e?void 0:e.id)&&(Z(),J())},[null==e?void 0:e.id]);let Z=async()=>{if(null==e?void 0:e.id)try{k(!0);let a=await fetch("/api/teachers?branch=".concat(e.id)),s=await a.json();t(s.teachers||[]),U(null)}catch(e){console.error("Error fetching teachers:",e),U("Failed to fetch teachers")}finally{k(!1)}},J=async()=>{if(null==e?void 0:e.id)try{let a=await fetch("/api/teachers/kpis?branch=".concat(e.id)),t=await a.json();A(t)}catch(e){console.error("Error fetching teacher KPIs:",e)}},q=async a=>{_(!0),U(null);try{let t=a.userId;if(!t){let e=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:a.name,phone:a.phone,email:a.email||null,role:"TEACHER",password:"defaultPassword123"})});if(!e.ok){let a=await e.json();throw Error(a.error||"Failed to create user")}t=(await e.json()).id}let s=await fetch("/api/teachers",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t,subject:a.subject,experience:a.experience,branch:e.id,tier:a.tier})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create teacher")}R(!1),Z()}catch(e){U(e instanceof Error?e.message:"An error occurred")}finally{_(!1)}},W=async e=>{if(V){_(!0),U(null);try{let a=await fetch("/api/teachers/".concat(V.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({subject:e.subject,experience:e.experience,branch:e.branch,tier:e.tier})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to update teacher")}F(!1),I(null),Z()}catch(e){U(e instanceof Error?e.message:"An error occurred")}finally{_(!1)}}},H=async e=>{if(confirm("Are you sure you want to delete this teacher? This action cannot be undone."))try{let a=await fetch("/api/teachers/".concat(e),{method:"DELETE"});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to delete teacher")}Z()}catch(e){U(e instanceof Error?e.message:"An error occurred")}},O=a.filter(e=>{var a;return e.user.name.toLowerCase().includes(C.toLowerCase())||e.user.phone.includes(C)||(null==(a=e.user.email)?void 0:a.toLowerCase().includes(C.toLowerCase()))||e.subject.toLowerCase().includes(C.toLowerCase())||e.branch.toLowerCase().includes(C.toLowerCase())}),D=e=>e?e<2?"bg-yellow-100 text-yellow-800":e<5?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",Y=e=>e?e<2?"Junior":e<5?"Mid-level":"Senior":"New",G=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},$=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";default:return"New"}};return E?(0,s.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,s.jsx)(u.A,{className:"h-8 w-8 animate-spin"}),(0,s.jsx)("span",{className:"ml-2",children:"Loading teachers..."})]}):(0,s.jsxs)("div",{className:"space-y-6",children:[P&&(0,s.jsx)(m.Fc,{variant:"destructive",children:(0,s.jsx)(m.TN,{children:P})}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Teachers Management - ",null==e?void 0:e.name]}),(0,s.jsxs)("p",{className:"text-gray-600",children:["Manage teaching staff for ",null==e?void 0:e.name]})]}),(0,s.jsxs)(o.lG,{open:S,onOpenChange:R,children:[(0,s.jsx)(o.zM,{asChild:!0,children:(0,s.jsxs)(n.$,{children:[(0,s.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Teacher"]})}),(0,s.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,s.jsxs)(o.c7,{children:[(0,s.jsx)(o.L3,{children:"Add New Teacher"}),(0,s.jsx)(o.rr,{children:"Create a new teacher profile with their professional information."})]}),(0,s.jsx)(M,{onSubmit:q,onCancel:()=>R(!1),isEditing:!1})]})]})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Teachers"}),(0,s.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:w.totalTeachers}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active teaching staff"})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Groups"}),(0,s.jsx)(f.A,{className:"h-4 w-4 text-green-600"})]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:w.totalGroups}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active teaching groups"})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,s.jsx)(l.ZB,{className:"text-sm font-medium",children:"Total Classes"}),(0,s.jsx)(p.A,{className:"h-4 w-4 text-purple-600"})]}),(0,s.jsxs)(l.Wu,{children:[(0,s.jsx)("div",{className:"text-2xl font-bold",children:w.totalClasses}),(0,s.jsx)("p",{className:"text-xs text-muted-foreground",children:"Classes conducted"})]})]})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsx)(l.aR,{children:(0,s.jsx)(l.ZB,{children:"Search Teachers"})}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,s.jsx)(c.p,{placeholder:"Search by name, phone, email, subject, or branch...",value:C,onChange:e=>T(e.target.value),className:"pl-10"})]})})]}),(0,s.jsxs)(l.Zp,{children:[(0,s.jsxs)(l.aR,{children:[(0,s.jsxs)(l.ZB,{children:["Teachers (",O.length,")"]}),(0,s.jsx)(l.BT,{children:"Complete list of teaching staff"})]}),(0,s.jsx)(l.Wu,{children:(0,s.jsxs)(d.XI,{children:[(0,s.jsx)(d.A0,{children:(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nd,{children:"Teacher"}),(0,s.jsx)(d.nd,{children:"Subject"}),(0,s.jsx)(d.nd,{children:"Tier"}),(0,s.jsx)(d.nd,{children:"Experience"}),(0,s.jsx)(d.nd,{children:"Branch"}),(0,s.jsx)(d.nd,{children:"Groups"}),(0,s.jsx)(d.nd,{children:"Classes"}),(0,s.jsx)(d.nd,{children:"Actions"})]})}),(0,s.jsx)(d.BF,{children:O.map(e=>(0,s.jsxs)(d.Hj,{children:[(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,s.jsx)(x.A,{className:"h-5 w-5 text-gray-600"})})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.user.name}),(0,s.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,s.jsx)(g.A,{className:"h-3 w-3 mr-1"}),e.user.phone]}),e.user.email&&(0,s.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,s.jsx)(b.A,{className:"h-3 w-3 mr-1"}),e.user.email]})]})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(v.A,{className:"h-4 w-4 mr-2 text-blue-600"}),(0,s.jsx)("span",{className:"font-medium",children:e.subject})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsx)(i.E,{className:G(e.tier),children:$(e.tier)})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)(i.E,{className:D(e.experience),children:[e.experience?"".concat(e.experience,"y"):"0y"," - ",Y(e.experience)]})}),(0,s.jsx)(d.nA,{children:(0,s.jsx)("span",{className:"text-sm font-medium",children:e.branch})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(f.A,{className:"h-4 w-4 mr-1 text-green-600"}),(0,s.jsx)("span",{className:"font-medium",children:e._count.groups})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)(p.A,{className:"h-4 w-4 mr-1 text-purple-600"}),(0,s.jsx)("span",{className:"font-medium",children:e._count.classes})]})}),(0,s.jsx)(d.nA,{children:(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{I(e),F(!0)},children:(0,s.jsx)(y.A,{className:"h-4 w-4"})}),(0,s.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>H(e.id),className:"text-red-600 hover:text-red-700",children:(0,s.jsx)(N.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]}),(0,s.jsx)(o.lG,{open:B,onOpenChange:F,children:(0,s.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,s.jsxs)(o.c7,{children:[(0,s.jsx)(o.L3,{children:"Edit Teacher"}),(0,s.jsx)(o.rr,{children:"Update teacher information and professional details."})]}),V&&(0,s.jsx)(M,{initialData:{name:V.user.name,phone:V.user.phone,email:V.user.email||"",userId:V.userId,subject:V.subject,experience:V.experience||0,branch:V.branch,tier:V.tier||"NEW"},onSubmit:W,onCancel:()=>{F(!1),I(null)},isEditing:!0})]})})]})}},3999:(e,a,t)=>{"use strict";t.d(a,{Yq:()=>i,cn:()=>l,r6:()=>c,vv:()=>n});var s=t(2596),r=t(9688);function l(){for(var e=arguments.length,a=Array(e),t=0;t<e;t++)a[t]=arguments[t];return(0,r.QP)((0,s.$)(a))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let a="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(a)}function c(e){let a="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(a)}},4145:(e,a,t)=>{Promise.resolve().then(t.bind(t,3730))},4616:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5040:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]])},5784:(e,a,t)=>{"use strict";t.d(a,{bq:()=>u,eb:()=>p,gC:()=>f,l6:()=>o,yv:()=>m});var s=t(5155),r=t(2115),l=t(1992),n=t(6474),i=t(7863),c=t(5196),d=t(3999);let o=l.bL;l.YJ;let m=l.WT,u=r.forwardRef((e,a)=>{let{className:t,children:r,...i}=e;return(0,s.jsxs)(l.l9,{ref:a,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[r,(0,s.jsx)(l.In,{asChild:!0,children:(0,s.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let h=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.PP,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,s.jsx)(i.A,{className:"h-4 w-4"})})});h.displayName=l.PP.displayName;let x=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.wn,{ref:a,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,s.jsx)(n.A,{className:"h-4 w-4"})})});x.displayName=l.wn.displayName;let f=r.forwardRef((e,a)=>{let{className:t,children:r,position:n="popper",...i}=e;return(0,s.jsx)(l.ZL,{children:(0,s.jsxs)(l.UC,{ref:a,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,s.jsx)(h,{}),(0,s.jsx)(l.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,s.jsx)(x,{})]})})});f.displayName=l.UC.displayName,r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.JU,{ref:a,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let p=r.forwardRef((e,a)=>{let{className:t,children:r,...n}=e;return(0,s.jsxs)(l.q7,{ref:a,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,s.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,s.jsx)(l.VF,{children:(0,s.jsx)(c.A,{className:"h-4 w-4"})})}),(0,s.jsx)(l.p4,{children:r})]})});p.displayName=l.q7.displayName,r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.wv,{ref:a,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName},7168:(e,a,t)=>{"use strict";t.d(a,{$:()=>d,r:()=>c});var s=t(5155),r=t(2115),l=t(9708),n=t(2085),i=t(3999);let c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,a)=>{let{className:t,variant:r,size:n,asChild:d=!1,...o}=e,m=d?l.DX:"button";return(0,s.jsx)(m,{className:(0,i.cn)(c({variant:r,size:n,className:t})),ref:a,...o})});d.displayName="Button"},7580:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7624:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7705:(e,a,t)=>{"use strict";t.d(a,{BranchProvider:()=>i,O:()=>c,Z:()=>d});var s=t(5155),r=t(2115);let l=(0,r.createContext)(void 0),n=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function i(e){let{children:a}=e,[t,i]=(0,r.useState)(n[0]),[c]=(0,r.useState)(n),[d,o]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let a=c.find(a=>a.id===e);a&&i(a)}o(!1)},[c]),(0,s.jsx)(l.Provider,{value:{currentBranch:t,branches:c,switchBranch:e=>{let a=c.find(a=>a.id===e);a&&(i(a),localStorage.setItem("selectedBranch",e))},isLoading:d},children:a})}function c(){let e=(0,r.useContext)(l);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,r.useContext)(l)||null}},7924:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7949:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},8145:(e,a,t)=>{"use strict";t.d(a,{E:()=>i});var s=t(5155);t(2115);var r=t(2085),l=t(3999);let n=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:a,variant:t,...r}=e;return(0,s.jsx)("div",{className:(0,l.cn)(n({variant:t}),a),...r})}},8482:(e,a,t)=>{"use strict";t.d(a,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>i});var s=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...r})});n.displayName="Card";let i=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h3",{ref:a,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("p",{ref:a,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,l.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},8524:(e,a,t)=>{"use strict";t.d(a,{A0:()=>i,BF:()=>c,Hj:()=>d,XI:()=>n,nA:()=>m,nd:()=>o});var s=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:a,className:(0,l.cn)("w-full caption-bottom text-sm",t),...r})})});n.displayName="Table";let i=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("thead",{ref:a,className:(0,l.cn)("[&_tr]:border-b",t),...r})});i.displayName="TableHeader";let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("tbody",{ref:a,className:(0,l.cn)("[&_tr:last-child]:border-0",t),...r})});c.displayName="TableBody",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("tfoot",{ref:a,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...r})}).displayName="TableFooter";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("tr",{ref:a,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...r})});d.displayName="TableRow";let o=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("th",{ref:a,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...r})});o.displayName="TableHead";let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("td",{ref:a,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...r})});m.displayName="TableCell",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("caption",{ref:a,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",t),...r})}).displayName="TableCaption"},8883:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9026:(e,a,t)=>{"use strict";t.d(a,{Fc:()=>c,TN:()=>d});var s=t(5155),r=t(2115),l=t(2085),n=t(3999);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef((e,a)=>{let{className:t,variant:r,...l}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,n.cn)(i({variant:r}),t),...l})});c.displayName="Alert",r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h5",{ref:a,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let d=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});d.displayName="AlertDescription"},9037:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Award",[["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}],["path",{d:"M15.477 12.89 17 22l-5-3-5 3 1.523-9.11",key:"em7aur"}]])},9420:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(2895).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9840:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>u,L3:()=>x,c7:()=>h,lG:()=>c,rr:()=>f,zM:()=>d});var s=t(5155),r=t(2115),l=t(5452),n=t(4416),i=t(3999);let c=l.bL,d=l.l9,o=l.ZL;l.bm;let m=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});m.displayName=l.hJ.displayName;let u=r.forwardRef((e,a)=>{let{className:t,children:r,...c}=e;return(0,s.jsxs)(o,{children:[(0,s.jsx)(m,{}),(0,s.jsx)(l.UC,{ref:a,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",t),...c,children:(0,s.jsxs)("div",{className:"relative",children:[r,(0,s.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});u.displayName=l.UC.displayName;let h=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};h.displayName="DialogHeader";let x=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});x.displayName=l.hE.displayName;let f=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});f.displayName=l.VY.displayName},9852:(e,a,t)=>{"use strict";t.d(a,{p:()=>n});var s=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,a)=>{let{className:t,type:r,...n}=e;return(0,s.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...n})});n.displayName="Input"}},e=>{var a=a=>e(e.s=a);e.O(0,[5003,6221,4358,1071,2356,8441,1684,7358],()=>a(4145)),_N_E=e.O()}]);