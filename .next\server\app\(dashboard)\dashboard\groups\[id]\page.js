(()=>{var e={};e.id=704,e.ids=[704],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},21827:(e,s,t)=>{Promise.resolve().then(t.bind(t,34817))},28559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34817:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>C});var r=t(60687),a=t(43210),l=t(16189),n=t(55192),d=t(24934),i=t(59821),c=t(96752),o=t(96241),x=t(28559),m=t(23026),h=t(9923),p=t(27351),u=t(40228),j=t(82080),f=t(58869),N=t(48340),g=t(41550),b=t(41312),y=t(23689),v=t(83281),w=t(48730),A=t(85814),k=t.n(A);function C(){let e;(0,l.useParams)();let[s,t]=(0,a.useState)(null),[A,C]=(0,a.useState)(!0),P=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"COMPLETED":return"bg-blue-100 text-blue-800";case"DROPPED":return"bg-red-100 text-red-800";case"SUSPENDED":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}};if(A)return(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:"Loading..."});if(!s)return(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:"Group not found"});let D=s.enrollments.filter(e=>"ACTIVE"===e.status).length,R=s.enrollments.filter(e=>"COMPLETED"===e.status).length,E=s.enrollments.filter(e=>"DROPPED"===e.status).length,T=D/s.capacity*100;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)(k(),{href:"/dashboard/groups",children:(0,r.jsxs)(d.$,{variant:"outline",size:"sm",children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back to Groups"]})}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:s.name}),(0,r.jsx)("p",{className:"text-gray-600",children:"Group Details & Management"})]})]}),(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsxs)(d.$,{variant:"outline",children:[(0,r.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Add Student"]}),(0,r.jsxs)(d.$,{children:[(0,r.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Edit Group"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center",children:[(0,r.jsx)(p.A,{className:"h-5 w-5 mr-2"}),"Group Information"]})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Status"}),(0,r.jsx)(i.E,{className:s.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:s.isActive?"Active":"Inactive"})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Capacity"}),(0,r.jsxs)("span",{className:"font-semibold",children:[D,"/",s.capacity]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Branch"}),(0,r.jsx)("span",{className:"font-semibold",children:s.branch})]}),s.room&&(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Room"}),(0,r.jsx)("span",{className:"font-semibold",children:s.room})]}),(0,r.jsxs)("div",{className:"pt-2 border-t",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Duration"})]}),(0,r.jsxs)("p",{className:"text-sm",children:[(0,o.Yq)(s.startDate)," - ",(0,o.Yq)(s.endDate)]})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 mr-2"}),"Course Details"]})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"font-semibold text-lg",children:s.course.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-600",children:["Level: ",s.course.level]})]}),s.course.description&&(0,r.jsx)("p",{className:"text-sm text-gray-700",children:s.course.description}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Duration"}),(0,r.jsxs)("span",{className:"font-semibold",children:[s.course.duration," weeks"]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-sm text-gray-600",children:"Price"}),(0,r.jsx)("span",{className:"font-semibold",children:(e=s.course.price,new Intl.NumberFormat("en-US",{style:"currency",currency:"USD",minimumFractionDigits:0}).format(e/12500))})]})]})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsxs)(n.ZB,{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-5 w-5 mr-2"}),"Teacher"]})}),(0,r.jsxs)(n.Wu,{className:"space-y-4",children:[(0,r.jsx)("div",{children:(0,r.jsx)("h3",{className:"font-semibold text-lg",children:s.teacher.user.name})}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(N.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{className:"text-sm",children:s.teacher.user.phone})]}),s.teacher.user.email&&(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 text-gray-400"}),(0,r.jsx)("span",{className:"text-sm",children:s.teacher.user.email})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Active Students"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(y.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Completed"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:R})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(v.A,{className:"h-8 w-8 text-red-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Dropped"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:E})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(w.A,{className:"h-8 w-8 text-purple-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Capacity Usage"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[T.toFixed(0),"%"]})]})]})})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{children:["Enrolled Students (",s.enrollments.length,")"]}),(0,r.jsx)(n.BT,{children:"Students currently enrolled in this group"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)(c.XI,{children:[(0,r.jsx)(c.A0,{children:(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nd,{children:"Student"}),(0,r.jsx)(c.nd,{children:"Contact"}),(0,r.jsx)(c.nd,{children:"Enrollment Date"}),(0,r.jsx)(c.nd,{children:"Status"}),(0,r.jsx)(c.nd,{children:"Actions"})]})}),(0,r.jsx)(c.BF,{children:s.enrollments.map(e=>(0,r.jsxs)(c.Hj,{children:[(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"h-8 w-8 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(f.A,{className:"h-4 w-4 text-gray-600"})})}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.student.user.name})})]})}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{children:[(0,r.jsx)("div",{className:"text-sm",children:e.student.user.phone}),e.student.user.email&&(0,r.jsx)("div",{className:"text-sm text-gray-500",children:e.student.user.email})]})}),(0,r.jsx)(c.nA,{children:(0,o.Yq)(e.startDate)}),(0,r.jsx)(c.nA,{children:(0,r.jsx)(i.E,{className:P(e.status),children:e.status})}),(0,r.jsx)(c.nA,{children:(0,r.jsxs)("div",{className:"flex space-x-2",children:[(0,r.jsx)(d.$,{variant:"outline",size:"sm",children:"View"}),(0,r.jsx)(d.$,{variant:"outline",size:"sm",children:"Edit"})]})})]},e.id))})]})})]})]})}},41550:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},45803:(e,s,t)=>{Promise.resolve().then(t.bind(t,95659))},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>n,aR:()=>d});var r=t(60687),a=t(43210),l=t(96241);let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));n.displayName="Card";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,l.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,l.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},59425:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),l=t(88170),n=t.n(l),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(s,i);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["groups",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,95659)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\[id]\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\[id]\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/groups/[id]/page",pathname:"/dashboard/groups/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83281:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},95659:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\groups\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\groups\\[id]\\page.tsx","default")},96752:(e,s,t)=>{"use strict";t.d(s,{A0:()=>d,BF:()=>i,Hj:()=>c,XI:()=>n,nA:()=>x,nd:()=>o});var r=t(60687),a=t(43210),l=t(96241);let n=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,l.cn)("w-full caption-bottom text-sm",e),...s})}));n.displayName="Table";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("thead",{ref:t,className:(0,l.cn)("[&_tr]:border-b",e),...s}));d.displayName="TableHeader";let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tbody",{ref:t,className:(0,l.cn)("[&_tr:last-child]:border-0",e),...s}));i.displayName="TableBody",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tfoot",{ref:t,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tr",{ref:t,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("th",{ref:t,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("td",{ref:t,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("caption",{ref:t,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,3039],()=>t(59425));module.exports=r})();