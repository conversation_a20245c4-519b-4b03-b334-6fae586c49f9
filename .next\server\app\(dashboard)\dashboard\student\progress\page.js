(()=>{var e={};e.id=1153,e.ids=[1153],e.modules={1179:(e,s,r)=>{"use strict";r.r(s),r.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>m,tree:()=>o});var a=r(65239),t=r(48088),n=r(88170),l=r.n(n),d=r(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);r.d(s,i);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["progress",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,85942)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\progress\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\progress\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:t.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/progress/page",pathname:"/dashboard/student/progress",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},2206:(e,s,r)=>{Promise.resolve().then(r.bind(r,85942))},3018:(e,s,r)=>{"use strict";r.d(s,{Fc:()=>i,TN:()=>o});var a=r(60687),t=r(43210),n=r(24224),l=r(96241);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=t.forwardRef(({className:e,variant:s,...r},t)=>(0,a.jsx)("div",{ref:t,role:"alert",className:(0,l.cn)(d({variant:s}),e),...r}));i.displayName="Alert",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("h5",{ref:r,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...s}));o.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},55192:(e,s,r)=>{"use strict";r.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>i,Zp:()=>l,aR:()=>d});var a=r(60687),t=r(43210),n=r(96241);let l=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));l.displayName="Card";let d=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let i=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("h3",{ref:r,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let o=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("p",{ref:r,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",t.forwardRef(({className:e,...s},r)=>(0,a.jsx)("div",{ref:r,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68550:(e,s,r)=>{Promise.resolve().then(r.bind(r,81382))},72730:(e,s,r)=>{"use strict";r.d(s,{A:()=>a});let a=(0,r(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},81382:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>B});var a=r(60687),t=r(43210),n=r(55192),l=r(59821),d=r(11273),i=r(14163),o="Progress",[c,x]=(0,d.A)(o),[m,u]=c(o),p=t.forwardRef((e,s)=>{var r,t;let{__scopeProgress:n,value:l=null,max:d,getValueLabel:o=v,...c}=e;(d||0===d)&&!N(d)&&console.error((r=`${d}`,`Invalid prop \`max\` of value \`${r}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let x=N(d)?d:100;null===l||b(l,x)||console.error((t=`${l}`,`Invalid prop \`value\` of value \`${t}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let u=b(l,x)?l:null,p=j(u)?o(u,x):void 0;return(0,a.jsx)(m,{scope:n,value:u,max:x,children:(0,a.jsx)(i.sG.div,{"aria-valuemax":x,"aria-valuemin":0,"aria-valuenow":j(u)?u:void 0,"aria-valuetext":p,role:"progressbar","data-state":g(u,x),"data-value":u??void 0,"data-max":x,...c,ref:s})})});p.displayName=o;var h="ProgressIndicator",f=t.forwardRef((e,s)=>{let{__scopeProgress:r,...t}=e,n=u(h,r);return(0,a.jsx)(i.sG.div,{"data-state":g(n.value,n.max),"data-value":n.value??void 0,"data-max":n.max,...t,ref:s})});function v(e,s){return`${Math.round(e/s*100)}%`}function g(e,s){return null==e?"indeterminate":e===s?"complete":"loading"}function j(e){return"number"==typeof e}function N(e){return j(e)&&!isNaN(e)&&e>0}function b(e,s){return j(e)&&!isNaN(e)&&e<=s&&e>=0}f.displayName=h;var y=r(96241);let w=t.forwardRef(({className:e,value:s,...r},t)=>(0,a.jsx)(p,{ref:t,className:(0,y.cn)("relative h-2 w-full overflow-hidden rounded-full bg-primary/20",e),...r,children:(0,a.jsx)(f,{className:"h-full w-full flex-1 bg-primary transition-all",style:{transform:`translateX(-${100-(s||0)}%)`}})}));w.displayName=p.displayName;var P=r(3018),k=r(72730),R=r(28947),A=r(82080),C=r(23689),T=r(86561);let _=(0,r(18962).A)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);function B(){let[e,s]=(0,t.useState)(null),[r,d]=(0,t.useState)(!0),[i,o]=(0,t.useState)(null),c=e=>e>=80?"bg-green-500":e>=60?"bg-yellow-500":"bg-red-500",x=(e,s)=>{let r=e/s*100;return r>=80?"text-green-600":r>=60?"text-yellow-600":"text-red-600"};return r?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(k.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading progress data..."})]}):i?(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(P.Fc,{variant:"destructive",children:(0,a.jsx)(P.TN,{children:i})})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Progress"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Track your learning journey and achievements"})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(R.A,{className:"h-5 w-5 mr-2"}),"Level Progress"]}),(0,a.jsxs)(n.BT,{children:["Your progress from ",e.student?.level," to ",e.student?.nextLevel]})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.E,{className:"bg-yellow-100 text-yellow-800",children:["Current: ",e.student?.level]}),(0,a.jsx)("span",{className:"text-gray-400",children:"→"}),(0,a.jsxs)(l.E,{className:"bg-blue-100 text-blue-800",children:["Target: ",e.student?.nextLevel]})]}),(0,a.jsxs)("span",{className:"text-2xl font-bold",children:[e.progress?.overall,"%"]})]}),(0,a.jsx)(w,{value:e.progress?.overall,className:"h-3"}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["You're ",100-(e.progress?.overall||0),"% away from reaching ",e.student?.nextLevel," level!"]})]})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(A.A,{className:"h-5 w-5 mr-2"}),"Skills Assessment"]}),(0,a.jsx)(n.BT,{children:"Your performance across different language skills"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:e.skills.map((e,s)=>(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{variant:"outline",className:"text-xs",children:e.level}),(0,a.jsxs)("span",{className:"text-sm font-medium",children:[e.progress,"%"]})]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:`h-2 rounded-full ${c(e.progress)}`,style:{width:`${e.progress}%`}})})]},s))})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(C.A,{className:"h-5 w-5 mr-2"}),"Recent Test Results"]}),(0,a.jsx)(n.BT,{children:"Your latest assessment scores"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:e.recentActivity?.assessments?.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:e.testName}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:new Date(e.completedAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:`text-lg font-bold ${x(e.score,e.maxScore)}`,children:[e.score,"/",e.maxScore]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[Math.round(e.score/e.maxScore*100),"%"]})]})]},s))||(0,a.jsx)("p",{className:"text-gray-500 text-center py-4",children:"No recent assessments available."})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(T.A,{className:"h-5 w-5 mr-2"}),"Achievements"]}),(0,a.jsx)(n.BT,{children:"Your learning milestones and accomplishments"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:e.achievements.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-4 border rounded-lg bg-gradient-to-r from-yellow-50 to-orange-50",children:[(0,a.jsx)("div",{className:"text-2xl",children:e.icon}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.date})]})]},s))})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsx)(n.aR,{children:(0,a.jsxs)(n.ZB,{className:"flex items-center",children:[(0,a.jsx)(_,{className:"h-5 w-5 mr-2"}),"Personalized Learning Tips"]})}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-50 border-l-4 border-blue-400 rounded",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Focus on Writing:"})," Your writing skills need improvement. Practice daily writing exercises to reach B1+ level."]})}),(0,a.jsx)("div",{className:"p-3 bg-green-50 border-l-4 border-green-400 rounded",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Great Grammar Progress:"})," You're excelling in grammar! Keep up the excellent work."]})}),(0,a.jsx)("div",{className:"p-3 bg-yellow-50 border-l-4 border-yellow-400 rounded",children:(0,a.jsxs)("p",{className:"text-sm",children:[(0,a.jsx)("strong",{children:"Vocabulary Building:"})," Expand your vocabulary by reading more English texts and using new words in conversations."]})})]})})]})]}):(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)(P.Fc,{children:(0,a.jsx)(P.TN,{children:"No progress data available."})})})}},85942:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\student\\\\progress\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\progress\\page.tsx","default")}};var s=require("../../../../../webpack-runtime.js");s.C(e);var r=e=>s(s.s=e),a=s.X(0,[4243,7615,2918,8887,3039],()=>r(1179));module.exports=a})();