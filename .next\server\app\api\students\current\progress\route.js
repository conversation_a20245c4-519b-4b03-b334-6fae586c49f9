(()=>{var e={};e.id=6430,e.ids=[6430],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=o.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},a=await fetch(s,{method:r.method,headers:n,body:r.data?JSON.stringify(r.data):void 0}),i=await a.json();return{success:a.ok,data:i,status:a.status,error:a.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>o,cU:()=>a,g2:()=>s});class a{static async authenticateUser(e,r){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class o{static logRequest(e,r,t,s){let n=new Date().toISOString(),a=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:a,success:t,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31736:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>h});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),u=t(19854),c=t(41098),l=t(79464);async function p(e){try{let e=await (0,u.getServerSession)(c.N);if(!e?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let r=await l.z.student.findUnique({where:{userId:e.user.id},include:{user:{select:{name:!0,email:!0}},currentGroup:{include:{course:{select:{name:!0,level:!0}}}},attendances:{include:{class:{select:{date:!0,topic:!0}}},orderBy:{createdAt:"desc"},take:50},assessments:{orderBy:{createdAt:"desc"},take:20},payments:{where:{status:"PAID"},orderBy:{paidDate:"desc"}}}});if(!r)return i.NextResponse.json({error:"Student profile not found"},{status:404});let t=r.attendances.length,s=r.attendances.filter(e=>"PRESENT"===e.status).length,n=t>0?s/t*100:0,a=r.assessments.filter(e=>null!==e.score),o=a.length>0?a.reduce((e,r)=>e+(r.score||0),0)/a.length:0,p=Math.min(100,.4*n+.6*o),d=["A1","A2","B1","B2","IELTS","SAT","MATH","KIDS"],m=d.indexOf(r.level),h=m>=0&&m<d.length-1?d[m+1]:null,v=[{name:"Speaking",progress:Math.min(100,Math.max(0,.9*o)),level:r.level},{name:"Listening",progress:Math.min(100,Math.max(0,1.1*o)),level:r.level},{name:"Reading",progress:Math.min(100,Math.max(0,1.05*o)),level:r.level},{name:"Writing",progress:Math.min(100,Math.max(0,.85*o)),level:r.level},{name:"Grammar",progress:Math.min(100,Math.max(0,o)),level:r.level},{name:"Vocabulary",progress:Math.min(100,Math.max(0,.95*o)),level:r.level}],g=[];n>=95&&g.push({name:"Perfect Attendance",date:new Date().toISOString(),icon:"\uD83C\uDFAF"}),o>=90&&g.push({name:"Excellence Award",date:new Date().toISOString(),icon:"\uD83C\uDFC6"}),a.length>=10&&g.push({name:"Assessment Master",date:new Date().toISOString(),icon:"\uD83D\uDCDA"});let E={student:{name:r.user.name,email:r.user.email,level:r.level,nextLevel:h,branch:r.branch},currentGroup:r.currentGroup?{name:r.currentGroup.name,course:r.currentGroup.course.name,level:r.currentGroup.course.level}:null,progress:{overall:Math.round(p),attendance:Math.round(n),averageScore:Math.round(o)},statistics:{totalClasses:t,attendedClasses:s,completedAssessments:a.length,pendingAssessments:r.assessments.filter(e=>null===e.score).length,totalPayments:r.payments.length,totalPaid:r.payments.reduce((e,r)=>e+Number(r.amount),0)},skills:v,achievements:g,recentActivity:{assessments:r.assessments.slice(0,5).map(e=>({id:e.id,testName:e.testName,score:e.score,maxScore:e.maxScore,passed:e.passed,completedAt:e.completedAt})),attendance:r.attendances.slice(0,10).map(e=>({id:e.id,date:e.class.date,topic:e.class.topic,status:e.status}))}};return i.NextResponse.json(E)}catch(e){return console.error("Error fetching student progress:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let d=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/students/current/progress/route",pathname:"/api/students/current/progress",filename:"route",bundlePath:"app/api/students/current/progress/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\current\\progress\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:h,serverHooks:v}=d;function g(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:h})}},41098:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(13581),n=t(7786);let a={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let r=await n.cU.authenticateUser(e.phone,e.password);if(!r.success)return console.error("Authentication failed:",r.error),null;let t=r.data.user;if(!t)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(t.role))return console.error("User role not allowed on staff server:",t.role),null;return{id:t.id,phone:t.phone,name:t.name,email:t.email,role:t.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role||null),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3412],()=>t(31736));module.exports=s})();