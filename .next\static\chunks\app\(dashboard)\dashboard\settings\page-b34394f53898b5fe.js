(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2791],{381:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2895).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},968:(e,s,a)=>{"use strict";a.d(s,{b:()=>n});var r=a(2115),l=a(3655),i=a(5155),t=r.forwardRef((e,s)=>(0,i.jsx)(l.sG.label,{...e,ref:s,onMouseDown:s=>{var a;s.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));t.displayName="Label";var n=t},1007:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2714:(e,s,a)=>{"use strict";a.d(s,{J:()=>c});var r=a(5155),l=a(2115),i=a(968),t=a(2085),n=a(3999);let d=(0,t.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(i.b,{ref:s,className:(0,n.cn)(d(),a),...l})});c.displayName=i.b.displayName},3861:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2895).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},3999:(e,s,a)=>{"use strict";a.d(s,{Yq:()=>n,cn:()=>i,r6:()=>d,vv:()=>t});var r=a(2596),l=a(9688);function i(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return(0,l.QP)((0,r.$)(s))}function t(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function n(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s)}function d(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}},5525:(e,s,a)=>{"use strict";a.d(s,{A:()=>r});let r=(0,a(2895).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5688:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>p});var r=a(5155),l=a(2115),i=a(8482),t=a(7168),n=a(9852),d=a(2714),c=a(5784),o=a(381),x=a(1007),m=a(3861),h=a(5525),u=a(2895);let f=(0,u.A)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),j=(0,u.A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);function p(){let[e,s]=(0,l.useState)("general"),a=[{id:"general",label:"General",icon:o.A},{id:"profile",label:"Profile",icon:x.A},{id:"notifications",label:"Notifications",icon:m.A},{id:"security",label:"Security",icon:h.A},{id:"system",label:"System",icon:f},{id:"localization",label:"Localization",icon:j}];return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Settings"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Manage your account and system preferences"})]}),(0,r.jsxs)("div",{className:"flex space-x-6",children:[(0,r.jsx)("div",{className:"w-64 space-y-2",children:a.map(a=>(0,r.jsxs)("button",{onClick:()=>s(a.id),className:"w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ".concat(e===a.id?"bg-blue-50 text-blue-700 border border-blue-200":"text-gray-600 hover:bg-gray-50"),children:[(0,r.jsx)(a.icon,{className:"h-5 w-5"}),(0,r.jsx)("span",{children:a.label})]},a.id))}),(0,r.jsxs)("div",{className:"flex-1",children:["general"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"General Settings"}),(0,r.jsx)(i.BT,{children:"Basic system configuration and preferences"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"institution-name",children:"Institution Name"}),(0,r.jsx)(n.p,{id:"institution-name",defaultValue:"Innovative Centre"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"academic-year",children:"Academic Year"}),(0,r.jsxs)(c.l6,{defaultValue:"2024",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"2024",children:"2024"}),(0,r.jsx)(c.eb,{value:"2025",children:"2025"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"default-currency",children:"Default Currency"}),(0,r.jsxs)(c.l6,{defaultValue:"UZS",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"UZS",children:"UZS - Uzbek Som"}),(0,r.jsx)(c.eb,{value:"USD",children:"USD - US Dollar"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"timezone",children:"Timezone"}),(0,r.jsxs)(c.l6,{defaultValue:"Asia/Tashkent",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"Asia/Tashkent",children:"Asia/Tashkent"}),(0,r.jsx)(c.eb,{value:"UTC",children:"UTC"})]})]})]})]}),(0,r.jsx)(t.$,{children:"Save Changes"})]})]}),"profile"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Profile Settings"}),(0,r.jsx)(i.BT,{children:"Manage your personal information"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,r.jsx)("div",{className:"h-20 w-20 rounded-full bg-gray-300 flex items-center justify-center",children:(0,r.jsx)(x.A,{className:"h-8 w-8 text-gray-600"})}),(0,r.jsxs)("div",{children:[(0,r.jsx)(t.$,{variant:"outline",children:"Change Photo"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"JPG, PNG up to 2MB"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"first-name",children:"First Name"}),(0,r.jsx)(n.p,{id:"first-name",defaultValue:"Admin"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"last-name",children:"Last Name"}),(0,r.jsx)(n.p,{id:"last-name",defaultValue:"User"})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"email",children:"Email"}),(0,r.jsx)(n.p,{id:"email",type:"email",defaultValue:"<EMAIL>"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"phone",children:"Phone"}),(0,r.jsx)(n.p,{id:"phone",defaultValue:"+998901234567"})]})]}),(0,r.jsx)(t.$,{children:"Update Profile"})]})]}),"notifications"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Notification Settings"}),(0,r.jsx)(i.BT,{children:"Configure how you receive notifications"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"New Lead Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Get notified when new leads are captured"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Payment Notifications"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Receive alerts for payment activities"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Class Reminders"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Get reminded about upcoming classes"})]}),(0,r.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"System Updates"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Notifications about system maintenance"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]})]}),(0,r.jsx)(t.$,{children:"Save Preferences"})]})]}),"security"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Security Settings"}),(0,r.jsx)(i.BT,{children:"Manage your account security"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"current-password",children:"Current Password"}),(0,r.jsx)(n.p,{id:"current-password",type:"password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"new-password",children:"New Password"}),(0,r.jsx)(n.p,{id:"new-password",type:"password"})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"confirm-password",children:"Confirm New Password"}),(0,r.jsx)(n.p,{id:"confirm-password",type:"password"})]})]}),(0,r.jsxs)("div",{className:"border-t pt-4",children:[(0,r.jsx)("h4",{className:"font-medium mb-4",children:"Two-Factor Authentication"}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm",children:"Enable 2FA for additional security"}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"Requires SMS verification"})]}),(0,r.jsx)(t.$,{variant:"outline",children:"Enable 2FA"})]})]}),(0,r.jsx)(t.$,{children:"Update Password"})]})]}),"system"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"System Settings"}),(0,r.jsx)(i.BT,{children:"Configure system-wide settings"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"backup-frequency",children:"Backup Frequency"}),(0,r.jsxs)(c.l6,{defaultValue:"daily",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"daily",children:"Daily"}),(0,r.jsx)(c.eb,{value:"weekly",children:"Weekly"}),(0,r.jsx)(c.eb,{value:"monthly",children:"Monthly"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"session-timeout",children:"Session Timeout (minutes)"}),(0,r.jsx)(n.p,{id:"session-timeout",type:"number",defaultValue:"60"})]})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Maintenance Mode"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Temporarily disable system access"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium",children:"Debug Mode"}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:"Enable detailed error logging"})]}),(0,r.jsx)("input",{type:"checkbox",className:"rounded"})]})]}),(0,r.jsx)(t.$,{children:"Save System Settings"})]})]}),"localization"===e&&(0,r.jsxs)(i.Zp,{children:[(0,r.jsxs)(i.aR,{children:[(0,r.jsx)(i.ZB,{children:"Localization Settings"}),(0,r.jsx)(i.BT,{children:"Configure language and regional settings"})]}),(0,r.jsxs)(i.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"language",children:"Default Language"}),(0,r.jsxs)(c.l6,{defaultValue:"en",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"en",children:"English"}),(0,r.jsx)(c.eb,{value:"uz",children:"Uzbek"}),(0,r.jsx)(c.eb,{value:"ru",children:"Russian"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"date-format",children:"Date Format"}),(0,r.jsxs)(c.l6,{defaultValue:"dd/mm/yyyy",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"dd/mm/yyyy",children:"DD/MM/YYYY"}),(0,r.jsx)(c.eb,{value:"mm/dd/yyyy",children:"MM/DD/YYYY"}),(0,r.jsx)(c.eb,{value:"yyyy-mm-dd",children:"YYYY-MM-DD"})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"number-format",children:"Number Format"}),(0,r.jsxs)(c.l6,{defaultValue:"1,234.56",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"1,234.56",children:"1,234.56"}),(0,r.jsx)(c.eb,{value:"1.234,56",children:"1.234,56"}),(0,r.jsx)(c.eb,{value:"1 234.56",children:"1 234.56"})]})]})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)(d.J,{htmlFor:"first-day",children:"First Day of Week"}),(0,r.jsxs)(c.l6,{defaultValue:"monday",children:[(0,r.jsx)(c.bq,{children:(0,r.jsx)(c.yv,{})}),(0,r.jsxs)(c.gC,{children:[(0,r.jsx)(c.eb,{value:"sunday",children:"Sunday"}),(0,r.jsx)(c.eb,{value:"monday",children:"Monday"})]})]})]})]}),(0,r.jsx)(t.$,{children:"Save Localization Settings"})]})]})]})]})]})}},5784:(e,s,a)=>{"use strict";a.d(s,{bq:()=>m,eb:()=>j,gC:()=>f,l6:()=>o,yv:()=>x});var r=a(5155),l=a(2115),i=a(1992),t=a(6474),n=a(7863),d=a(5196),c=a(3999);let o=i.bL;i.YJ;let x=i.WT,m=l.forwardRef((e,s)=>{let{className:a,children:l,...n}=e;return(0,r.jsxs)(i.l9,{ref:s,className:(0,c.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...n,children:[l,(0,r.jsx)(i.In,{asChild:!0,children:(0,r.jsx)(t.A,{className:"h-4 w-4 opacity-50"})})]})});m.displayName=i.l9.displayName;let h=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(i.PP,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=i.PP.displayName;let u=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(i.wn,{ref:s,className:(0,c.cn)("flex cursor-default items-center justify-center py-1",a),...l,children:(0,r.jsx)(t.A,{className:"h-4 w-4"})})});u.displayName=i.wn.displayName;let f=l.forwardRef((e,s)=>{let{className:a,children:l,position:t="popper",...n}=e;return(0,r.jsx)(i.ZL,{children:(0,r.jsxs)(i.UC,{ref:s,className:(0,c.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===t&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:t,...n,children:[(0,r.jsx)(h,{}),(0,r.jsx)(i.LM,{className:(0,c.cn)("p-1","popper"===t&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:l}),(0,r.jsx)(u,{})]})})});f.displayName=i.UC.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(i.JU,{ref:s,className:(0,c.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...l})}).displayName=i.JU.displayName;let j=l.forwardRef((e,s)=>{let{className:a,children:l,...t}=e;return(0,r.jsxs)(i.q7,{ref:s,className:(0,c.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...t,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(i.VF,{children:(0,r.jsx)(d.A,{className:"h-4 w-4"})})}),(0,r.jsx)(i.p4,{children:l})]})});j.displayName=i.q7.displayName,l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)(i.wv,{ref:s,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",a),...l})}).displayName=i.wv.displayName},7168:(e,s,a)=>{"use strict";a.d(s,{$:()=>c,r:()=>d});var r=a(5155),l=a(2115),i=a(9708),t=a(2085),n=a(3999);let d=(0,t.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=l.forwardRef((e,s)=>{let{className:a,variant:l,size:t,asChild:c=!1,...o}=e,x=c?i.DX:"button";return(0,r.jsx)(x,{className:(0,n.cn)(d({variant:l,size:t,className:a})),ref:s,...o})});c.displayName="Button"},7187:(e,s,a)=>{Promise.resolve().then(a.bind(a,5688))},8482:(e,s,a)=>{"use strict";a.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>d,Zp:()=>t,aR:()=>n});var r=a(5155),l=a(2115),i=a(3999);let t=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",a),...l})});t.displayName="Card";let n=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",a),...l})});n.displayName="CardHeader";let d=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",a),...l})});d.displayName="CardTitle";let c=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",a),...l})});c.displayName="CardDescription";let o=l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",a),...l})});o.displayName="CardContent",l.forwardRef((e,s)=>{let{className:a,...l}=e;return(0,r.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",a),...l})}).displayName="CardFooter"},9852:(e,s,a)=>{"use strict";a.d(s,{p:()=>t});var r=a(5155),l=a(2115),i=a(3999);let t=l.forwardRef((e,s)=>{let{className:a,type:l,...t}=e;return(0,r.jsx)("input",{type:l,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...t})});t.displayName="Input"}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,8441,1684,7358],()=>s(7187)),_N_E=e.O()}]);