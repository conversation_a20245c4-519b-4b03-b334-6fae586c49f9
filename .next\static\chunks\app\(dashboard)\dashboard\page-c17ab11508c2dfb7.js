(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6337],{1605:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>T});var r=s(5155),a=s(2115),n=s(8482),l=s(8145),i=s(7168),c=s(6891),d=s(3999),o=s(9397),u=s(3904),m=s(2895);let h=(0,m.A)("ExternalLink",[["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}],["polyline",{points:"15 3 21 3 21 9",key:"mznyad"}],["line",{x1:"10",x2:"21",y1:"14",y2:"3",key:"18c3s4"}]]);var x=s(6874),f=s.n(x);function p(e){let{limit:t=10,showHeader:s=!0,showRefresh:m=!0,showViewAll:x=!0,userId:p,resource:v,className:g}=e,[j,N]=(0,a.useState)([]),[y,b]=(0,a.useState)(!0),[w,A]=(0,a.useState)(null),C=(0,a.useCallback)(async()=>{b(!0),A(null);try{let e=new URLSearchParams({limit:t.toString(),page:"1"});p&&e.append("userId",p),v&&e.append("resource",v);let s=await fetch("/api/activity-logs?".concat(e));if(!s.ok)throw Error("Failed to fetch activities");let r=await s.json();N(r.logs||[])}catch(e){A(e instanceof Error?e.message:"Failed to load activities")}finally{b(!1)}},[t,p,v]);(0,a.useEffect)(()=>{C()},[C]);let E=e=>{switch(e){case"ADMIN":return"bg-red-100 text-red-800";case"MANAGER":return"bg-blue-100 text-blue-800";case"TEACHER":return"bg-green-100 text-green-800";case"RECEPTION":return"bg-yellow-100 text-yellow-800";case"CASHIER":return"bg-purple-100 text-purple-800";default:return"bg-gray-100 text-gray-800"}};return w?(0,r.jsx)(n.Zp,{className:g,children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"text-center text-red-600",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2"}),(0,r.jsx)("p",{children:"Failed to load activity feed"}),(0,r.jsxs)(i.$,{variant:"outline",size:"sm",onClick:C,className:"mt-2",children:[(0,r.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Retry"]})]})})}):(0,r.jsxs)(n.Zp,{className:g,children:[s&&(0,r.jsx)(n.aR,{className:"pb-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)(n.ZB,{className:"flex items-center",children:[(0,r.jsx)(o.A,{className:"h-5 w-5 mr-2"}),"Recent Activity"]}),(0,r.jsx)(n.BT,{children:"Latest system activities and user actions"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[m&&(0,r.jsx)(i.$,{variant:"outline",size:"sm",onClick:C,disabled:y,children:(0,r.jsx)(u.A,{className:"h-4 w-4 ".concat(y?"animate-spin":"")})}),x&&(0,r.jsx)(i.$,{variant:"outline",size:"sm",asChild:!0,children:(0,r.jsxs)(f(),{href:"/dashboard/admin/activity-logs",children:[(0,r.jsx)(h,{className:"h-4 w-4 mr-2"}),"View All"]})})]})]})}),(0,r.jsx)(n.Wu,{className:"p-0",children:y?(0,r.jsxs)("div",{className:"p-6 text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-2",children:"Loading activities..."})]}):0===j.length?(0,r.jsxs)("div",{className:"p-6 text-center text-gray-500",children:[(0,r.jsx)(o.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No recent activities"})]}):(0,r.jsx)(c.F,{className:"h-96",children:(0,r.jsx)("div",{className:"p-4 space-y-3",children:j.map((e,t)=>{let s=["DELETE","LOGIN","LOGOUT"].includes(e.action)?"high":["CREATE","CONTACT","COMPLETE"].includes(e.action)?"medium":"low",a=function(e){var t,s,r,a,n,l,i;let c={CREATE:"Created",UPDATE:"Updated",DELETE:"Deleted",LOGIN:"Logged in",LOGOUT:"Logged out",CONTACT:"Contacted",COMPLETE:"Completed",VIEW:"Viewed",EXPORT:"Exported"}[l=e.action]||l,d={student:"Student",lead:"Lead",payment:"Payment",group:"Group",enrollment:"Enrollment",assessment:"Assessment",teacher:"Teacher",course:"Course",class:"Class",attendance:"Attendance",user:"User",auth:"Authentication"}[i=e.resource]||i.charAt(0).toUpperCase()+i.slice(1),o=e.user.name;switch(e.action){case"LOGIN":return"".concat(o," logged into the system");case"LOGOUT":return"".concat(o," logged out of the system");case"CONTACT":if("lead"===e.resource){let s=(null==(t=e.details)?void 0:t.leadName)||"a lead";return"".concat(o," contacted ").concat(s)}break;case"CREATE":if("student"===e.resource){let t=(null==(s=e.details)?void 0:s.studentName)||"a student";return"".concat(o," created student record for ").concat(t)}if("payment"===e.resource){let t=(null==(r=e.details)?void 0:r.amount)||"payment";return"".concat(o," created a payment record (").concat(t,")")}break;case"COMPLETE":if("assessment"===e.resource){let t=(null==(a=e.details)?void 0:a.studentName)||"a student",s=(null==(n=e.details)?void 0:n.type)||"assessment";return"".concat(o," completed ").concat(s.toLowerCase().replace("_"," ")," for ").concat(t)}}return"".concat(o," ").concat(c.toLowerCase()," ").concat(d.toLowerCase())}(e),n={CREATE:"➕",UPDATE:"✏️",DELETE:"\uD83D\uDDD1️",LOGIN:"\uD83D\uDD10",LOGOUT:"\uD83D\uDEAA",CONTACT:"\uD83D\uDCDE",COMPLETE:"✅",VIEW:"\uD83D\uDC41️",EXPORT:"\uD83D\uDCE4"}[e.action]||"\uD83D\uDCDD",i=function(e){let t=new Date(e),s=Math.floor((new Date().getTime()-t.getTime())/1e3);if(s<60)return"Just now";let r=Math.floor(s/60);if(r<60)return"".concat(r," minute").concat(r>1?"s":""," ago");let a=Math.floor(r/60);if(a<24)return"".concat(a," hour").concat(a>1?"s":""," ago");let n=Math.floor(a/24);if(n<7)return"".concat(n," day").concat(n>1?"s":""," ago");let l=Math.floor(n/7);return l<4?"".concat(l," week").concat(l>1?"s":""," ago"):t.toLocaleDateString()}(e.createdAt);return(0,r.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border transition-colors hover:bg-gray-50 ".concat(0===t?"bg-blue-50 border-blue-200":"border-gray-200"),children:[(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm ".concat("high"===s?"bg-red-100":"medium"===s?"bg-yellow-100":"bg-gray-100"),children:n})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:a}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsx)(l.E,{className:E(e.userRole),variant:"secondary",children:e.userRole}),"high"===s&&(0,r.jsx)(l.E,{variant:"destructive",className:"text-xs",children:"High"})]})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("p",{className:"text-xs text-gray-500",children:e.user.name}),(0,r.jsx)("p",{className:"text-xs text-gray-500",title:(0,d.Yq)(e.createdAt),children:i})]})]})]},e.id)})})})})]})}var v=s(7624),g=s(9074),j=s(4576),N=s(7580),y=s(3109);let b=(0,m.A)("TrendingDown",[["polyline",{points:"22 17 13.5 8.5 8.5 13.5 2 7",key:"1r2t7k"}],["polyline",{points:"16 17 22 17 22 11",key:"11uiuu"}]]);var w=s(2318),A=s(5040),C=s(2915),E=s(5868);let k=(0,m.A)("ArrowUpRight",[["path",{d:"M7 7h10v10",key:"1tivn9"}],["path",{d:"M7 17 17 7",key:"1vkiza"}]]);var R=s(4186),L=s(7705);function T(){var e,t,s,l,c,d,o,u,m;let h,{currentBranch:x}=(0,L.O)(),[f,T]=(0,a.useState)(null),[S,O]=(0,a.useState)(!0),[M,Z]=(0,a.useState)(null);(0,a.useEffect)(()=>{D()},[]);let D=async()=>{try{O(!0);let e=await fetch("/api/dashboard/stats");if(!e.ok)throw Error("Failed to fetch dashboard stats");let t=await e.json();T(t),Z(null)}catch(e){console.error("Error fetching dashboard stats:",e),Z("Failed to load dashboard data")}finally{O(!1)}},$=e=>new Intl.NumberFormat().format(e);return S?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsx)(v.A,{className:"h-8 w-8 animate-spin text-blue-600"})}):M?(0,r.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-red-600 mb-4",children:M}),(0,r.jsx)(i.$,{onClick:D,children:"Try Again"})]})}):(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{className:"flex flex-col md:flex-row md:items-center md:justify-between gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 tracking-tight",children:["Welcome to ",x.name]}),(0,r.jsx)("p",{className:"text-gray-600 mt-1",children:"Here's what's happening with your educational center today."})]}),(0,r.jsxs)("div",{className:"flex gap-3",children:[(0,r.jsxs)(i.$,{variant:"outline",className:"shadow-sm",children:[(0,r.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"View Schedule"]}),(0,r.jsxs)(i.$,{className:"shadow-sm",children:[(0,r.jsx)(j.A,{className:"h-4 w-4 mr-2"}),"Analytics"]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)(n.Zp,{className:"kpi-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(n.ZB,{className:"kpi-label",children:"Total Students"}),(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-blue-50 flex items-center justify-center",children:(0,r.jsx)(N.A,{className:"h-5 w-5 text-blue-600"})})]})}),(0,r.jsxs)(n.Wu,{className:"dashboard-card-content",children:[(0,r.jsx)("div",{className:"kpi-value text-blue-700",children:$((null==f?void 0:f.totalStudents.count)||0)}),(0,r.jsxs)("div",{className:"kpi-change mt-2 ".concat((null!=(e=null==f?void 0:f.totalStudents.growth)?e:0)>=0?"text-green-600":"text-red-600"),children:[(null!=(t=null==f?void 0:f.totalStudents.growth)?t:0)>=0?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(b,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[(null!=(s=null==f?void 0:f.totalStudents.growth)?s:0)>=0?"+":"",(null==f?void 0:f.totalStudents.growth)||0,"% from last month"]})]})]})]}),(0,r.jsxs)(n.Zp,{className:"kpi-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(n.ZB,{className:"kpi-label",children:"New Leads"}),(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-green-50 flex items-center justify-center",children:(0,r.jsx)(w.A,{className:"h-5 w-5 text-green-600"})})]})}),(0,r.jsxs)(n.Wu,{className:"dashboard-card-content",children:[(0,r.jsx)("div",{className:"kpi-value text-green-700",children:$((null==f?void 0:f.newLeads.count)||0)}),(0,r.jsxs)("div",{className:"kpi-change mt-2 ".concat((null!=(l=null==f?void 0:f.newLeads.growth)?l:0)>=0?"text-green-600":"text-red-600"),children:[(null!=(c=null==f?void 0:f.newLeads.growth)?c:0)>=0?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(b,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[(null!=(d=null==f?void 0:f.newLeads.growth)?d:0)>=0?"+":"",(null==f?void 0:f.newLeads.growth)||0,"% from last week"]})]})]})]}),(0,r.jsxs)(n.Zp,{className:"kpi-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(n.ZB,{className:"kpi-label",children:"Active Groups"}),(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-purple-50 flex items-center justify-center",children:(0,r.jsx)(A.A,{className:"h-5 w-5 text-purple-600"})})]})}),(0,r.jsxs)(n.Wu,{className:"dashboard-card-content",children:[(0,r.jsx)("div",{className:"kpi-value text-purple-700",children:$((null==f?void 0:f.activeGroups.count)||0)}),(0,r.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,r.jsxs)("div",{className:"kpi-change text-blue-600",children:[(0,r.jsx)(C.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{children:"Active groups"})]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"text-xs text-blue-600 hover:text-blue-700",children:"View All"})]})]})]}),(0,r.jsxs)(n.Zp,{className:"kpi-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)(n.ZB,{className:"kpi-label",children:"Monthly Revenue"}),(0,r.jsx)("div",{className:"h-10 w-10 rounded-full bg-amber-50 flex items-center justify-center",children:(0,r.jsx)(E.A,{className:"h-5 w-5 text-amber-600"})})]})}),(0,r.jsxs)(n.Wu,{className:"dashboard-card-content",children:[(0,r.jsx)("div",{className:"kpi-value text-amber-700",children:(h=(null==f?void 0:f.monthlyRevenue.amount)||0,new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0,maximumFractionDigits:0}).format(h).replace("UZS","UZS"))}),(0,r.jsxs)("div",{className:"kpi-change mt-2 ".concat((null!=(o=null==f?void 0:f.monthlyRevenue.growth)?o:0)>=0?"text-green-600":"text-red-600"),children:[(null!=(u=null==f?void 0:f.monthlyRevenue.growth)?u:0)>=0?(0,r.jsx)(y.A,{className:"h-4 w-4"}):(0,r.jsx)(b,{className:"h-4 w-4"}),(0,r.jsxs)("span",{children:[(null!=(m=null==f?void 0:f.monthlyRevenue.growth)?m:0)>=0?"+":"",(null==f?void 0:f.monthlyRevenue.growth)||0,"% from last month"]})]})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(n.Zp,{className:"dashboard-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-lg font-semibold text-gray-900",children:"Recent Leads"}),(0,r.jsx)(n.BT,{className:"mt-1",children:"Latest inquiries from potential students"})]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"text-sm",children:(0,r.jsx)(k,{className:"h-4 w-4"})})]})}),(0,r.jsx)(n.Wu,{className:"dashboard-card-content",children:(0,r.jsx)("div",{className:"space-y-4",children:(null==f?void 0:f.recentLeads)&&f.recentLeads.length>0?f.recentLeads.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.course})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm text-gray-500 mb-1",children:e.time}),(0,r.jsx)("span",{className:"status-badge ".concat("NEW"===e.status?"status-active":"CONTACTED"===e.status?"status-pending":"status-active"),children:e.status})]})]},t)):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(w.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No recent leads"})]})})})]}),(0,r.jsxs)(n.Zp,{className:"dashboard-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-lg font-semibold text-gray-900",children:"Upcoming Classes"}),(0,r.jsx)(n.BT,{className:"mt-1",children:"Today's scheduled classes"})]}),(0,r.jsx)(i.$,{variant:"ghost",size:"sm",className:"text-sm",children:(0,r.jsx)(g.A,{className:"h-4 w-4"})})]})}),(0,r.jsx)(n.Wu,{className:"dashboard-card-content",children:(0,r.jsx)("div",{className:"space-y-4",children:(null==f?void 0:f.upcomingClasses)&&f.upcomingClasses.length>0?f.upcomingClasses.map((e,t)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors duration-200",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"font-medium text-gray-900",children:e.group}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.teacher})]}),(0,r.jsxs)("div",{className:"text-right",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-blue-600",children:e.time}),(0,r.jsx)("p",{className:"text-sm text-gray-500",children:e.room})]})]},t)):(0,r.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,r.jsx)(g.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,r.jsx)("p",{children:"No classes scheduled for today"})]})})})]}),(0,r.jsxs)(n.Zp,{className:"dashboard-card",children:[(0,r.jsx)(n.aR,{className:"dashboard-card-header",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(n.ZB,{className:"text-lg font-semibold text-gray-900",children:"Recent Activity"}),(0,r.jsx)(n.BT,{className:"mt-1",children:"Latest system activities"})]}),(0,r.jsxs)(i.$,{variant:"ghost",size:"sm",className:"text-sm",children:[(0,r.jsx)(R.A,{className:"h-4 w-4 mr-2"}),"View All"]})]})}),(0,r.jsx)(n.Wu,{className:"dashboard-card-content",children:(0,r.jsx)(p,{limit:8,showHeader:!1,showRefresh:!1,showViewAll:!1})})]})]})]})}},2712:(e,t,s)=>{"use strict";s.d(t,{N:()=>a});var r=s(2115),a=globalThis?.document?r.useLayoutEffect:()=>{}},2895:(e,t,s)=>{"use strict";s.d(t,{A:()=>l});var r=s(2115),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let n=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),l=(e,t)=>{let s=(0,r.forwardRef)((s,l)=>{let{color:i="currentColor",size:c=24,strokeWidth:d=2,absoluteStrokeWidth:o,className:u="",children:m,...h}=s;return(0,r.createElement)("svg",{ref:l,...a,width:c,height:c,stroke:i,strokeWidth:o?24*Number(d)/Number(c):d,className:["lucide","lucide-".concat(n(e)),u].join(" "),...h},[...t.map(e=>{let[t,s]=e;return(0,r.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s}},3655:(e,t,s)=>{"use strict";s.d(t,{hO:()=>c,sG:()=>i});var r=s(2115),a=s(7650),n=s(9708),l=s(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let s=(0,n.TL)(`Primitive.${t}`),a=r.forwardRef((e,r)=>{let{asChild:a,...n}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,l.jsx)(a?s:t,{...n,ref:r})});return a.displayName=`Primitive.${t}`,{...e,[t]:a}},{});function c(e,t){e&&a.flushSync(()=>e.dispatchEvent(t))}},3904:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},3999:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>n,r6:()=>c,vv:()=>l});var r=s(2596),a=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function l(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function c(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4315:(e,t,s)=>{"use strict";s.d(t,{jH:()=>n});var r=s(2115);s(5155);var a=r.createContext(void 0);function n(e){let t=r.useContext(a);return e||t||"ltr"}},5185:(e,t,s)=>{"use strict";function r(e,t,{checkForDefaultPrevented:s=!0}={}){return function(r){if(e?.(r),!1===s||!r.defaultPrevented)return t?.(r)}}s.d(t,{m:()=>r})},5868:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("DollarSign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6081:(e,t,s)=>{"use strict";s.d(t,{A:()=>l,q:()=>n});var r=s(2115),a=s(5155);function n(e,t){let s=r.createContext(t),n=e=>{let{children:t,...n}=e,l=r.useMemo(()=>n,Object.values(n));return(0,a.jsx)(s.Provider,{value:l,children:t})};return n.displayName=e+"Provider",[n,function(a){let n=r.useContext(s);if(n)return n;if(void 0!==t)return t;throw Error(`\`${a}\` must be used within \`${e}\``)}]}function l(e,t=[]){let s=[],n=()=>{let t=s.map(e=>r.createContext(e));return function(s){let a=s?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...s,[e]:a}}),[s,a])}};return n.scopeName=e,[function(t,n){let l=r.createContext(n),i=s.length;s=[...s,n];let c=t=>{let{scope:s,children:n,...c}=t,d=s?.[e]?.[i]||l,o=r.useMemo(()=>c,Object.values(c));return(0,a.jsx)(d.Provider,{value:o,children:n})};return c.displayName=t+"Provider",[c,function(s,a){let c=a?.[e]?.[i]||l,d=r.useContext(c);if(d)return d;if(void 0!==n)return n;throw Error(`\`${s}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let s=()=>{let s=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=s.reduce((t,{useScope:s,scopeName:r})=>{let a=s(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return s.scopeName=t.scopeName,s}(n,...t)]}},6101:(e,t,s)=>{"use strict";s.d(t,{s:()=>l,t:()=>n});var r=s(2115);function a(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function n(...e){return t=>{let s=!1,r=e.map(e=>{let r=a(e,t);return s||"function"!=typeof r||(s=!0),r});if(s)return()=>{for(let t=0;t<r.length;t++){let s=r[t];"function"==typeof s?s():a(e[t],null)}}}}function l(...e){return r.useCallback(n(...e),e)}},6891:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var r=s(5155),a=s(2115),n=s(7655),l=s(3999);let i=a.forwardRef((e,t)=>{let{className:s,children:a,...i}=e;return(0,r.jsxs)(n.bL,{ref:t,className:(0,l.cn)("relative overflow-hidden",s),...i,children:[(0,r.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:a}),(0,r.jsx)(c,{}),(0,r.jsx)(n.OK,{})]})});i.displayName=n.bL.displayName;let c=a.forwardRef((e,t)=>{let{className:s,orientation:a="vertical",...i}=e;return(0,r.jsx)(n.VM,{ref:t,orientation:a,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===a&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===a&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),...i,children:(0,r.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})})});c.displayName=n.VM.displayName},7060:(e,t,s)=>{Promise.resolve().then(s.bind(s,1605))},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>c});var r=s(5155),a=s(2115),n=s(9708),l=s(2085),i=s(3999);let c=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef((e,t)=>{let{className:s,variant:a,size:l,asChild:d=!1,...o}=e,u=d?n.DX:"button";return(0,r.jsx)(u,{className:(0,i.cn)(c({variant:a,size:l,className:s})),ref:t,...o})});d.displayName="Button"},7624:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7705:(e,t,s)=>{"use strict";s.d(t,{BranchProvider:()=>i,O:()=>c,Z:()=>d});var r=s(5155),a=s(2115);let n=(0,a.createContext)(void 0),l=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function i(e){let{children:t}=e,[s,i]=(0,a.useState)(l[0]),[c]=(0,a.useState)(l),[d,o]=(0,a.useState)(!0);return(0,a.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let t=c.find(t=>t.id===e);t&&i(t)}o(!1)},[c]),(0,r.jsx)(n.Provider,{value:{currentBranch:s,branches:c,switchBranch:e=>{let t=c.find(t=>t.id===e);t&&(i(t),localStorage.setItem("selectedBranch",e))},isLoading:d},children:t})}function c(){let e=(0,a.useContext)(n);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,a.useContext)(n)||null}},8145:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var r=s(5155);s(2115);var a=s(2085),n=s(3999);let l=(0,a.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,...a}=e;return(0,r.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...a})}},8482:(e,t,s)=>{"use strict";s.d(t,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>l,aR:()=>i});var r=s(5155),a=s(2115),n=s(3999);let l=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",s),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",s),...a})});i.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",s),...a})});c.displayName="CardTitle";let d=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",s),...a})});d.displayName="CardDescription";let o=a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",s),...a})});o.displayName="CardContent",a.forwardRef((e,t)=>{let{className:s,...a}=e;return(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",s),...a})}).displayName="CardFooter"},9033:(e,t,s)=>{"use strict";s.d(t,{c:()=>a});var r=s(2115);function a(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9367:(e,t,s)=>{"use strict";function r(e,[t,s]){return Math.min(s,Math.max(t,e))}s.d(t,{q:()=>r})},9708:(e,t,s)=>{"use strict";s.d(t,{DX:()=>i,Dc:()=>d,TL:()=>l});var r=s(2115),a=s(6101),n=s(5155);function l(e){let t=function(e){let t=r.forwardRef((e,t)=>{let{children:s,...n}=e;if(r.isValidElement(s)){var l;let e,i,c=(l=s,(i=(e=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.ref:(i=(e=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning)?l.props.ref:l.props.ref||l.ref),d=function(e,t){let s={...t};for(let r in t){let a=e[r],n=t[r];/^on[A-Z]/.test(r)?a&&n?s[r]=(...e)=>{let t=n(...e);return a(...e),t}:a&&(s[r]=a):"style"===r?s[r]={...a,...n}:"className"===r&&(s[r]=[a,n].filter(Boolean).join(" "))}return{...e,...s}}(n,s.props);return s.type!==r.Fragment&&(d.ref=t?(0,a.t)(t,c):c),r.cloneElement(s,d)}return r.Children.count(s)>1?r.Children.only(null):null});return t.displayName=`${e}.SlotClone`,t}(e),s=r.forwardRef((e,s)=>{let{children:a,...l}=e,i=r.Children.toArray(a),c=i.find(o);if(c){let e=c.props.children,a=i.map(t=>t!==c?t:r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null);return(0,n.jsx)(t,{...l,ref:s,children:r.isValidElement(e)?r.cloneElement(e,void 0,a):null})}return(0,n.jsx)(t,{...l,ref:s,children:a})});return s.displayName=`${e}.Slot`,s}var i=l("Slot"),c=Symbol("radix.slottable");function d(e){let t=({children:e})=>(0,n.jsx)(n.Fragment,{children:e});return t.displayName=`${e}.Slottable`,t.__radixId=c,t}function o(e){return r.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===c}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,6874,9526,8441,1684,7358],()=>t(7060)),_N_E=e.O()}]);