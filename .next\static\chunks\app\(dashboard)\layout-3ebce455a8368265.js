(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9305],{426:(e,t,s)=>{Promise.resolve().then(s.bind(s,2871)),Promise.resolve().then(s.bind(s,5248)),Promise.resolve().then(s.bind(s,5038)),Promise.resolve().then(s.bind(s,7705))},2871:(e,t,s)=>{"use strict";s.d(t,{Header:()=>k});var a=s(5155),r=s(2115),n=s(2108),l=s(7168),i=s(8698),o=s(3052),d=s(5196),c=s(9428),m=s(3999);let u=i.bL,x=i.l9;i.YJ,i.ZL,i.Pb,i.z6,r.forwardRef((e,t)=>{let{className:s,inset:r,children:n,...l}=e;return(0,a.jsxs)(i.ZP,{ref:t,className:(0,m.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",r&&"pl-8",s),...l,children:[n,(0,a.jsx)(o.A,{className:"ml-auto h-4 w-4"})]})}).displayName=i.ZP.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.G5,{ref:t,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...r})}).displayName=i.G5.displayName;let f=r.forwardRef((e,t)=>{let{className:s,sideOffset:r=4,...n}=e;return(0,a.jsx)(i.ZL,{children:(0,a.jsx)(i.UC,{ref:t,sideOffset:r,className:(0,m.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",s),...n})})});f.displayName=i.UC.displayName;let h=r.forwardRef((e,t)=>{let{className:s,inset:r,...n}=e;return(0,a.jsx)(i.q7,{ref:t,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r&&"pl-8",s),...n})});h.displayName=i.q7.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,checked:n,...l}=e;return(0,a.jsxs)(i.H_,{ref:t,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),checked:n,...l,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),r]})}).displayName=i.H_.displayName,r.forwardRef((e,t)=>{let{className:s,children:r,...n}=e;return(0,a.jsxs)(i.hN,{ref:t,className:(0,m.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",s),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(c.A,{className:"h-2 w-2 fill-current"})})}),r]})}).displayName=i.hN.displayName;let p=r.forwardRef((e,t)=>{let{className:s,inset:r,...n}=e;return(0,a.jsx)(i.JU,{ref:t,className:(0,m.cn)("px-2 py-1.5 text-sm font-semibold",r&&"pl-8",s),...n})});p.displayName=i.JU.displayName;let g=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(i.wv,{ref:t,className:(0,m.cn)("-mx-1 my-1 h-px bg-muted",s),...r})});g.displayName=i.wv.displayName;var b=s(8145),N=s(3227),y=s(6474),v=s(7705);function j(){let{currentBranch:e,branches:t,switchBranch:s,isLoading:n}=(0,v.O)(),[i,o]=(0,r.useState)(!1);return n?(0,a.jsxs)("div",{className:"flex items-center gap-2 px-3 py-2 bg-gray-100 rounded-md",children:[(0,a.jsx)(N.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"text-sm",children:"Loading..."})]}):(0,a.jsxs)(u,{open:i,onOpenChange:o,children:[(0,a.jsx)(x,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"outline",className:"flex items-center gap-3 min-w-[280px] justify-between bg-white/80 backdrop-blur-sm border-gray-200 hover:bg-white hover:shadow-sm transition-all duration-200 rounded-xl py-3",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-lg bg-blue-50 flex items-center justify-center",children:(0,a.jsx)(N.A,{className:"h-4 w-4 text-blue-600"})}),(0,a.jsxs)("div",{className:"flex flex-col items-start",children:[(0,a.jsx)("span",{className:"font-semibold text-gray-900 text-sm",children:e.name}),(0,a.jsx)(b.E,{variant:"secondary",className:"text-xs font-medium bg-green-100 text-green-700",children:"Active"})]})]}),(0,a.jsx)(y.A,{className:"h-4 w-4 text-gray-400"})]})}),(0,a.jsxs)(f,{align:"start",className:"w-[280px] rounded-xl border-gray-100 shadow-lg",children:[(0,a.jsx)(p,{className:"text-sm font-semibold text-gray-900 px-4 py-3",children:"Switch Branch"}),(0,a.jsx)(g,{className:"bg-gray-100"}),t.map(t=>(0,a.jsx)(h,{onClick:()=>{s(t.id),o(!1)},className:"flex items-center justify-between cursor-pointer px-4 py-3 hover:bg-gray-50 transition-colors duration-200",children:(0,a.jsxs)("div",{className:"flex flex-col",children:[(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"h-6 w-6 rounded-md bg-gray-100 flex items-center justify-center",children:(0,a.jsx)(N.A,{className:"h-3.5 w-3.5 text-gray-600"})}),(0,a.jsx)("span",{className:"font-medium text-gray-900",children:t.name}),e.id===t.id&&(0,a.jsx)(d.A,{className:"h-4 w-4 text-green-600"})]}),t.address&&(0,a.jsx)("span",{className:"text-xs text-gray-500 ml-9 mt-0.5",children:t.address})]})},t.id))]})]})}var A=s(6891),w=s(2915),E=s(8533),T=s(4416),M=s(1284),S=s(3861),R=s(6258);function D(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(0),[i,o]=(0,r.useState)(!1),[d,c]=(0,r.useState)(!1);(0,r.useEffect)(()=>{(async()=>{try{let e=await fetch("/api/notifications/test?action=mock");if(e.ok){let s=await e.json();t(s.notifications),n(s.unreadCount)}else t([]),n(0)}catch(e){console.error("Failed to load notifications:",e),t([]),n(0)}})()},[]);let m=e=>{switch(e){case"success":return(0,a.jsx)(w.A,{className:"h-4 w-4 text-green-500"});case"warning":return(0,a.jsx)(E.A,{className:"h-4 w-4 text-yellow-500"});case"error":return(0,a.jsx)(T.A,{className:"h-4 w-4 text-red-500"});default:return(0,a.jsx)(M.A,{className:"h-4 w-4 text-blue-500"})}},N=e=>{switch(e){case"urgent":return"bg-red-100 text-red-800";case"high":return"bg-orange-100 text-orange-800";case"medium":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}},y=async e=>{t(t=>t.map(t=>t.id===e?{...t,read:!0}:t)),n(e=>Math.max(0,e-1))},v=async()=>{c(!0);try{t(e=>e.map(e=>({...e,read:!0}))),n(0)}catch(e){console.error("Failed to mark all as read:",e)}finally{c(!1)}},j=e=>{e.read||y(e.id),e.actionUrl&&(window.location.href=e.actionUrl),o(!1)};return(0,a.jsxs)(u,{open:i,onOpenChange:o,children:[(0,a.jsx)(x,{asChild:!0,children:(0,a.jsxs)(l.$,{variant:"ghost",size:"icon",className:"relative",children:[(0,a.jsx)(S.A,{className:"h-5 w-5"}),s>0&&(0,a.jsx)("span",{className:"absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center",children:s>9?"9+":s})]})}),(0,a.jsxs)(f,{align:"end",className:"w-80",children:[(0,a.jsxs)(p,{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{children:"Notifications"}),s>0&&(0,a.jsx)(l.$,{variant:"ghost",size:"sm",onClick:v,disabled:d,className:"text-xs",children:"Mark all read"})]}),(0,a.jsx)(g,{}),(0,a.jsx)(A.F,{className:"h-96",children:0===e.length?(0,a.jsxs)("div",{className:"p-4 text-center text-gray-500",children:[(0,a.jsx)(S.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No notifications"})]}):(0,a.jsx)("div",{className:"space-y-1",children:e.map(e=>(0,a.jsx)(h,{className:"p-3 cursor-pointer ".concat(e.read?"":"bg-blue-50"),onClick:()=>j(e),children:(0,a.jsxs)("div",{className:"flex items-start space-x-3 w-full",children:[(0,a.jsx)("div",{className:"flex-shrink-0 mt-1",children:m(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),!e.read&&(0,a.jsx)("div",{className:"h-2 w-2 bg-blue-500 rounded-full flex-shrink-0"})]}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mb-2",children:e.message}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs text-gray-400",children:(0,R.A)(new Date(e.createdAt),{addSuffix:!0})}),"low"!==e.priority&&(0,a.jsx)(b.E,{className:"text-xs ".concat(N(e.priority)),children:e.priority})]})]})]})},e.id))})}),e.length>0&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(g,{}),(0,a.jsx)(h,{className:"text-center text-blue-600 hover:text-blue-800",children:(0,a.jsx)(l.$,{variant:"ghost",size:"sm",className:"w-full",children:"View all notifications"})})]})]})]})}var C=s(7924),I=s(4835);function k(){var e,t,s,i,o,d;let{data:c}=(0,n.useSession)(),[m,u]=(0,r.useState)(!1);return(0,a.jsx)("header",{className:"bg-white/80 backdrop-blur-md shadow-sm border-b border-gray-100",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-8 py-5",children:[(0,a.jsxs)("div",{className:"flex items-center flex-1 gap-6",children:[(0,a.jsx)(j,{}),(0,a.jsxs)("div",{className:"relative max-w-lg w-full",children:[(0,a.jsx)(C.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400"}),(0,a.jsx)("input",{type:"text",placeholder:"Search students, leads, groups...",className:"w-full pl-12 pr-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-gray-50 hover:bg-white transition-all duration-200 text-sm font-medium"})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(D,{}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(l.$,{variant:"ghost",onClick:()=>u(!m),className:"flex items-center space-x-3 hover:bg-gray-100 rounded-xl px-4 py-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full gradient-primary flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-sm font-medium text-white",children:(null==c||null==(t=c.user)||null==(e=t.name)?void 0:e.charAt(0).toUpperCase())||"U"})}),(0,a.jsx)("span",{className:"hidden md:block font-medium text-gray-700",children:(null==c||null==(s=c.user)?void 0:s.name)||"User"})]}),m&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-56 bg-white rounded-xl shadow-lg py-2 z-50 border border-gray-100",children:[(0,a.jsxs)("div",{className:"px-4 py-3 text-sm text-gray-700 border-b border-gray-100",children:[(0,a.jsx)("div",{className:"font-semibold text-gray-900",children:null==c||null==(i=c.user)?void 0:i.name}),(0,a.jsx)("div",{className:"text-gray-500 capitalize",children:null==c||null==(d=c.user)||null==(o=d.role)?void 0:o.toLowerCase()})]}),(0,a.jsxs)("button",{onClick:()=>(0,n.signOut)(),className:"flex items-center w-full px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors duration-200",children:[(0,a.jsx)(I.A,{className:"mr-3 h-4 w-4 text-gray-500"}),"Sign out"]})]})]})]})]})})}},3580:(e,t,s)=>{"use strict";s.d(t,{dj:()=>u});var a=s(2115);let r=0,n=new Map,l=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),c({type:"REMOVE_TOAST",toastId:e})},5e3);n.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?l(s):e.toasts.forEach(e=>{l(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},o=[],d={toasts:[]};function c(e){d=i(d,e),o.forEach(e=>{e(d)})}function m(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||a()}}}),{id:s,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function u(){let[e,t]=a.useState(d);return a.useEffect(()=>(o.push(t),()=>{let e=o.indexOf(t);e>-1&&o.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},3999:(e,t,s)=>{"use strict";s.d(t,{Yq:()=>i,cn:()=>n,r6:()=>o,vv:()=>l});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}function l(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(t)}function o(e){let t="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(t)}},5038:(e,t,s)=>{"use strict";s.d(t,{Toaster:()=>g});var a=s(5155),r=s(2115),n=s(6621),l=s(2085),i=s(4416),o=s(3999);let d=n.Kq,c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.LM,{ref:t,className:(0,o.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",s),...r})});c.displayName=n.LM.displayName;let m=(0,l.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive border-destructive bg-destructive text-destructive-foreground",success:"border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-900 dark:text-green-200",warning:"border-yellow-200 bg-yellow-50 text-yellow-800 dark:border-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",info:"border-blue-200 bg-blue-50 text-blue-800 dark:border-blue-800 dark:bg-blue-900 dark:text-blue-200"}},defaultVariants:{variant:"default"}}),u=r.forwardRef((e,t)=>{let{className:s,variant:r,...l}=e;return(0,a.jsx)(n.bL,{ref:t,className:(0,o.cn)(m({variant:r}),s),...l})});u.displayName=n.bL.displayName,r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.rc,{ref:t,className:(0,o.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",s),...r})}).displayName=n.rc.displayName;let x=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.bm,{ref:t,className:(0,o.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",s),"toast-close":"",...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=n.bm.displayName;let f=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.hE,{ref:t,className:(0,o.cn)("text-sm font-semibold",s),...r})});f.displayName=n.hE.displayName;let h=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)(n.VY,{ref:t,className:(0,o.cn)("text-sm opacity-90",s),...r})});h.displayName=n.VY.displayName;var p=s(3580);function g(){let{toasts:e}=(0,p.dj)();return(0,a.jsxs)(d,{children:[e.map(function(e){let{id:t,title:s,description:r,action:n,...l}=e;return(0,a.jsxs)(u,{...l,children:[(0,a.jsxs)("div",{className:"grid gap-1",children:[s&&(0,a.jsx)(f,{children:s}),r&&(0,a.jsx)(h,{children:r})]}),n,(0,a.jsx)(x,{})]},t)}),(0,a.jsx)(c,{})]})}},5248:(e,t,s)=>{"use strict";s.d(t,{Sidebar:()=>P});var a=s(5155),r=s(6874),n=s.n(r),l=s(5695),i=s(2108),o=s(3999),d=s(3783),c=s(2318),m=s(7580),u=s(5670),x=s(7949),f=s(8623),h=s(1586),p=s(4576),g=s(3109),b=s(9074),N=s(9037),y=s(7434),v=s(1497),j=s(9420),A=s(5525),w=s(9397),E=s(4571),T=s(381),M=s(5040),S=s(6785),R=s(3052),D=s(6474),C=s(2115),I=s(8145);let k=[{name:"Dashboard",roles:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"],items:[{name:"Overview",href:"/dashboard",icon:d.A,roles:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]}]},{name:"Student Management",roles:["ADMIN","MANAGER","TEACHER","RECEPTION"],collapsible:!0,items:[{name:"Leads",href:"/dashboard/leads",icon:c.A,roles:["ADMIN","MANAGER","RECEPTION"]},{name:"Students",href:"/dashboard/students",icon:m.A,roles:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER"]}]},{name:"Academic Management",roles:["ADMIN","MANAGER","TEACHER"],collapsible:!0,items:[{name:"Teachers",href:"/dashboard/teachers",icon:u.A,roles:["ADMIN","MANAGER"]},{name:"Groups",href:"/dashboard/groups",icon:x.A,roles:["ADMIN","MANAGER","TEACHER"]},{name:"Attendance",href:"/dashboard/attendance",icon:u.A,roles:["MANAGER","TEACHER"]},{name:"Assessments",href:"/dashboard/assessments",icon:f.A,roles:["ADMIN","MANAGER","TEACHER","ACADEMIC_MANAGER"]}]},{name:"Financial Management",roles:["ADMIN","CASHIER"],collapsible:!0,items:[{name:"Payments",href:"/dashboard/payments",icon:h.A,roles:["ADMIN","CASHIER"]},{name:"Analytics",href:"/dashboard/analytics",icon:p.A,roles:["ADMIN"]}]},{name:"Student Progress",roles:["STUDENT"],items:[{name:"My Progress",href:"/dashboard/student/progress",icon:g.A,roles:["STUDENT"]},{name:"My Schedule",href:"/dashboard/student/schedule",icon:b.A,roles:["STUDENT"]},{name:"My Attendance",href:"/dashboard/student/attendance",icon:u.A,roles:["STUDENT"]},{name:"My Payments",href:"/dashboard/student/payments",icon:h.A,roles:["STUDENT"]},{name:"Certificates",href:"/dashboard/student/certificates",icon:N.A,roles:["STUDENT"]},{name:"Assignments",href:"/dashboard/student/assignments",icon:y.A,roles:["STUDENT"]}]},{name:"Communication",roles:["ADMIN","MANAGER","TEACHER","STUDENT","ACADEMIC_MANAGER"],items:[{name:"Messages",href:"/dashboard/communication",icon:v.A,roles:["ADMIN","MANAGER","TEACHER","STUDENT","PARENT"]},{name:"Announcements",href:"/dashboard/communication/announcements",icon:j.A,roles:["ADMIN","MANAGER","TEACHER","STUDENT","PARENT"]}]},{name:"Administration",roles:["ADMIN","MANAGER"],collapsible:!0,items:[{name:"Users",href:"/dashboard/users",icon:A.A,roles:["ADMIN"]},{name:"Activity Logs",href:"/dashboard/admin/activity-logs",icon:w.A,roles:["ADMIN"]},{name:"KPI Dashboard",href:"/dashboard/admin/kpis",icon:E.A,roles:["ADMIN","MANAGER"]},{name:"Teacher KPIs",href:"/dashboard/admin/teacher-kpis",icon:g.A,roles:["ADMIN","MANAGER"]},{name:"Settings",href:"/dashboard/settings",icon:T.A,roles:["ADMIN","MANAGER"]}]}],U=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800",O=e=>{let t=["A1","A2","B1","B2"],s=t.indexOf(e);return -1!==s&&s<t.length-1?t[s+1]:null};function P(){var e,t;let s=(0,l.usePathname)(),{data:r}=(0,i.useSession)(),[d,c]=(0,C.useState)([]),m=null==r||null==(e=r.user)?void 0:e.role,u=null==r||null==(t=r.user)?void 0:t.name,x="STUDENT"===m?"B1":null,f=x?O(x):null,h=e=>{c(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},p=m?k.filter(e=>e.roles.includes(m)).map(e=>({...e,items:e.items.filter(e=>e.roles.includes(m))})).filter(e=>e.items.length>0):[];return(0,a.jsxs)("div",{className:"flex flex-col w-72 bg-white shadow-xl h-full border-r border-gray-100",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center h-20 px-6 gradient-primary",children:[(0,a.jsx)(M.A,{className:"h-8 w-8 text-white mr-3"}),(0,a.jsx)("span",{className:"text-xl font-bold text-white tracking-tight",children:"Innovative CRM"})]}),(null==r?void 0:r.user)&&(0,a.jsxs)("div",{className:"px-6 py-5 border-b border-gray-100 bg-white",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full gradient-primary flex items-center justify-center shadow-md",children:(0,a.jsx)("span",{className:"text-sm font-medium text-white",children:null==u?void 0:u.charAt(0).toUpperCase()})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("p",{className:"text-sm font-semibold text-gray-900 truncate",children:u}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 mt-0.5",children:[(0,a.jsx)("p",{className:"text-xs text-gray-500 capitalize font-medium",children:null==m?void 0:m.toLowerCase()}),x&&(0,a.jsxs)(I.E,{className:(0,o.cn)("text-xs font-medium",U(x)),children:["Level ",x]})]})]})]}),x&&f&&(0,a.jsxs)("div",{className:"mt-4 p-3 bg-white rounded-lg border border-gray-100 shadow-sm",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-gray-700 font-medium",children:["Progress to ",f]}),(0,a.jsx)(S.A,{className:"h-3.5 w-3.5 text-blue-600"})]}),(0,a.jsx)("div",{className:"mt-2 w-full bg-gray-100 rounded-full h-2",children:(0,a.jsx)("div",{className:"gradient-primary h-2 rounded-full",style:{width:"65%"}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-600 font-medium mt-1.5 flex items-center",children:[(0,a.jsx)(g.A,{className:"h-3 w-3 mr-1 text-blue-600"}),"65% Complete"]})]})]}),(0,a.jsx)("nav",{className:"flex-1 px-6 py-6 space-y-2 overflow-y-auto",children:p.map(e=>{let t=d.includes(e.name),r=!e.collapsible||!t;return(0,a.jsxs)("div",{className:"space-y-1.5",children:[(0,a.jsx)("div",{className:"flex items-center justify-between",children:e.collapsible?(0,a.jsxs)("button",{onClick:()=>h(e.name),className:"flex items-center w-full px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider hover:text-gray-700 transition-all duration-200 rounded-lg hover:bg-gray-50",children:[(0,a.jsx)("span",{className:"flex-1 text-left",children:e.name}),t?(0,a.jsx)(R.A,{className:"h-3.5 w-3.5 text-gray-400"}):(0,a.jsx)(D.A,{className:"h-3.5 w-3.5 text-gray-400"})]}):(0,a.jsx)("h3",{className:"px-3 py-2 text-xs font-bold text-gray-500 uppercase tracking-wider",children:e.name})}),r&&(0,a.jsx)("div",{className:"space-y-1",children:e.items.map(e=>{let t=s===e.href;return(0,a.jsxs)(n(),{href:e.href,className:(0,o.cn)("flex items-center px-4 py-3 text-sm font-medium rounded-xl transition-all duration-200 group",t?"bg-blue-50 text-blue-700 border-r-4 border-blue-600 shadow-sm":"text-gray-600 hover:bg-gray-50 hover:text-gray-900 hover:shadow-sm"),children:[(0,a.jsx)(e.icon,{className:(0,o.cn)("mr-3 h-5 w-5 transition-colors duration-200",t?"text-blue-600":"text-gray-400 group-hover:text-gray-600")}),(0,a.jsx)("span",{className:"truncate",children:e.name})]},e.name)})}),e!==p[p.length-1]&&(0,a.jsx)("div",{className:"border-b border-gray-100 my-2"})]},e.name)})})]})}},6891:(e,t,s)=>{"use strict";s.d(t,{F:()=>i});var a=s(5155),r=s(2115),n=s(7655),l=s(3999);let i=r.forwardRef((e,t)=>{let{className:s,children:r,...i}=e;return(0,a.jsxs)(n.bL,{ref:t,className:(0,l.cn)("relative overflow-hidden",s),...i,children:[(0,a.jsx)(n.LM,{className:"h-full w-full rounded-[inherit]",children:r}),(0,a.jsx)(o,{}),(0,a.jsx)(n.OK,{})]})});i.displayName=n.bL.displayName;let o=r.forwardRef((e,t)=>{let{className:s,orientation:r="vertical",...i}=e;return(0,a.jsx)(n.VM,{ref:t,orientation:r,className:(0,l.cn)("flex touch-none select-none transition-colors","vertical"===r&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===r&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",s),...i,children:(0,a.jsx)(n.lr,{className:"relative flex-1 rounded-full bg-border"})})});o.displayName=n.VM.displayName},7168:(e,t,s)=>{"use strict";s.d(t,{$:()=>d,r:()=>o});var a=s(5155),r=s(2115),n=s(9708),l=s(2085),i=s(3999);let o=(0,l.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,t)=>{let{className:s,variant:r,size:l,asChild:d=!1,...c}=e,m=d?n.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(o({variant:r,size:l,className:s})),ref:t,...c})});d.displayName="Button"},7705:(e,t,s)=>{"use strict";s.d(t,{BranchProvider:()=>i,O:()=>o,Z:()=>d});var a=s(5155),r=s(2115);let n=(0,r.createContext)(void 0),l=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function i(e){let{children:t}=e,[s,i]=(0,r.useState)(l[0]),[o]=(0,r.useState)(l),[d,c]=(0,r.useState)(!0);return(0,r.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let t=o.find(t=>t.id===e);t&&i(t)}c(!1)},[o]),(0,a.jsx)(n.Provider,{value:{currentBranch:s,branches:o,switchBranch:e=>{let t=o.find(t=>t.id===e);t&&(i(t),localStorage.setItem("selectedBranch",e))},isLoading:d},children:t})}function o(){let e=(0,r.useContext)(n);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,r.useContext)(n)||null}},8145:(e,t,s)=>{"use strict";s.d(t,{E:()=>i});var a=s(5155);s(2115);var r=s(2085),n=s(3999);let l=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,n.cn)(l({variant:s}),t),...r})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5003,6221,6874,2108,9526,2198,8441,1684,7358],()=>t(426)),_N_E=e.O()}]);