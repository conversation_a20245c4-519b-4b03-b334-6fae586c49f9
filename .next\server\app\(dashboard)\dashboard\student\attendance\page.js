(()=>{var e={};e.id=5513,e.ids=[5513],e.modules={3018:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>i,TN:()=>c});var r=t(60687),a=t(43210),n=t(24224),l=t(96241);let d=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),i=a.forwardRef(({className:e,variant:s,...t},a)=>(0,r.jsx)("div",{ref:a,role:"alert",className:(0,l.cn)(d({variant:s}),e),...t}));i.displayName="Alert",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...s}));c.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7568:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>N});var r=t(60687),a=t(43210),n=t(55192),l=t(59821),d=t(68988),i=t(96752),c=t(3018),o=t(96241),x=t(23689),m=t(83281),u=t(48730),h=t(63851),p=t(72730),f=t(25541),j=t(99270),b=t(40228),g=t(82136);function N(){let{data:e}=(0,g.useSession)(),[s,t]=(0,a.useState)([]),[N,v]=(0,a.useState)(!0),[y,w]=(0,a.useState)(""),[A,C]=(0,a.useState)("ALL"),[E,k]=(0,a.useState)(null);(0,a.useCallback)(async()=>{try{v(!0);let s=`/api/students/${e?.user?.id}/attendance?limit=100`;"ALL"!==A&&(s+=`&status=${A}`);let r=await fetch(s),a=await r.json();t(a.attendances||[]),k(null)}catch(e){console.error("Error fetching attendances:",e),k("Failed to fetch attendance records")}finally{v(!1)}},[e?.user?.id,A]);let R=s.filter(e=>e.class.group.name.toLowerCase().includes(y.toLowerCase())||e.class.group.course.name.toLowerCase().includes(y.toLowerCase())||e.class.teacher.user.name.toLowerCase().includes(y.toLowerCase())||e.class.topic&&e.class.topic.toLowerCase().includes(y.toLowerCase())),T=e=>{switch(e){case"PRESENT":return(0,r.jsx)(x.A,{className:"h-4 w-4 text-green-600"});case"ABSENT":return(0,r.jsx)(m.A,{className:"h-4 w-4 text-red-600"});case"LATE":return(0,r.jsx)(u.A,{className:"h-4 w-4 text-yellow-600"});case"EXCUSED":return(0,r.jsx)(h.A,{className:"h-4 w-4 text-blue-600"});default:return null}},L=e=>{switch(e){case"PRESENT":return"bg-green-100 text-green-800";case"ABSENT":return"bg-red-100 text-red-800";case"LATE":return"bg-yellow-100 text-yellow-800";case"EXCUSED":return"bg-blue-100 text-blue-800";default:return"bg-gray-100 text-gray-800"}},P=s.length,S=s.filter(e=>"PRESENT"===e.status).length,_=s.filter(e=>"LATE"===e.status).length,D=s.filter(e=>"ABSENT"===e.status).length,W=s.filter(e=>"EXCUSED"===e.status).length,B=P>0?Math.round(S/P*100):0;return N?(0,r.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,r.jsx)(p.A,{className:"h-8 w-8 animate-spin"}),(0,r.jsx)("span",{className:"ml-2",children:"Loading attendance records..."})]}):(0,r.jsxs)("div",{className:"space-y-6",children:[E&&(0,r.jsx)(c.Fc,{variant:"destructive",children:(0,r.jsx)(c.TN,{children:E})}),(0,r.jsx)("div",{className:"flex justify-between items-center",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"My Attendance"}),(0,r.jsx)("p",{className:"text-gray-600",children:"View your class attendance history"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(f.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Attendance Rate"}),(0,r.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[B,"%"]})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(x.A,{className:"h-8 w-8 text-green-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Present"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:S})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(u.A,{className:"h-8 w-8 text-yellow-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Late"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:_})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(m.A,{className:"h-8 w-8 text-red-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Absent"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:D})]})]})})}),(0,r.jsx)(n.Zp,{children:(0,r.jsx)(n.Wu,{className:"p-6",children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(h.A,{className:"h-8 w-8 text-blue-600"}),(0,r.jsxs)("div",{className:"ml-4",children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-600",children:"Excused"}),(0,r.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:W})]})]})})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsx)(n.aR,{children:(0,r.jsx)(n.ZB,{children:"Filter Attendance"})}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,r.jsx)(d.p,{placeholder:"Search by group, course, teacher, or topic...",value:y,onChange:e=>w(e.target.value),className:"pl-10"})]})}),(0,r.jsx)("div",{className:"flex gap-2",children:["ALL","PRESENT","ABSENT","LATE","EXCUSED"].map(e=>(0,r.jsx)("button",{onClick:()=>C(e),className:`px-3 py-2 text-sm rounded-md transition-colors ${A===e?"bg-blue-100 text-blue-700 border border-blue-300":"bg-gray-100 text-gray-700 hover:bg-gray-200"}`,children:"ALL"===e?"All":e.charAt(0)+e.slice(1).toLowerCase()},e))})]})})]}),(0,r.jsxs)(n.Zp,{children:[(0,r.jsxs)(n.aR,{children:[(0,r.jsxs)(n.ZB,{children:["Attendance History (",R.length,")"]}),(0,r.jsx)(n.BT,{children:"Your complete attendance record"})]}),(0,r.jsx)(n.Wu,{children:(0,r.jsxs)(i.XI,{children:[(0,r.jsx)(i.A0,{children:(0,r.jsxs)(i.Hj,{children:[(0,r.jsx)(i.nd,{children:"Date"}),(0,r.jsx)(i.nd,{children:"Group"}),(0,r.jsx)(i.nd,{children:"Course"}),(0,r.jsx)(i.nd,{children:"Teacher"}),(0,r.jsx)(i.nd,{children:"Topic"}),(0,r.jsx)(i.nd,{children:"Status"}),(0,r.jsx)(i.nd,{children:"Notes"})]})}),(0,r.jsx)(i.BF,{children:0===R.length?(0,r.jsx)(i.Hj,{children:(0,r.jsx)(i.nA,{colSpan:7,className:"text-center py-8",children:"No attendance records found"})}):R.map(e=>(0,r.jsxs)(i.Hj,{children:[(0,r.jsx)(i.nA,{children:(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)(b.A,{className:"h-4 w-4 text-gray-400 mr-2"}),(0,o.Yq)(new Date(e.class.date))]})}),(0,r.jsx)(i.nA,{children:(0,r.jsx)(l.E,{variant:"outline",children:e.class.group.name})}),(0,r.jsx)(i.nA,{children:e.class.group.course.name}),(0,r.jsx)(i.nA,{children:e.class.teacher.user.name}),(0,r.jsx)(i.nA,{children:e.class.topic||"N/A"}),(0,r.jsx)(i.nA,{children:(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[T(e.status),(0,r.jsx)(l.E,{className:L(e.status),children:e.status})]})}),(0,r.jsx)(i.nA,{children:e.notes||"N/A"})]},e.id))})]})})]})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19793:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});let r=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\student\\\\attendance\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\attendance\\page.tsx","default")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},49782:(e,s,t)=>{Promise.resolve().then(t.bind(t,19793))},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>l,aR:()=>d});var r=t(60687),a=t(43210),n=t(96241);let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));l.displayName="Card";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},68988:(e,s,t)=>{"use strict";t.d(s,{p:()=>l});var r=t(60687),a=t(43210),n=t(96241);let l=a.forwardRef(({className:e,type:s,...t},a)=>(0,r.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:a,...t}));l.displayName="Input"},72730:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},83281:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});let r=(0,t(18962).A)("XCircle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},86471:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var r=t(65239),a=t(48088),n=t(88170),l=t.n(n),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(s,i);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["attendance",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,19793)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\attendance\\page.tsx"]}]},{}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\attendance\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/attendance/page",pathname:"/dashboard/student/attendance",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},96638:(e,s,t)=>{Promise.resolve().then(t.bind(t,7568))},96752:(e,s,t)=>{"use strict";t.d(s,{A0:()=>d,BF:()=>i,Hj:()=>c,XI:()=>l,nA:()=>x,nd:()=>o});var r=t(60687),a=t(43210),n=t(96241);let l=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let d=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",e),...s}));d.displayName="TableHeader";let i=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...s}));i.displayName="TableBody",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let c=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));c.displayName="TableRow";let o=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));o.displayName="TableHead";let x=a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));x.displayName="TableCell",a.forwardRef(({className:e,...s},t)=>(0,r.jsx)("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"}};var s=require("../../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[4243,7615,2918,8887,3039],()=>t(86471));module.exports=r})();