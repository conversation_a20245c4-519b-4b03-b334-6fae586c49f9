"use strict";(()=>{var e={};e.id=5167,e.ids=[5167],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41420:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>v,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>y});var s={};t.r(s),t.d(s,{GET:()=>m,POST:()=>g});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),u=t(19854),l=t(41098),c=t(79464),p=t(99326),d=t(45697);let h=d.Ik({groupId:d.Yj().min(1,"Group ID is required"),notes:d.Yj().optional()});async function g(e,{params:r}){try{let{id:t}=await r,s=await (0,u.getServerSession)(l.N);if(!s?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let n=await e.json(),a=h.parse(n),o=await c.z.lead.findUnique({where:{id:t}});if(!o)return i.NextResponse.json({error:"Lead not found"},{status:404});if("CALL_COMPLETED"!==o.status)return i.NextResponse.json({error:"Lead must be in CALL_COMPLETED status to assign to group"},{status:400});let d=await c.z.group.findUnique({where:{id:a.groupId},include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}},_count:{select:{enrollments:!0}}}});if(!d)return i.NextResponse.json({error:"Group not found"},{status:404});if(!d.isActive)return i.NextResponse.json({error:"Group is not active"},{status:400});if(d._count.enrollments>=d.capacity)return i.NextResponse.json({error:"Group is at full capacity"},{status:400});let g=new Date,m=await c.z.lead.update({where:{id:t},data:{status:"GROUP_ASSIGNED",assignedGroupId:a.groupId,assignedTeacherId:d.teacherId,assignedAt:g,notes:a.notes||o.notes,updatedAt:g},include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},assignedTeacher:{include:{user:{select:{name:!0}}}}}});return await p._.logLeadContacted(s.user.id,s.user.role,o.id,{leadName:o.name,leadPhone:o.phone,previousStatus:o.status,newStatus:"GROUP_ASSIGNED",notes:`Assigned to group: ${d.name} (${d.course.name} - ${d.course.level}) with teacher: ${d.teacher.user.name}`},e),i.NextResponse.json({lead:m,group:d})}catch(e){if(e instanceof d.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error assigning group to lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function m(e,{params:r}){try{let{searchParams:r}=new URL(e.url),t=r.get("teacher"),s=r.get("level"),n=r.get("search"),a=r.get("branch")||"main",o={isActive:!0,branch:"main"===a?"Main Branch":"Branch"};t&&(o.teacher={user:{name:{contains:t,mode:"insensitive"}}}),s&&(o.course={level:s}),n&&(o.OR=[{name:{contains:n,mode:"insensitive"}},{course:{name:{contains:n,mode:"insensitive"}}},{teacher:{user:{name:{contains:n,mode:"insensitive"}}}}]);let u=(await c.z.group.findMany({where:o,include:{course:{select:{name:!0,level:!0}},teacher:{select:{id:!0,tier:!0,subject:!0,user:{select:{name:!0}}}},_count:{select:{enrollments:!0}}},orderBy:[{startDate:"asc"},{name:"asc"}]})).filter(e=>e._count.enrollments<e.capacity),l={A_LEVEL:1,B_LEVEL:2,C_LEVEL:3,NEW:4},p=e=>{try{let r=e;try{let t=JSON.parse(e);r=Array.isArray(t)?t.join(" "):e}catch{r=e}let t=r.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/),s=t?`${t[1]}:${t[2]}-${t[3]}:${t[4]}`:"Unknown",n=r.toLowerCase(),a=n.includes("monday")&&n.includes("wednesday")&&n.includes("friday")?"MWF":n.includes("tuesday")&&n.includes("thursday")&&n.includes("saturday")?"TTS":"Other";return{time:s,days:a}}catch(r){return console.error("Error parsing schedule:",r,"Schedule:",e),{time:"Unknown",days:"Unknown"}}},d=u.reduce((e,r)=>{let{time:t,days:s}=p(r.schedule),n=`${r.course.level}-${s}-${t}`;return e[n]||(e[n]={courseLevel:r.course.level,days:s,time:t,groups:[]}),e[n].groups.push(r),e},{}),h=[],g=[];Object.entries(d).forEach(([e,r])=>{let t=r.groups.reduce((e,r)=>{let t=r.teacher.tier||"NEW";return e[t]||(e[t]=[]),e[t].push(r),e},{}),s=Object.entries(t).map(([e,r])=>{let t=r.reduce((e,r)=>e+r.capacity,0),s=r.reduce((e,r)=>e+r._count.enrollments,0);return{tier:e,groups:r,utilizationRate:t>0?s/t*100:0,priority:l[e]||4}}).sort((e,r)=>e.priority-r.priority),n=[];for(let e of s)if("A_LEVEL"===e.tier)n.push(...e.groups);else{let r=s.filter(r=>r.priority<e.priority);(r.every(e=>e.utilizationRate>=80)||0===r.length)&&n.push(...e.groups)}h.push(...n),g.push({slotKey:e,courseLevel:r.courseLevel,days:r.days,time:r.time,tierUtilization:s.map(e=>({tier:e.tier,utilizationRate:Math.round(100*e.utilizationRate)/100,groupCount:e.groups.length,availableGroups:n.filter(r=>r.teacher.tier===e.tier).length})),totalAvailable:n.length})});let m=h.sort((e,r)=>{let t=l[e.teacher.tier||"NEW"]||4,s=l[r.teacher.tier||"NEW"]||4;return t-s});return i.NextResponse.json({groups:m,slotAnalysis:g})}catch(e){return console.error("Error fetching available groups:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let v=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/leads/[id]/assign-group/route",pathname:"/api/leads/[id]/assign-group",filename:"route",bundlePath:"app/api/leads/[id]/assign-group/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\leads\\[id]\\assign-group\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:y,serverHooks:f}=v;function w(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:y})}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(41420));module.exports=s})();