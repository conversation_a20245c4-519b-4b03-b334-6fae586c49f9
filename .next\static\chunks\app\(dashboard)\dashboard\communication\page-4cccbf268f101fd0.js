(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8750],{3999:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>l,cn:()=>i,r6:()=>c,vv:()=>n});var a=t(2596),r=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function l(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s)}function c(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}},4964:(e,s,t)=>{"use strict";t.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>l});var a=t(5155),r=t(2115),i=t(704),n=t(3999);let l=i.bL,c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});c.displayName=i.B8.displayName;let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});d.displayName=i.l9.displayName;let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});o.displayName=i.UC.displayName},5784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>p,gC:()=>h,l6:()=>o,yv:()=>m});var a=t(5155),r=t(2115),i=t(1992),n=t(6474),l=t(7863),c=t(5196),d=t(3999);let o=i.bL;i.YJ;let m=i.WT,u=r.forwardRef((e,s)=>{let{className:t,children:r,...l}=e;return(0,a.jsxs)(i.l9,{ref:s,className:(0,d.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...l,children:[r,(0,a.jsx)(i.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=i.l9.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.PP,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(l.A,{className:"h-4 w-4"})})});x.displayName=i.PP.displayName;let f=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.wn,{ref:s,className:(0,d.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});f.displayName=i.wn.displayName;let h=r.forwardRef((e,s)=>{let{className:t,children:r,position:n="popper",...l}=e;return(0,a.jsx)(i.ZL,{children:(0,a.jsxs)(i.UC,{ref:s,className:(0,d.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...l,children:[(0,a.jsx)(x,{}),(0,a.jsx)(i.LM,{className:(0,d.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(f,{})]})})});h.displayName=i.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.JU,{ref:s,className:(0,d.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=i.JU.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(i.q7,{ref:s,className:(0,d.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(i.VF,{children:(0,a.jsx)(c.A,{className:"h-4 w-4"})})}),(0,a.jsx)(i.p4,{children:r})]})});p.displayName=i.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(i.wv,{ref:s,className:(0,d.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=i.wv.displayName},6476:(e,s,t)=>{Promise.resolve().then(t.bind(t,7189))},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>d,r:()=>c});var a=t(5155),r=t(2115),i=t(9708),n=t(2085),l=t(3999);let c=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:d=!1,...o}=e,m=d?i.DX:"button";return(0,a.jsx)(m,{className:(0,l.cn)(c({variant:r,size:n,className:t})),ref:s,...o})});d.displayName="Button"},7189:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var a=t(5155),r=t(2115),i=t(8482),n=t(7168),l=t(8145),c=t(5784),d=t(4964),o=t(9026),m=t(1497),u=t(9420),x=t(8883),f=t(3861),h=t(2486),p=t(7580),j=t(1539),v=t(2915),g=t(311),N=t(381);function b(){let[e,s]=(0,r.useState)("sms"),[t,b]=(0,r.useState)(""),[y,w]=(0,r.useState)(""),[S,k]=(0,r.useState)([]),[A,C]=(0,r.useState)({sms:!1,email:!1}),[R,T]=(0,r.useState)(!1),[E,M]=(0,r.useState)(""),[B,Z]=(0,r.useState)("reminder"),[W,z]=(0,r.useState)("medium"),[U,D]=(0,r.useState)(["email"]),[F,P]=(0,r.useState)([{label:"SMS Sent Today",value:"0",icon:m.A,color:"text-blue-600"},{label:"Calls Made",value:"0",icon:u.A,color:"text-green-600"},{label:"Emails Sent",value:"0",icon:x.A,color:"text-purple-600"},{label:"Active Notifications",value:"0",icon:f.A,color:"text-orange-600"}]);(0,r.useEffect)(()=>{I(),L(),$()},[]);let $=async()=>{try{let e=await fetch("/api/communication/stats?period=today");if(e.ok){let s=await e.json();P([{label:"SMS Sent Today",value:s.stats.sms.total.toString(),icon:m.A,color:"text-blue-600"},{label:"Calls Made",value:s.stats.calls.total.toString(),icon:u.A,color:"text-green-600"},{label:"Emails Sent",value:s.stats.email.total.toString(),icon:x.A,color:"text-purple-600"},{label:"Active Notifications",value:s.stats.notifications.active.toString(),icon:f.A,color:"text-orange-600"}])}}catch(e){console.error("Error fetching communication stats:",e)}},I=async()=>{try{let e=await fetch("/api/workflows?action=list");if(e.ok){let s=await e.json();k(s.workflows)}}catch(e){console.error("Error fetching workflows:",e)}},L=async()=>{try{let e=await fetch("/api/notifications?action=test");if(e.ok){let s=await e.json();C({sms:s.sms.available,email:s.email.available})}}catch(e){console.error("Error fetching service status:",e)}},Y=async(e,s)=>{try{(await fetch("/api/workflows",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({workflowId:e,enabled:s})})).ok&&(I(),M("Workflow ".concat(s?"enabled":"disabled"," successfully")))}catch(e){M("Failed to update workflow")}};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Communication Center"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Send messages, manage workflows, and configure notifications"})]})}),E&&(0,a.jsx)(o.Fc,{children:(0,a.jsx)(o.TN,{children:E})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:F.map((e,s)=>(0,a.jsx)(i.Zp,{children:(0,a.jsx)(i.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(e.icon,{className:"h-8 w-8 ".concat(e.color)}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600",children:e.label}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:e.value})]})]})})},s))}),(0,a.jsxs)(d.tU,{defaultValue:"send",className:"space-y-6",children:[(0,a.jsxs)(d.j7,{children:[(0,a.jsx)(d.Xi,{value:"send",children:"Send Messages"}),(0,a.jsx)(d.Xi,{value:"workflows",children:"Workflows"}),(0,a.jsx)(d.Xi,{value:"settings",children:"Settings"})]}),(0,a.jsx)(d.av,{value:"send",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"lg:col-span-2",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Send Message"}),(0,a.jsx)(i.BT,{children:"Compose and send messages to students or groups"})]}),(0,a.jsxs)(i.Wu,{className:"space-y-4",children:[(0,a.jsx)("div",{className:"flex space-x-2",children:["sms","whatsapp","email","notification"].map(t=>(0,a.jsx)(n.$,{variant:e===t?"default":"outline",onClick:()=>s(t),size:"sm",children:t.toUpperCase()},t))}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Recipient"}),(0,a.jsxs)(c.l6,{value:y,onValueChange:w,children:[(0,a.jsx)(c.bq,{children:(0,a.jsx)(c.yv,{placeholder:"Select recipient"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"all-students",children:"All Students"}),(0,a.jsx)(c.eb,{value:"group-a1",children:"Group A1 - Morning"}),(0,a.jsx)(c.eb,{value:"group-b1",children:"Group B1 - Evening"}),(0,a.jsx)(c.eb,{value:"ielts-students",children:"IELTS Students"}),(0,a.jsx)(c.eb,{value:"individual",children:"Individual Student"})]})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Message"}),(0,a.jsx)("textarea",{className:"w-full p-3 border border-gray-300 rounded-md resize-none",rows:4,placeholder:"Type your message here...",value:t,onChange:e=>b(e.target.value)}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:[t.length,"/160 characters"]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"checkbox",id:"schedule",className:"rounded"}),(0,a.jsx)("label",{htmlFor:"schedule",className:"text-sm",children:"Schedule for later"})]}),(0,a.jsxs)(n.$,{className:"flex items-center space-x-2",children:[(0,a.jsx)(h.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Send Message"})]})]})]})]}),(0,a.jsxs)(i.Zp,{className:"mt-6",children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Recent Messages"}),(0,a.jsx)(i.BT,{children:"Latest sent messages and their status"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:[{id:1,type:"SMS",recipient:"Aziza Karimova",message:"Reminder: Your IELTS class starts tomorrow at 10:00 AM",status:"Delivered",timestamp:"2 hours ago"},{id:2,type:"WhatsApp",recipient:"Bobur Toshev",message:"Your payment has been received. Thank you!",status:"Read",timestamp:"4 hours ago"},{id:3,type:"SMS",recipient:"Group A1-Morning",message:"Class moved to Room 205 today",status:"Delivered",timestamp:"6 hours ago"}].map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 border rounded-lg",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"h-4 w-4 text-blue-600"})})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.recipient}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{variant:"outline",children:e.type}),(0,a.jsx)(l.E,{className:"Delivered"===e.status?"bg-green-100 text-green-800":"Read"===e.status?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800",children:e.status})]})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-1",children:e.message}),(0,a.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:e.timestamp})]})]},e.id))})})]})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Message Templates"}),(0,a.jsx)(i.BT,{children:"Quick templates for common messages"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-3",children:[{id:1,name:"Class Reminder",content:"Reminder: Your {course} class starts {time} at {location}"},{id:2,name:"Payment Confirmation",content:"Your payment of {amount} has been received. Thank you!"},{id:3,name:"Welcome Message",content:"Welcome to Innovative Centre! Your journey to English mastery begins now."},{id:4,name:"Assignment Reminder",content:"Don't forget to complete your homework for tomorrow's class."}].map(e=>(0,a.jsxs)("div",{className:"p-3 border rounded-lg hover:bg-gray-50 cursor-pointer",children:[(0,a.jsx)("h4",{className:"font-medium text-sm",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-600 mt-1",children:e.content}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",className:"mt-2",onClick:()=>b(e.content),children:"Use Template"})]},e.id))})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Quick Actions"}),(0,a.jsx)(i.BT,{children:"Common communication tasks"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Send Class Reminder"]}),(0,a.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Payment Reminder"]}),(0,a.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Welcome New Students"]}),(0,a.jsxs)(n.$,{variant:"outline",className:"w-full justify-start",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Schedule Follow-up"]})]})})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Settings"}),(0,a.jsx)(i.BT,{children:"Communication preferences"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"SMS Notifications"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Email Notifications"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"WhatsApp Integration"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Auto Reminders"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]})})]})]})]})}),(0,a.jsx)(d.av,{value:"workflows",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Automated Workflows"}),(0,a.jsx)(i.BT,{children:"Manage automated business processes and notifications"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:S.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border rounded-lg",children:[(0,a.jsx)("div",{className:"flex-1",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(j.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.description}),(0,a.jsx)("div",{className:"mt-1",children:(0,a.jsx)(l.E,{variant:"outline",size:"sm",children:e.trigger.event})})]})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(l.E,{variant:e.enabled?"default":"secondary",children:e.enabled?"Enabled":"Disabled"}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>Y(e.id,!e.enabled),children:e.enabled?"Disable":"Enable"})]})]},e.id))})})]})}),(0,a.jsx)(d.av,{value:"settings",children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)(i.aR,{children:[(0,a.jsx)(i.ZB,{children:"Communication Settings"}),(0,a.jsx)(i.BT,{children:"Configure SMS and email service providers"})]}),(0,a.jsx)(i.Wu,{children:(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Service Status"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(u.A,{className:"h-5 w-5 text-blue-600"}),(0,a.jsx)("span",{children:"SMS Service"})]}),A.sms?(0,a.jsx)(v.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(g.A,{className:"h-5 w-5 text-red-600"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.A,{className:"h-5 w-5 text-green-600"}),(0,a.jsx)("span",{children:"Email Service"})]}),A.email?(0,a.jsx)(v.A,{className:"h-5 w-5 text-green-600"}):(0,a.jsx)(g.A,{className:"h-5 w-5 text-red-600"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium mb-4",children:"Notification Preferences"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"SMS Notifications"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Email Notifications"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Auto Reminders"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm",children:"Payment Alerts"}),(0,a.jsx)("input",{type:"checkbox",defaultChecked:!0,className:"rounded"})]})]})]}),(0,a.jsxs)(n.$,{children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Save Settings"]})]})})]})})]})]})}},8145:(e,s,t)=>{"use strict";t.d(s,{E:()=>l});var a=t(5155);t(2115);var r=t(2085),i=t(3999);let n=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:t}),s),...r})}},8482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>d,Wu:()=>o,ZB:()=>c,Zp:()=>n,aR:()=>l});var a=t(5155),r=t(2115),i=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...r})});n.displayName="Card";let l=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",t),...r})});l.displayName="CardHeader";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});c.displayName="CardTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});d.displayName="CardDescription";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("p-6 pt-0",t),...r})});o.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,i.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},9026:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>c,TN:()=>d});var a=t(5155),r=t(2115),i=t(2085),n=t(3999);let l=(0,i.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=r.forwardRef((e,s)=>{let{className:t,variant:r,...i}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(l({variant:r}),t),...i})});c.displayName="Alert",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});d.displayName="AlertDescription"}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,5027,8441,1684,7358],()=>s(6476)),_N_E=e.O()}]);