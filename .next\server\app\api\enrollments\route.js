(()=>{var e={};e.id=5229,e.ids=[5229],e.modules={3039:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>h});var n={};r.r(n),r.d(n,{GET:()=>c,POST:()=>p});var s=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(79464),d=r(45697);let l=d.Ik({studentId:d.Yj(),groupId:d.Yj(),status:d.k5(["ACTIVE","COMPLETED","DROPPED","SUSPENDED"]).default("ACTIVE"),startDate:d.Yj(),endDate:d.Yj().optional()});async function c(e){try{let{searchParams:t}=new URL(e.url),r=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"10"),s=t.get("search"),a=t.get("status"),o=t.get("groupId"),d=t.get("studentId"),l=t.get("branch")||"main",c="main"===l?"Main Branch":"Branch",p={AND:[{student:{branch:c}},{group:{branch:c}}]};s&&(p.OR=[{student:{branch:l,user:{name:{contains:s,mode:"insensitive"}}}},{student:{branch:l,user:{phone:{contains:s}}}},{group:{branch:l,name:{contains:s,mode:"insensitive"}}},{group:{branch:l,course:{name:{contains:s,mode:"insensitive"}}}}]),a&&(p.status=a),o&&(p.groupId=o,p.AND.push({group:{id:o,branch:l}})),d&&(p.studentId=d,p.AND.push({student:{id:d,branch:l}}));let[m,g]=await Promise.all([u.z.enrollment.findMany({where:p,include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},group:{include:{course:{select:{id:!0,name:!0,level:!0,duration:!0,price:!0}},teacher:{include:{user:{select:{name:!0}}}}}}},orderBy:{createdAt:"desc"},skip:(r-1)*n,take:n}),u.z.enrollment.count({where:p})]);return i.NextResponse.json({enrollments:m,pagination:{page:r,limit:n,total:g,pages:Math.ceil(g/n)}})}catch(e){return console.error("Error fetching enrollments:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e){try{let t=await e.json(),r=l.parse(t);if(!await u.z.student.findUnique({where:{id:r.studentId}}))return i.NextResponse.json({error:"Student not found"},{status:400});let n=await u.z.group.findUnique({where:{id:r.groupId},include:{_count:{select:{enrollments:{where:{status:"ACTIVE"}}}}}});if(!n)return i.NextResponse.json({error:"Group not found"},{status:400});if(!n.isActive)return i.NextResponse.json({error:"Cannot enroll in inactive group"},{status:400});if(n._count.enrollments>=n.capacity)return i.NextResponse.json({error:"Group is at full capacity"},{status:400});if(await u.z.enrollment.findUnique({where:{studentId_groupId:{studentId:r.studentId,groupId:r.groupId}}}))return i.NextResponse.json({error:"Student is already enrolled in this group"},{status:400});let s=await u.z.enrollment.create({data:{...r,startDate:new Date(r.startDate),endDate:r.endDate?new Date(r.endDate):null},include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},group:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}}});return i.NextResponse.json(s,{status:201})}catch(e){if(e instanceof d.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating enrollment:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/enrollments/route",pathname:"/api/enrollments",filename:"route",bundlePath:"app/api/enrollments/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\enrollments\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:g,workUnitAsyncStorage:h,serverHooks:x}=m;function f(){return(0,o.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:h})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>s});var n=r(96330);let s=globalThis.prisma??new n.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[4243,580,5697],()=>r(3039));module.exports=n})();