(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7898],{712:(e,s,t)=>{Promise.resolve().then(t.bind(t,8054))},809:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("AlertTriangle",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z",key:"c3ski4"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1788:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2318:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("UserPlus",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"19",x2:"19",y1:"8",y2:"14",key:"1bvyxn"}],["line",{x1:"22",x2:"16",y1:"11",y2:"11",key:"1shjgl"}]])},2525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2657:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},2714:(e,s,t)=>{"use strict";t.d(s,{J:()=>o});var a=t(5155),r=t(2115),l=t(968),n=t(2085),i=t(3999);let d=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.b,{ref:s,className:(0,i.cn)(d(),t),...r})});o.displayName=l.b.displayName},2915:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},3580:(e,s,t)=>{"use strict";t.d(s,{dj:()=>u});var a=t(2115);let r=0,l=new Map,n=e=>{if(l.has(e))return;let s=setTimeout(()=>{l.delete(e),c({type:"REMOVE_TOAST",toastId:e})},5e3);l.set(e,s)},i=(e,s)=>{switch(s.type){case"ADD_TOAST":return{...e,toasts:[s.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===s.toast.id?{...e,...s.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=s;return t?n(t):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===s.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==s.toastId)}}},d=[],o={toasts:[]};function c(e){o=i(o,e),d.forEach(e=>{e(o)})}function m(e){let{...s}=e,t=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>c({type:"DISMISS_TOAST",toastId:t});return c({type:"ADD_TOAST",toast:{...s,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,s]=a.useState(o);return a.useEffect(()=>(d.push(s),()=>{let e=d.indexOf(s);e>-1&&d.splice(e,1)}),[e]),{...e,toast:m,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},3999:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>i,cn:()=>l,r6:()=>d,vv:()=>n});var a=t(2596),r=t(9688);function l(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,r.QP)((0,a.$)(s))}function n(e){return new Intl.NumberFormat("uz-UZ",{style:"currency",currency:"UZS",minimumFractionDigits:0}).format(e)}function i(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric"}).format(s)}function d(e){let s="string"==typeof e?new Date(e):e;return new Intl.DateTimeFormat("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}).format(s)}},4621:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]])},5784:(e,s,t)=>{"use strict";t.d(s,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>m});var a=t(5155),r=t(2115),l=t(1992),n=t(6474),i=t(7863),d=t(5196),o=t(3999);let c=l.bL;l.YJ;let m=l.WT,u=r.forwardRef((e,s)=>{let{className:t,children:r,...i}=e;return(0,a.jsxs)(l.l9,{ref:s,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",t),...i,children:[r,(0,a.jsx)(l.In,{asChild:!0,children:(0,a.jsx)(n.A,{className:"h-4 w-4 opacity-50"})})]})});u.displayName=l.l9.displayName;let x=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.PP,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(i.A,{className:"h-4 w-4"})})});x.displayName=l.PP.displayName;let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wn,{ref:s,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",t),...r,children:(0,a.jsx)(n.A,{className:"h-4 w-4"})})});h.displayName=l.wn.displayName;let p=r.forwardRef((e,s)=>{let{className:t,children:r,position:n="popper",...i}=e;return(0,a.jsx)(l.ZL,{children:(0,a.jsxs)(l.UC,{ref:s,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,a.jsx)(x,{}),(0,a.jsx)(l.LM,{className:(0,o.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,a.jsx)(h,{})]})})});p.displayName=l.UC.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.JU,{ref:s,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",t),...r})}).displayName=l.JU.displayName;let f=r.forwardRef((e,s)=>{let{className:t,children:r,...n}=e;return(0,a.jsxs)(l.q7,{ref:s,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",t),...n,children:[(0,a.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,a.jsx)(l.VF,{children:(0,a.jsx)(d.A,{className:"h-4 w-4"})})}),(0,a.jsx)(l.p4,{children:r})]})});f.displayName=l.q7.displayName,r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.wv,{ref:s,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",t),...r})}).displayName=l.wv.displayName},7168:(e,s,t)=>{"use strict";t.d(s,{$:()=>o,r:()=>d});var a=t(5155),r=t(2115),l=t(9708),n=t(2085),i=t(3999);let d=(0,n.F)("inline-flex items-center justify-center whitespace-nowrap rounded-xl text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 shadow-sm hover:shadow-md",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 hover:scale-[1.02]",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 hover:scale-[1.02]",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground hover:border-gray-300",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 hover:scale-[1.02]",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline",success:"bg-green-600 text-white hover:bg-green-700 hover:scale-[1.02]",warning:"bg-amber-600 text-white hover:bg-amber-700 hover:scale-[1.02]"},size:{default:"h-10 px-4 py-2",sm:"h-8 rounded-lg px-3 text-xs",lg:"h-12 rounded-xl px-8 text-base",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=r.forwardRef((e,s)=>{let{className:t,variant:r,size:n,asChild:o=!1,...c}=e,m=o?l.DX:"button";return(0,a.jsx)(m,{className:(0,i.cn)(d({variant:r,size:n,className:t})),ref:s,...c})});o.displayName="Button"},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8054:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>ef});var a=t(5155),r=t(2115),l=t(7168),n=t(9852),i=t(8145),d=t(8524),o=t(9840),c=t(8482),m=t(1788),u=t(2318),x=t(7580),h=t(7924),p=t(4621),f=t(2525),g=t(2177),j=t(221),N=t(1153),y=t(2714),v=t(5784),b=t(9026),w=t(1007),A=t(5525),E=t(8749),R=t(2657),C=t(2915),T=t(3580);let S=N.z.object({name:N.z.string().min(2,"Name must be at least 2 characters"),phone:N.z.string().min(9,"Phone number must be at least 9 characters"),email:N.z.string().email("Invalid email address").optional().or(N.z.literal("")),role:N.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]),password:N.z.string().min(6,"Password must be at least 6 characters"),confirmPassword:N.z.string().min(6,"Please confirm your password")}).refine(e=>e.password===e.confirmPassword,{message:"Passwords don&apos;t match",path:["confirmPassword"]}),k=N.z.object({name:N.z.string().min(2,"Name must be at least 2 characters"),phone:N.z.string().min(9,"Phone number must be at least 9 characters"),email:N.z.string().email("Invalid email address").optional().or(N.z.literal("")),role:N.z.enum(["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"]),password:N.z.string().optional(),confirmPassword:N.z.string().optional()}).refine(e=>{if(e.password||e.confirmPassword){var s;return e.password===e.confirmPassword&&((null==(s=e.password)?void 0:s.length)||0)>=6}return!0},{message:"Passwords don&apos;t match or are too short",path:["confirmPassword"]}),D={ADMIN:"Full system access including financial data and user management",MANAGER:"Management access to operations and staff oversight",TEACHER:"Access to classes, students, and assessment system",RECEPTION:"Student enrollment, leads management, and front desk operations",CASHIER:"Limited access to student lookup and payment recording only",STUDENT:"Student portal access for personal information and progress",ACADEMIC_MANAGER:"Academic management access to assign tests and view test statistics"},M={ADMIN:"text-red-600 bg-red-50 border-red-200",MANAGER:"text-blue-600 bg-blue-50 border-blue-200",TEACHER:"text-green-600 bg-green-50 border-green-200",RECEPTION:"text-purple-600 bg-purple-50 border-purple-200",CASHIER:"text-orange-600 bg-orange-50 border-orange-200",STUDENT:"text-gray-600 bg-gray-50 border-gray-200",PARENT:"text-indigo-600 bg-indigo-50 border-indigo-200"};function P(e){let{user:s,onSuccess:t,onCancel:i}=e,[d,o]=(0,r.useState)(!1),[m,u]=(0,r.useState)(!1),[x,h]=(0,r.useState)(!1),[p,f]=(0,r.useState)(""),{toast:N}=(0,T.dj)(),P=!!s,{register:I,handleSubmit:U,formState:{errors:z},setValue:O,watch:F,reset:H}=(0,g.mN)({resolver:(0,j.u)(P?k:S),defaultValues:{name:(null==s?void 0:s.name)||"",phone:(null==s?void 0:s.phone)||"",email:(null==s?void 0:s.email)||"",role:(null==s?void 0:s.role)||"",password:"",confirmPassword:""}}),_=F("role");(0,r.useEffect)(()=>{f(_)},[_]),(0,r.useEffect)(()=>{s&&(H({name:s.name,phone:s.phone,email:s.email||"",role:s.role,password:"",confirmPassword:""}),f(s.role))},[s,H]);let L=async e=>{h(!0);try{let a={name:e.name,phone:e.phone,email:e.email||null,role:e.role,...e.password&&{password:e.password}};P&&(a.id=s.id);let r=await fetch("/api/users",{method:P?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)}),l=await r.json();if(!r.ok)throw Error(l.error||"Failed to save user");N({title:"Success",description:"User ".concat(P?"updated":"created"," successfully")}),t()}catch(e){N({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"An error occurred"})}finally{h(!1)}};return(0,a.jsxs)("form",{onSubmit:U(L),className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(w.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Basic Information"})]}),(0,a.jsx)(c.BT,{children:"Enter the user's personal details"})]}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(y.J,{htmlFor:"name",children:"Full Name *"}),(0,a.jsx)(n.p,{id:"name",...I("name"),placeholder:"Enter full name",className:z.name?"border-red-500":""}),z.name&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:z.name.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(y.J,{htmlFor:"phone",children:"Phone Number *"}),(0,a.jsx)(n.p,{id:"phone",...I("phone"),placeholder:"+998901234567",className:z.phone?"border-red-500":""}),z.phone&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:z.phone.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)(y.J,{htmlFor:"email",children:"Email Address"}),(0,a.jsx)(n.p,{id:"email",type:"email",...I("email"),placeholder:"<EMAIL>",className:z.email?"border-red-500":""}),z.email&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:z.email.message}),(0,a.jsx)("p",{className:"text-xs text-gray-500 mt-1",children:"Optional - used for notifications"})]})]})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsxs)(c.ZB,{className:"flex items-center space-x-2",children:[(0,a.jsx)(A.A,{className:"h-5 w-5"}),(0,a.jsx)("span",{children:"Role & Security"})]}),(0,a.jsx)(c.BT,{children:"Set user role and access credentials"})]}),(0,a.jsxs)(c.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(y.J,{htmlFor:"role",children:"User Role *"}),(0,a.jsxs)(v.l6,{onValueChange:e=>O("role",e),defaultValue:null==s?void 0:s.role,children:[(0,a.jsx)(v.bq,{className:z.role?"border-red-500":"",children:(0,a.jsx)(v.yv,{placeholder:"Select user role"})}),(0,a.jsxs)(v.gC,{children:[(0,a.jsx)(v.eb,{value:"ADMIN",children:"Admin"}),(0,a.jsx)(v.eb,{value:"MANAGER",children:"Manager"}),(0,a.jsx)(v.eb,{value:"TEACHER",children:"Teacher"}),(0,a.jsx)(v.eb,{value:"RECEPTION",children:"Reception"}),(0,a.jsx)(v.eb,{value:"CASHIER",children:"Cashier"}),(0,a.jsx)(v.eb,{value:"STUDENT",children:"Student"}),(0,a.jsx)(v.eb,{value:"PARENT",children:"Parent"})]})]}),z.role&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:z.role.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(y.J,{htmlFor:"password",children:["Password ",P?"(leave blank to keep current)":"*"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.p,{id:"password",type:d?"text":"password",...I("password"),placeholder:P?"Enter new password":"Enter password",className:z.password?"border-red-500":""}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>o(!d),children:d?(0,a.jsx)(E.A,{className:"h-4 w-4"}):(0,a.jsx)(R.A,{className:"h-4 w-4"})})]}),z.password&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:z.password.message})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(y.J,{htmlFor:"confirmPassword",children:["Confirm Password ",P?"(if changing)":"*"]}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(n.p,{id:"confirmPassword",type:m?"text":"password",...I("confirmPassword"),placeholder:"Confirm password",className:z.confirmPassword?"border-red-500":""}),(0,a.jsx)(l.$,{type:"button",variant:"ghost",size:"sm",className:"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent",onClick:()=>u(!m),children:m?(0,a.jsx)(E.A,{className:"h-4 w-4"}):(0,a.jsx)(R.A,{className:"h-4 w-4"})})]}),z.confirmPassword&&(0,a.jsx)("p",{className:"text-sm text-red-500 mt-1",children:z.confirmPassword.message})]})]})]})]}),p&&(0,a.jsxs)(b.Fc,{className:M[p],children:[(0,a.jsx)(A.A,{className:"h-4 w-4"}),(0,a.jsxs)(b.TN,{children:[(0,a.jsxs)("strong",{children:[p," Role:"]})," ",D[p]]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 pt-4 border-t",children:[(0,a.jsx)(l.$,{type:"button",variant:"outline",onClick:i,children:"Cancel"}),(0,a.jsx)(l.$,{type:"submit",disabled:x,children:x?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),P?"Updating...":"Creating..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(C.A,{className:"h-4 w-4 mr-2"}),P?"Update User":"Create User"]})})]})]})}var I=t(6081),U=t(6101),z=t(5452),O=t(5185),F=t(9708),H="AlertDialog",[_,L]=(0,I.A)(H,[z.Hs]),G=(0,z.Hs)(),Z=e=>{let{__scopeAlertDialog:s,...t}=e,r=G(s);return(0,a.jsx)(z.bL,{...r,...t,modal:!0})};Z.displayName=H,r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...r}=e,l=G(t);return(0,a.jsx)(z.l9,{...l,...r,ref:s})}).displayName="AlertDialogTrigger";var B=e=>{let{__scopeAlertDialog:s,...t}=e,r=G(s);return(0,a.jsx)(z.ZL,{...r,...t})};B.displayName="AlertDialogPortal";var V=r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...r}=e,l=G(t);return(0,a.jsx)(z.hJ,{...l,...r,ref:s})});V.displayName="AlertDialogOverlay";var q="AlertDialogContent",[J,$]=_(q),W=(0,F.Dc)("AlertDialogContent"),Y=r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,children:l,...n}=e,i=G(t),d=r.useRef(null),o=(0,U.s)(s,d),c=r.useRef(null);return(0,a.jsx)(z.G$,{contentName:q,titleName:X,docsSlug:"alert-dialog",children:(0,a.jsx)(J,{scope:t,cancelRef:c,children:(0,a.jsxs)(z.UC,{role:"alertdialog",...i,...n,ref:o,onOpenAutoFocus:(0,O.m)(n.onOpenAutoFocus,e=>{var s;e.preventDefault(),null==(s=c.current)||s.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,a.jsx)(W,{children:l}),(0,a.jsx)(er,{contentRef:d})]})})})});Y.displayName=q;var X="AlertDialogTitle",Q=r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...r}=e,l=G(t);return(0,a.jsx)(z.hE,{...l,...r,ref:s})});Q.displayName=X;var K="AlertDialogDescription",ee=r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...r}=e,l=G(t);return(0,a.jsx)(z.VY,{...l,...r,ref:s})});ee.displayName=K;var es=r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...r}=e,l=G(t);return(0,a.jsx)(z.bm,{...l,...r,ref:s})});es.displayName="AlertDialogAction";var et="AlertDialogCancel",ea=r.forwardRef((e,s)=>{let{__scopeAlertDialog:t,...r}=e,{cancelRef:l}=$(et,t),n=G(t),i=(0,U.s)(s,l);return(0,a.jsx)(z.bm,{...n,...r,ref:i})});ea.displayName=et;var er=e=>{let{contentRef:s}=e,t="`".concat(q,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(q,"` by passing a `").concat(K,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(q,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return r.useEffect(()=>{var e;document.getElementById(null==(e=s.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(t)},[t,s]),null},el=t(3999);let en=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(V,{className:(0,el.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r,ref:s})});en.displayName=V.displayName;let ei=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsxs)(B,{children:[(0,a.jsx)(en,{}),(0,a.jsx)(Y,{ref:s,className:(0,el.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...r})]})});ei.displayName=Y.displayName;let ed=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,el.cn)("flex flex-col space-y-2 text-center sm:text-left",s),...t})};ed.displayName="AlertDialogHeader";let eo=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,el.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",s),...t})};eo.displayName="AlertDialogFooter";let ec=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(Q,{ref:s,className:(0,el.cn)("text-lg font-semibold",t),...r})});ec.displayName=Q.displayName;let em=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ee,{ref:s,className:(0,el.cn)("text-sm text-muted-foreground",t),...r})});em.displayName=ee.displayName;let eu=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(es,{ref:s,className:(0,el.cn)((0,l.r)(),t),...r})});eu.displayName=es.displayName;let ex=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(ea,{ref:s,className:(0,el.cn)((0,l.r)({variant:"outline"}),"mt-2 sm:mt-0",t),...r})});ex.displayName=ea.displayName;var eh=t(809);function ep(e){let{user:s,open:t,onOpenChange:l,onConfirm:i,isDeleting:d}=e,[o,c]=(0,r.useState)("");if(!s)return null;let m=o===s.name,u="ADMIN"===s.role;return(0,a.jsx)(Z,{open:t,onOpenChange:l,children:(0,a.jsxs)(ei,{className:"max-w-md",children:[(0,a.jsxs)(ed,{children:[(0,a.jsxs)(ec,{className:"flex items-center space-x-2",children:[(0,a.jsx)(eh.A,{className:"h-5 w-5 text-red-600"}),(0,a.jsx)("span",{children:"Delete User Account"})]}),(0,a.jsxs)(em,{className:"space-y-4",children:[(0,a.jsx)("div",{children:"You are about to permanently delete the user account for:"}),(0,a.jsxs)("div",{className:"bg-gray-50 p-3 rounded-lg border",children:[(0,a.jsx)("div",{className:"font-medium",children:s.name}),(0,a.jsx)("div",{className:"text-sm text-gray-600",children:s.phone}),s.email&&(0,a.jsx)("div",{className:"text-sm text-gray-600",children:s.email}),(0,a.jsxs)("div",{className:"flex items-center space-x-1 mt-1",children:[(0,a.jsx)(A.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{className:"text-sm font-medium",children:s.role})]})]}),u&&(0,a.jsxs)(b.Fc,{className:"border-red-200 bg-red-50",children:[(0,a.jsx)(eh.A,{className:"h-4 w-4 text-red-600"}),(0,a.jsxs)(b.TN,{className:"text-red-800",children:[(0,a.jsx)("strong",{children:"Warning:"})," You are deleting an ADMIN user. This will remove all administrative privileges associated with this account."]})]}),(0,a.jsxs)(b.Fc,{className:"border-orange-200 bg-orange-50",children:[(0,a.jsx)(eh.A,{className:"h-4 w-4 text-orange-600"}),(0,a.jsxs)(b.TN,{className:"text-orange-800",children:["This action will permanently delete:",(0,a.jsxs)("ul",{className:"list-disc list-inside mt-2 space-y-1",children:[(0,a.jsx)("li",{children:"User account and login credentials"}),(0,a.jsx)("li",{children:"Associated profile data"}),(0,a.jsx)("li",{children:"Activity history and logs"}),(0,a.jsx)("li",{children:"Any related records in the system"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)(y.J,{htmlFor:"confirmName",className:"text-sm font-medium",children:["Type ",(0,a.jsx)("strong",{children:s.name})," to confirm deletion:"]}),(0,a.jsx)(n.p,{id:"confirmName",value:o,onChange:e=>c(e.target.value),placeholder:'Type "'.concat(s.name,'" here'),className:"mt-1",disabled:d})]})]})]}),(0,a.jsxs)(eo,{children:[(0,a.jsx)(ex,{onClick:()=>{c(""),l(!1)},disabled:d,children:"Cancel"}),(0,a.jsx)(eu,{onClick:()=>{m&&!d&&(i(s.id),c(""))},disabled:!m||d,className:"bg-red-600 hover:bg-red-700 focus:ring-red-600",children:d?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Deleting..."]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Delete User"]})})]})]})})}function ef(){let[e,s]=(0,r.useState)([]),[t,g]=(0,r.useState)([]),[j,N]=(0,r.useState)(""),[y,v]=(0,r.useState)(""),[b,w]=(0,r.useState)(!1),[A,E]=(0,r.useState)(!1),[R,C]=(0,r.useState)(!1),[S,k]=(0,r.useState)(!1),[D,M]=(0,r.useState)(null),[I,U]=(0,r.useState)(null),[z,O]=(0,r.useState)(!1),{toast:F}=(0,T.dj)(),H=async()=>{w(!0);try{let e=await fetch("/api/users");if(e.ok){let t=await e.json();s(t.users),g(t.users)}}catch(e){console.error("Error fetching users:",e)}finally{w(!1)}};(0,r.useEffect)(()=>{H()},[]),(0,r.useEffect)(()=>{let s=e;j&&(s=s.filter(e=>{var s;return e.name.toLowerCase().includes(j.toLowerCase())||e.phone.includes(j)||(null==(s=e.email)?void 0:s.toLowerCase().includes(j.toLowerCase()))})),y&&(s=s.filter(e=>e.role===y)),g(s)},[e,j,y]);let _=[...new Set(e.map(e=>e.role))],L=async t=>{O(!0);try{let a=await fetch("/api/users?id=".concat(t),{method:"DELETE"});if(a.ok)s(e.filter(e=>e.id!==t)),k(!1),U(null),F({title:"Success",description:"User deleted successfully"});else{let e=await a.json();throw Error(e.error||"Failed to delete user")}}catch(e){console.error("Error deleting user:",e),F({variant:"destructive",title:"Error",description:e instanceof Error?e.message:"Failed to delete user"})}finally{O(!1)}},G=()=>{E(!1),C(!1),M(null),H()},Z=e=>{M(e),C(!0)},B=()=>{E(!1),C(!1),M(null)},V=e=>{U(e),k(!0)},q=e=>{switch(e){case"ADMIN":return"destructive";case"MANAGER":return"default";case"TEACHER":case"STUDENT":default:return"secondary";case"RECEPTION":case"CASHIER":case"PARENT":return"outline"}};return _.map(s=>({role:s,count:e.filter(e=>e.role===s).length})),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"User Management"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Manage system users and their roles"})]}),(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsxs)(l.$,{onClick:()=>{let e=new Blob([[["Name","Phone","Email","Role","Profile Info","Created"],...t.map(e=>[e.name,e.phone,e.email||"",e.role,e.studentProfile?"Student - ".concat(e.studentProfile.level," (").concat(e.studentProfile.branch,")"):e.teacherProfile?"Teacher - ".concat(e.teacherProfile.subject," (").concat(e.teacherProfile.branch,")"):"",new Date(e.createdAt).toLocaleDateString()])].map(e=>e.map(e=>'"'.concat(e,'"')).join(",")).join("\n")],{type:"text/csv"}),s=URL.createObjectURL(e),a=document.createElement("a");a.href=s,a.download="users-".concat(new Date().toISOString().split("T")[0],".csv"),a.click(),URL.revokeObjectURL(s)},variant:"outline",size:"sm",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Export CSV"]}),(0,a.jsxs)(o.lG,{open:A,onOpenChange:E,children:[(0,a.jsx)(o.zM,{asChild:!0,children:(0,a.jsxs)(l.$,{size:"sm",children:[(0,a.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Add User"]})}),(0,a.jsxs)(o.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(o.c7,{children:(0,a.jsx)(o.L3,{children:"Create New User"})}),(0,a.jsx)(P,{onSuccess:G,onCancel:B})]})]})]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)(c.Zp,{children:(0,a.jsx)(c.Wu,{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 text-blue-600"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-lg font-medium text-gray-700",children:"Total Users"}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:e.length})]})]})})}),(0,a.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4",children:["ADMIN","MANAGER","TEACHER","RECEPTION","CASHIER","STUDENT","ACADEMIC_MANAGER"].map(s=>{let t=e.filter(e=>e.role===s).length;return(0,a.jsx)(c.Zp,{className:"hover:shadow-lg transition-shadow",children:(0,a.jsx)(c.Wu,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"text-2xl",children:{ADMIN:"\uD83D\uDC51",MANAGER:"\uD83D\uDCCA",TEACHER:"\uD83C\uDF93",RECEPTION:"\uD83D\uDCDE",CASHIER:"\uD83D\uDCB0",STUDENT:"\uD83D\uDCDA",ACADEMIC_MANAGER:"\uD83D\uDCCB"}[s]}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium ".concat({ADMIN:"text-red-600",MANAGER:"text-blue-600",TEACHER:"text-green-600",RECEPTION:"text-purple-600",CASHIER:"text-orange-600",STUDENT:"text-gray-600",ACADEMIC_MANAGER:"text-indigo-600"}[s]),children:s.replace("_"," ")}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:t})]})]})})},s)})})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(h.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)(n.p,{placeholder:"Search users...",value:j,onChange:e=>N(e.target.value),className:"pl-10"})]}),(0,a.jsxs)("select",{value:y,onChange:e=>v(e.target.value),className:"px-3 py-2 border rounded-md",children:[(0,a.jsx)("option",{value:"",children:"All Roles"}),(0,a.jsx)("option",{value:"ADMIN",children:"Admin"}),(0,a.jsx)("option",{value:"MANAGER",children:"Manager"}),(0,a.jsx)("option",{value:"TEACHER",children:"Teacher"}),(0,a.jsx)("option",{value:"RECEPTION",children:"Reception"}),(0,a.jsx)("option",{value:"CASHIER",children:"Cashier"}),(0,a.jsx)("option",{value:"STUDENT",children:"Student"}),(0,a.jsx)("option",{value:"PARENT",children:"Parent"})]}),(y||j)&&(0,a.jsx)(l.$,{variant:"outline",onClick:()=>{v(""),N("")},children:"Clear Filters"})]}),(0,a.jsxs)(c.Zp,{children:[(0,a.jsxs)(c.aR,{children:[(0,a.jsx)(c.ZB,{children:"Users"}),(0,a.jsx)(c.BT,{children:"Manage user accounts and permissions"})]}),(0,a.jsx)(c.Wu,{children:(0,a.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,a.jsxs)(d.XI,{children:[(0,a.jsx)(d.A0,{children:(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nd,{children:"User"}),(0,a.jsx)(d.nd,{children:"Contact"}),(0,a.jsx)(d.nd,{children:"Role"}),(0,a.jsx)(d.nd,{children:"Profile"}),(0,a.jsx)(d.nd,{children:"Created"}),(0,a.jsx)(d.nd,{children:"Actions"})]})}),(0,a.jsx)(d.BF,{children:b?(0,a.jsx)(d.Hj,{children:(0,a.jsx)(d.nA,{colSpan:6,className:"text-center py-8",children:"Loading users..."})}):0===t.length?(0,a.jsx)(d.Hj,{children:(0,a.jsx)(d.nA,{colSpan:6,className:"text-center py-8",children:"No users found"})}):t.map(e=>(0,a.jsxs)(d.Hj,{children:[(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["ID: ",e.id.slice(0,8),"..."]})]})}),(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("div",{children:e.phone}),e.email&&(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.email})]})}),(0,a.jsx)(d.nA,{children:(0,a.jsx)(i.E,{variant:q(e.role),children:e.role})}),(0,a.jsx)(d.nA,{children:e.studentProfile?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{children:["Level: ",e.studentProfile.level]}),(0,a.jsxs)("div",{className:"text-gray-500",children:["Branch: ",e.studentProfile.branch]})]}):e.teacherProfile?(0,a.jsxs)("div",{className:"text-sm",children:[(0,a.jsxs)("div",{children:["Subject: ",e.teacherProfile.subject]}),(0,a.jsxs)("div",{className:"text-gray-500",children:["Branch: ",e.teacherProfile.branch]})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"No profile"})}),(0,a.jsx)(d.nA,{children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsx)(d.nA,{children:(0,a.jsxs)("div",{className:"flex gap-2",children:[(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>Z(e),children:(0,a.jsx)(p.A,{className:"h-4 w-4"})}),(0,a.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>V(e),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(f.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})})]}),(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Showing ",t.length," of ",e.length," users"]}),(0,a.jsx)(o.lG,{open:R,onOpenChange:C,children:(0,a.jsxs)(o.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,a.jsx)(o.c7,{children:(0,a.jsx)(o.L3,{children:"Edit User"})}),(0,a.jsx)(P,{user:D,onSuccess:G,onCancel:B})]})}),(0,a.jsx)(ep,{user:I,open:S,onOpenChange:k,onConfirm:L,isDeleting:z})]})}},8145:(e,s,t)=>{"use strict";t.d(s,{E:()=>i});var a=t(5155);t(2115);var r=t(2085),l=t(3999);let n=(0,r.F)("inline-flex items-center rounded-full border font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground"},size:{default:"px-2.5 py-0.5 text-xs",sm:"px-2 py-0.5 text-xs",lg:"px-3 py-1 text-sm"}},defaultVariants:{variant:"default",size:"default"}});function i(e){let{className:s,variant:t,...r}=e;return(0,a.jsx)("div",{className:(0,l.cn)(n({variant:t}),s),...r})}},8482:(e,s,t)=>{"use strict";t.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>n,aR:()=>i});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",t),...r})});n.displayName="Card";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex flex-col space-y-1.5 p-6",t),...r})});i.displayName="CardHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h3",{ref:s,className:(0,l.cn)("text-2xl font-semibold leading-none tracking-tight",t),...r})});d.displayName="CardTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("p",{ref:s,className:(0,l.cn)("text-sm text-muted-foreground",t),...r})});o.displayName="CardDescription";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("p-6 pt-0",t),...r})});c.displayName="CardContent",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,l.cn)("flex items-center p-6 pt-0",t),...r})}).displayName="CardFooter"},8524:(e,s,t)=>{"use strict";t.d(s,{A0:()=>i,BF:()=>d,Hj:()=>o,XI:()=>n,nA:()=>m,nd:()=>c});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,l.cn)("w-full caption-bottom text-sm",t),...r})})});n.displayName="Table";let i=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("thead",{ref:s,className:(0,l.cn)("[&_tr]:border-b",t),...r})});i.displayName="TableHeader";let d=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,l.cn)("[&_tr:last-child]:border-0",t),...r})});d.displayName="TableBody",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,l.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...r})}).displayName="TableFooter";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("tr",{ref:s,className:(0,l.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...r})});o.displayName="TableRow";let c=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("th",{ref:s,className:(0,l.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...r})});c.displayName="TableHead";let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("td",{ref:s,className:(0,l.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...r})});m.displayName="TableCell",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("caption",{ref:s,className:(0,l.cn)("mt-4 text-sm text-muted-foreground",t),...r})}).displayName="TableCaption"},8749:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("EyeOff",[["path",{d:"M9.88 9.88a3 3 0 1 0 4.24 4.24",key:"1jxqfv"}],["path",{d:"M10.73 5.08A10.43 10.43 0 0 1 12 5c7 0 10 7 10 7a13.16 13.16 0 0 1-1.67 2.68",key:"9wicm4"}],["path",{d:"M6.61 6.61A13.526 13.526 0 0 0 2 12s3 7 10 7a9.74 9.74 0 0 0 5.39-1.61",key:"1jreej"}],["line",{x1:"2",x2:"22",y1:"2",y2:"22",key:"a6p6uj"}]])},9026:(e,s,t)=>{"use strict";t.d(s,{Fc:()=>d,TN:()=>o});var a=t(5155),r=t(2115),l=t(2085),n=t(3999);let i=(0,l.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef((e,s)=>{let{className:t,variant:r,...l}=e;return(0,a.jsx)("div",{ref:s,role:"alert",className:(0,n.cn)(i({variant:r}),t),...l})});d.displayName="Alert",r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("h5",{ref:s,className:(0,n.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})}).displayName="AlertTitle";let o=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)("div",{ref:s,className:(0,n.cn)("text-sm [&_p]:leading-relaxed",t),...r})});o.displayName="AlertDescription"},9840:(e,s,t)=>{"use strict";t.d(s,{Cf:()=>u,L3:()=>h,c7:()=>x,lG:()=>d,rr:()=>p,zM:()=>o});var a=t(5155),r=t(2115),l=t(5452),n=t(4416),i=t(3999);let d=l.bL,o=l.l9,c=l.ZL;l.bm;let m=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});m.displayName=l.hJ.displayName;let u=r.forwardRef((e,s)=>{let{className:t,children:r,...d}=e;return(0,a.jsxs)(c,{children:[(0,a.jsx)(m,{}),(0,a.jsx)(l.UC,{ref:s,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",t),...d,children:(0,a.jsxs)("div",{className:"relative",children:[r,(0,a.jsxs)(l.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]})});u.displayName=l.UC.displayName;let x=e=>{let{className:s,...t}=e;return(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",s),...t})};x.displayName="DialogHeader";let h=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});h.displayName=l.hE.displayName;let p=r.forwardRef((e,s)=>{let{className:t,...r}=e;return(0,a.jsx)(l.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",t),...r})});p.displayName=l.VY.displayName},9852:(e,s,t)=>{"use strict";t.d(s,{p:()=>n});var a=t(5155),r=t(2115),l=t(3999);let n=r.forwardRef((e,s)=>{let{className:t,type:r,...n}=e;return(0,a.jsx)("input",{type:r,className:(0,l.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:s,...n})});n.displayName="Input"}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,2356,8441,1684,7358],()=>s(712)),_N_E=e.O()}]);