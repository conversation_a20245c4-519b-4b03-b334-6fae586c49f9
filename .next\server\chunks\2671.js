"use strict";exports.id=2671,exports.ids=[2671],exports.modules={3018:(e,t,a)=>{a.d(t,{Fc:()=>n,TN:()=>i});var r=a(60687),s=a(43210),o=a(24224),l=a(96241);let d=(0,o.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),n=s.forwardRef(({className:e,variant:t,...a},s)=>(0,r.jsx)("div",{ref:s,role:"alert",className:(0,l.cn)(d({variant:t}),e),...a}));n.displayName="Alert",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("h5",{ref:a,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...t})).displayName="AlertTitle";let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...t}));i.displayName="AlertDescription"},26134:(e,t,a)=>{a.d(t,{G$:()=>$,Hs:()=>v,UC:()=>ea,VY:()=>es,ZL:()=>ee,bL:()=>X,bm:()=>eo,hE:()=>er,hJ:()=>et,l9:()=>Q});var r=a(43210),s=a(70569),o=a(98599),l=a(11273),d=a(96963),n=a(65551),i=a(31355),c=a(32547),f=a(25028),u=a(46059),p=a(14163),m=a(1359),x=a(42247),g=a(63376),b=a(8730),y=a(60687),h="Dialog",[N,v]=(0,l.A)(h),[w,j]=N(h),R=e=>{let{__scopeDialog:t,children:a,open:s,defaultOpen:o,onOpenChange:l,modal:i=!0}=e,c=r.useRef(null),f=r.useRef(null),[u,p]=(0,n.i)({prop:s,defaultProp:o??!1,onChange:l,caller:h});return(0,y.jsx)(w,{scope:t,triggerRef:c,contentRef:f,contentId:(0,d.B)(),titleId:(0,d.B)(),descriptionId:(0,d.B)(),open:u,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:i,children:a})};R.displayName=h;var C="DialogTrigger",D=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,l=j(C,a),d=(0,o.s)(t,l.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":V(l.open),...r,ref:d,onClick:(0,s.m)(e.onClick,l.onOpenToggle)})});D.displayName=C;var k="DialogPortal",[A,I]=N(k,{forceMount:void 0}),F=e=>{let{__scopeDialog:t,forceMount:a,children:s,container:o}=e,l=j(k,t);return(0,y.jsx)(A,{scope:t,forceMount:a,children:r.Children.map(s,e=>(0,y.jsx)(u.C,{present:a||l.open,children:(0,y.jsx)(f.Z,{asChild:!0,container:o,children:e})}))})};F.displayName=k;var T="DialogOverlay",O=r.forwardRef((e,t)=>{let a=I(T,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,o=j(T,e.__scopeDialog);return o.modal?(0,y.jsx)(u.C,{present:r||o.open,children:(0,y.jsx)(_,{...s,ref:t})}):null});O.displayName=T;var E=(0,b.TL)("DialogOverlay.RemoveScroll"),_=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=j(T,a);return(0,y.jsx)(x.A,{as:E,allowPinchZoom:!0,shards:[s.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":V(s.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),P="DialogContent",B=r.forwardRef((e,t)=>{let a=I(P,e.__scopeDialog),{forceMount:r=a.forceMount,...s}=e,o=j(P,e.__scopeDialog);return(0,y.jsx)(u.C,{present:r||o.open,children:o.modal?(0,y.jsx)(L,{...s,ref:t}):(0,y.jsx)(U,{...s,ref:t})})});B.displayName=P;var L=r.forwardRef((e,t)=>{let a=j(P,e.__scopeDialog),l=r.useRef(null),d=(0,o.s)(t,a.contentRef,l);return r.useEffect(()=>{let e=l.current;if(e)return(0,g.Eq)(e)},[]),(0,y.jsx)(z,{...e,ref:d,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,s.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,s.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,a=0===t.button&&!0===t.ctrlKey;(2===t.button||a)&&e.preventDefault()}),onFocusOutside:(0,s.m)(e.onFocusOutside,e=>e.preventDefault())})}),U=r.forwardRef((e,t)=>{let a=j(P,e.__scopeDialog),s=r.useRef(!1),o=r.useRef(!1);return(0,y.jsx)(z,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(s.current||a.triggerRef.current?.focus(),t.preventDefault()),s.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(s.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let r=t.target;a.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),z=r.forwardRef((e,t)=>{let{__scopeDialog:a,trapFocus:s,onOpenAutoFocus:l,onCloseAutoFocus:d,...n}=e,f=j(P,a),u=r.useRef(null),p=(0,o.s)(t,u);return(0,m.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:s,onMountAutoFocus:l,onUnmountAutoFocus:d,children:(0,y.jsx)(i.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":V(f.open),...n,ref:p,onDismiss:()=>f.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(S,{titleId:f.titleId}),(0,y.jsx)(K,{contentRef:u,descriptionId:f.descriptionId})]})]})}),H="DialogTitle",M=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=j(H,a);return(0,y.jsx)(p.sG.h2,{id:s.titleId,...r,ref:t})});M.displayName=H;var q="DialogDescription",G=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,s=j(q,a);return(0,y.jsx)(p.sG.p,{id:s.descriptionId,...r,ref:t})});G.displayName=q;var J="DialogClose",Z=r.forwardRef((e,t)=>{let{__scopeDialog:a,...r}=e,o=j(J,a);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,s.m)(e.onClick,()=>o.onOpenChange(!1))})});function V(e){return e?"open":"closed"}Z.displayName=J;var W="DialogTitleWarning",[$,Y]=(0,l.q)(W,{contentName:P,titleName:H,docsSlug:"dialog"}),S=({titleId:e})=>{let t=Y(W),a=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},K=({contentRef:e,descriptionId:t})=>{let a=Y("DialogDescriptionWarning"),s=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return r.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");t&&a&&(document.getElementById(t)||console.warn(s))},[s,e,t]),null},X=R,Q=D,ee=F,et=O,ea=B,er=M,es=G,eo=Z},36058:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(18962).A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]])},37826:(e,t,a)=>{a.d(t,{Cf:()=>u,L3:()=>m,c7:()=>p,lG:()=>n,rr:()=>x,zM:()=>i});var r=a(60687),s=a(43210),o=a(26134),l=a(11860),d=a(96241);let n=o.bL,i=o.l9,c=o.ZL;o.bm;let f=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.hJ,{ref:a,className:(0,d.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));f.displayName=o.hJ.displayName;let u=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(c,{children:[(0,r.jsx)(f,{}),(0,r.jsx)(o.UC,{ref:s,className:(0,d.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",e),...a,children:(0,r.jsxs)("div",{className:"relative",children:[t,(0,r.jsxs)(o.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(l.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]}));u.displayName=o.UC.displayName;let p=({className:e,...t})=>(0,r.jsx)("div",{className:(0,d.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let m=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.hE,{ref:a,className:(0,d.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));m.displayName=o.hE.displayName;let x=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.VY,{ref:a,className:(0,d.cn)("text-sm text-muted-foreground",e),...t}));x.displayName=o.VY.displayName},39390:(e,t,a)=>{a.d(t,{J:()=>i});var r=a(60687),s=a(43210),o=a(78148),l=a(24224),d=a(96241);let n=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.b,{ref:a,className:(0,d.cn)(n(),e),...t}));i.displayName=o.b.displayName},55192:(e,t,a)=>{a.d(t,{BT:()=>i,Wu:()=>c,ZB:()=>n,Zp:()=>l,aR:()=>d});var r=a(60687),s=a(43210),o=a(96241);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,o.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...t}));l.displayName="Card";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));d.displayName="CardHeader";let n=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("h3",{ref:a,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));n.displayName="CardTitle";let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("p",{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",e),...t}));i.displayName="CardDescription";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,o.cn)("p-6 pt-0",e),...t}));c.displayName="CardContent",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{ref:a,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},58869:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},63974:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>g,gC:()=>x,l6:()=>c,yv:()=>f});var r=a(60687),s=a(43210),o=a(22670),l=a(78272),d=a(3589),n=a(13964),i=a(96241);let c=o.bL;o.YJ;let f=o.WT,u=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(o.l9,{ref:s,className:(0,i.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[t,(0,r.jsx)(o.In,{asChild:!0,children:(0,r.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=o.l9.displayName;let p=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.PP,{ref:a,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(d.A,{className:"h-4 w-4"})}));p.displayName=o.PP.displayName;let m=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.wn,{ref:a,className:(0,i.cn)("flex cursor-default items-center justify-center py-1",e),...t,children:(0,r.jsx)(l.A,{className:"h-4 w-4"})}));m.displayName=o.wn.displayName;let x=s.forwardRef(({className:e,children:t,position:a="popper",...s},l)=>(0,r.jsx)(o.ZL,{children:(0,r.jsxs)(o.UC,{ref:l,className:(0,i.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...s,children:[(0,r.jsx)(p,{}),(0,r.jsx)(o.LM,{className:(0,i.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:t}),(0,r.jsx)(m,{})]})}));x.displayName=o.UC.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.JU,{ref:a,className:(0,i.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...t})).displayName=o.JU.displayName;let g=s.forwardRef(({className:e,children:t,...a},s)=>(0,r.jsxs)(o.q7,{ref:s,className:(0,i.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,r.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,r.jsx)(o.VF,{children:(0,r.jsx)(n.A,{className:"h-4 w-4"})})}),(0,r.jsx)(o.p4,{children:t})]}));g.displayName=o.q7.displayName,s.forwardRef(({className:e,...t},a)=>(0,r.jsx)(o.wv,{ref:a,className:(0,i.cn)("-mx-1 my-1 h-px bg-muted",e),...t})).displayName=o.wv.displayName},68988:(e,t,a)=>{a.d(t,{p:()=>l});var r=a(60687),s=a(43210),o=a(96241);let l=s.forwardRef(({className:e,type:t,...a},s)=>(0,r.jsx)("input",{type:t,className:(0,o.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...a}));l.displayName="Input"},96752:(e,t,a)=>{a.d(t,{A0:()=>d,BF:()=>n,Hj:()=>i,XI:()=>l,nA:()=>f,nd:()=>c});var r=a(60687),s=a(43210),o=a(96241);let l=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("div",{className:"relative w-full overflow-auto",children:(0,r.jsx)("table",{ref:a,className:(0,o.cn)("w-full caption-bottom text-sm",e),...t})}));l.displayName="Table";let d=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("thead",{ref:a,className:(0,o.cn)("[&_tr]:border-b",e),...t}));d.displayName="TableHeader";let n=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tbody",{ref:a,className:(0,o.cn)("[&_tr:last-child]:border-0",e),...t}));n.displayName="TableBody",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tfoot",{ref:a,className:(0,o.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...t})).displayName="TableFooter";let i=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("tr",{ref:a,className:(0,o.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...t}));i.displayName="TableRow";let c=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("th",{ref:a,className:(0,o.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...t}));c.displayName="TableHead";let f=s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("td",{ref:a,className:(0,o.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...t}));f.displayName="TableCell",s.forwardRef(({className:e,...t},a)=>(0,r.jsx)("caption",{ref:a,className:(0,o.cn)("mt-4 text-sm text-muted-foreground",e),...t})).displayName="TableCaption"}};