(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4074],{1007:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1586:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2178:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Pause",[["rect",{width:"4",height:"16",x:"6",y:"4",key:"iffhe4"}],["rect",{width:"4",height:"16",x:"14",y:"4",key:"sjin7j"}]])},2525:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},2714:(e,s,t)=>{"use strict";t.d(s,{J:()=>c});var a=t(5155),l=t(2115),r=t(968),n=t(2085),d=t(3999);let i=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)(r.b,{ref:s,className:(0,d.cn)(i(),t),...l})});c.displayName=r.b.displayName},2915:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},4516:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},5670:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},7580:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7624:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7949:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("GraduationCap",[["path",{d:"M22 10v6M2 10l10-5 10 5-10 5z",key:"1ef52a"}],["path",{d:"M6 12v5c3 3 9 3 12 0v-5",key:"1f75yj"}]])},8082:(e,s,t)=>{Promise.resolve().then(t.bind(t,9537))},8524:(e,s,t)=>{"use strict";t.d(s,{A0:()=>d,BF:()=>i,Hj:()=>c,XI:()=>n,nA:()=>o,nd:()=>x});var a=t(5155),l=t(2115),r=t(3999);let n=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("div",{className:"relative w-full overflow-auto",children:(0,a.jsx)("table",{ref:s,className:(0,r.cn)("w-full caption-bottom text-sm",t),...l})})});n.displayName="Table";let d=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("thead",{ref:s,className:(0,r.cn)("[&_tr]:border-b",t),...l})});d.displayName="TableHeader";let i=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tbody",{ref:s,className:(0,r.cn)("[&_tr:last-child]:border-0",t),...l})});i.displayName="TableBody",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tfoot",{ref:s,className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...l})}).displayName="TableFooter";let c=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("tr",{ref:s,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...l})});c.displayName="TableRow";let x=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("th",{ref:s,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...l})});x.displayName="TableHead";let o=l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("td",{ref:s,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...l})});o.displayName="TableCell",l.forwardRef((e,s)=>{let{className:t,...l}=e;return(0,a.jsx)("caption",{ref:s,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",t),...l})}).displayName="TableCaption"},8883:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9074:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]])},9420:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(2895).A)("Phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]])},9537:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>D});var a=t(5155),l=t(2115),r=t(8482),n=t(7168),d=t(8145),i=t(9852),c=t(5784),x=t(8524),o=t(9840),h=t(9026),m=t(3999),u=t(2915);let j=(0,t(2895).A)("UserX",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["line",{x1:"17",x2:"22",y1:"8",y2:"13",key:"3nzzx3"}],["line",{x1:"22",x2:"17",y1:"8",y2:"13",key:"1swrse"}]]);var y=t(2178),p=t(5670),f=t(1007),g=t(7624),N=t(4616),v=t(7580),b=t(1586),w=t(7924),A=t(9420),k=t(8883),S=t(4621),C=t(2525),E=t(8443),P=t(7705);function D(){let{currentBranch:e}=(0,P.O)(),[s,t]=(0,l.useState)([]),[D,L]=(0,l.useState)({ACTIVE:0,DROPPED:0,PAUSED:0,COMPLETED:0}),[T,M]=(0,l.useState)(!0),[O,R]=(0,l.useState)(""),[U,B]=(0,l.useState)("ALL"),[I,Z]=(0,l.useState)("ALL"),[q,F]=(0,l.useState)(!1),[H,z]=(0,l.useState)(null),[V,_]=(0,l.useState)(!1),[W,$]=(0,l.useState)(!1),[G,J]=(0,l.useState)(null),[X,Y]=(0,l.useState)(!1),[K,Q]=(0,l.useState)(null);(0,l.useEffect)(()=>{(null==e?void 0:e.id)&&ee()},[U,I,q,null==e?void 0:e.id]);let ee=async()=>{if(null==e?void 0:e.id)try{M(!0);let s=new URLSearchParams({branch:e.id});"ALL"!==U&&s.append("status",U),"ALL"!==I&&s.append("paymentStatus",I),q&&s.append("includeDropped","true");let a=await fetch("/api/students?".concat(s.toString())),l=await a.json();t(l.students||[]),L(l.statusCounts||{}),Q(null)}catch(e){console.error("Error fetching students:",e),Q("Failed to fetch students")}finally{M(!1)}},es=s.filter(e=>{var s;return e.user.name.toLowerCase().includes(O.toLowerCase())||e.user.phone.includes(O)||(null==(s=e.user.email)?void 0:s.toLowerCase().includes(O.toLowerCase()))}),et=Object.values(D).reduce((e,s)=>e+s,0),ea=D.ACTIVE||0,el=D.DROPPED||0,er=D.PAUSED||0,en=s.filter(e=>"UNPAID"===e.paymentStatus).length,ed=e=>({A1:"bg-red-100 text-red-800",A2:"bg-orange-100 text-orange-800",B1:"bg-yellow-100 text-yellow-800",B2:"bg-green-100 text-green-800",IELTS:"bg-indigo-100 text-indigo-800",SAT:"bg-cyan-100 text-cyan-800",MATH:"bg-emerald-100 text-emerald-800",KIDS:"bg-pink-100 text-pink-800"})[e]||"bg-gray-100 text-gray-800",ei=e=>{switch(e){case"ACTIVE":return"bg-green-100 text-green-800";case"COMPLETED":return"bg-blue-100 text-blue-800";case"DROPPED":return"bg-red-100 text-red-800";case"PAUSED":return"bg-yellow-100 text-yellow-800";case"SUSPENDED":return"bg-orange-100 text-orange-800";default:return"bg-gray-100 text-gray-800"}},ec=e=>{switch(e){case"ACTIVE":return(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-600"});case"DROPPED":return(0,a.jsx)(j,{className:"h-4 w-4 text-red-600"});case"PAUSED":return(0,a.jsx)(y.A,{className:"h-4 w-4 text-yellow-600"});case"COMPLETED":return(0,a.jsx)(p.A,{className:"h-4 w-4 text-blue-600"});default:return(0,a.jsx)(f.A,{className:"h-4 w-4 text-gray-600"})}},ex=e=>"UNPAID"===e?"bg-red-100 text-red-800":"bg-green-100 text-green-800",eo=async e=>{Y(!0),Q(null);try{let s=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:e.name,phone:e.phone,email:e.email||null,role:"STUDENT",password:"defaultPassword123"})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to create user")}let t=await s.json(),a=await fetch("/api/students",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:t.id,level:e.level,branch:e.branch,emergencyContact:e.emergencyContact,dateOfBirth:e.dateOfBirth,address:e.address})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to create student")}_(!1),ee()}catch(e){Q(e instanceof Error?e.message:"An error occurred")}finally{Y(!1)}},eh=async e=>{if(G){Y(!0),Q(null);try{let s=await fetch("/api/students/".concat(G.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({level:e.level,branch:e.branch,emergencyContact:e.emergencyContact,dateOfBirth:e.dateOfBirth,address:e.address})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update student")}$(!1),J(null),ee()}catch(e){Q(e instanceof Error?e.message:"An error occurred")}finally{Y(!1)}}},em=async e=>{if(confirm("Are you sure you want to delete this student? This action cannot be undone."))try{let s=await fetch("/api/students/".concat(e),{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete student")}ee()}catch(e){Q(e instanceof Error?e.message:"An error occurred")}};return T?(0,a.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,a.jsx)(g.A,{className:"h-8 w-8 animate-spin"}),(0,a.jsx)("span",{className:"ml-2",children:"Loading students..."})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[K&&(0,a.jsx)(h.Fc,{variant:"destructive",children:(0,a.jsx)(h.TN,{children:K})}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:[q?"Dropped Students Management":"Students Management"," - ",null==e?void 0:e.name]}),(0,a.jsxs)("p",{className:"text-gray-600",children:[q?"Contact and re-enroll dropped students - Lead-like functionality":"Unified student management with status tracking and payment monitoring"," for ",null==e?void 0:e.name]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(n.$,{variant:q?"default":"outline",onClick:()=>F(!q),children:[(0,a.jsx)(j,{className:"h-4 w-4 mr-2"}),q?"Show Regular Students":"Show Dropped Students"]}),(0,a.jsxs)(o.lG,{open:V,onOpenChange:_,children:[(0,a.jsx)(o.zM,{asChild:!0,children:(0,a.jsxs)(n.$,{children:[(0,a.jsx)(N.A,{className:"h-4 w-4 mr-2"}),"Add Student"]})}),(0,a.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(o.c7,{children:[(0,a.jsx)(o.L3,{children:"Add New Student"}),(0,a.jsx)(o.rr,{children:"Create a new student profile with their personal and academic information."})]}),(0,a.jsx)(E.A,{onSubmit:eo,onCancel:()=>_(!1),isEditing:!1})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6",children:[(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Total Students"}),(0,a.jsx)(v.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:et}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"All registered students"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Active Students"}),(0,a.jsx)(u.A,{className:"h-4 w-4 text-green-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-green-600",children:ea}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Currently enrolled"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Unpaid Students"}),(0,a.jsx)(b.A,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:en}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Have pending payments"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Dropped Students"}),(0,a.jsx)(j,{className:"h-4 w-4 text-red-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-red-600",children:el}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Available for re-enrollment"})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(r.ZB,{className:"text-sm font-medium",children:"Paused Students"}),(0,a.jsx)(y.A,{className:"h-4 w-4 text-yellow-600"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold text-yellow-600",children:er}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Temporarily inactive"})]})]})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsx)(r.aR,{children:(0,a.jsx)(r.ZB,{children:"Search and Filter Students"})}),(0,a.jsx)(r.Wu,{children:(0,a.jsxs)("div",{className:"flex flex-col md:flex-row gap-4",children:[(0,a.jsxs)("div",{className:"relative flex-1",children:[(0,a.jsx)(w.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,a.jsx)(i.p,{placeholder:"Search by name, phone, or email...",value:O,onChange:e=>R(e.target.value),className:"pl-10"})]}),(0,a.jsxs)(c.l6,{value:U,onValueChange:B,children:[(0,a.jsx)(c.bq,{className:"w-full md:w-48",children:(0,a.jsx)(c.yv,{placeholder:"Filter by status"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"ALL",children:"All Statuses"}),(0,a.jsx)(c.eb,{value:"ACTIVE",children:"Active"}),(0,a.jsx)(c.eb,{value:"DROPPED",children:"Dropped"}),(0,a.jsx)(c.eb,{value:"PAUSED",children:"Paused"}),(0,a.jsx)(c.eb,{value:"COMPLETED",children:"Completed"})]})]}),(0,a.jsxs)(c.l6,{value:I,onValueChange:Z,children:[(0,a.jsx)(c.bq,{className:"w-full md:w-48",children:(0,a.jsx)(c.yv,{placeholder:"Filter by payment"})}),(0,a.jsxs)(c.gC,{children:[(0,a.jsx)(c.eb,{value:"ALL",children:"All Payments"}),(0,a.jsx)(c.eb,{value:"PAID",children:"Paid"}),(0,a.jsx)(c.eb,{value:"UNPAID",children:"Unpaid"})]})]})]})})]}),(0,a.jsxs)(r.Zp,{children:[(0,a.jsxs)(r.aR,{children:[(0,a.jsxs)(r.ZB,{children:["Students (",es.length,")"]}),(0,a.jsx)(r.BT,{children:"Unified student management with status and payment tracking"})]}),(0,a.jsxs)(r.Wu,{children:[(0,a.jsxs)(x.XI,{children:[(0,a.jsx)(x.A0,{children:(0,a.jsxs)(x.Hj,{children:[(0,a.jsx)(x.nd,{children:"Student"}),(0,a.jsx)(x.nd,{children:"Status"}),(0,a.jsx)(x.nd,{children:"Current Group"}),(0,a.jsx)(x.nd,{children:"Level"}),(0,a.jsx)(x.nd,{children:"Payment Status"}),(0,a.jsx)(x.nd,{children:"Branch"}),(0,a.jsx)(x.nd,{children:"Actions"})]})}),(0,a.jsx)(x.BF,{children:es.map(e=>(0,a.jsxs)(x.Hj,{children:[(0,a.jsx)(x.nA,{children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:ec(e.status)})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium text-gray-900",children:e.user.name}),(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,a.jsx)(A.A,{className:"h-3 w-3 mr-1"}),e.user.phone]}),e.user.email&&(0,a.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,a.jsx)(k.A,{className:"h-3 w-3 mr-1"}),e.user.email]})]})]})}),(0,a.jsxs)(x.nA,{children:[(0,a.jsx)(d.E,{className:ei(e.status),children:e.status}),"DROPPED"===e.status&&e.droppedAt&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Dropped: ",(0,m.Yq)(new Date(e.droppedAt))]}),"PAUSED"===e.status&&e.pausedAt&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 mt-1",children:["Paused: ",(0,m.Yq)(new Date(e.pausedAt))]})]}),(0,a.jsx)(x.nA,{children:e.enrollments.length>0?(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-medium",children:e.enrollments[0].group.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.enrollments[0].group.course.name}),(0,a.jsxs)("div",{className:"text-xs text-gray-500",children:["Status: ",e.enrollments[0].status]})]}):(0,a.jsx)("span",{className:"text-gray-400",children:"No enrollments"})}),(0,a.jsx)(x.nA,{children:(0,a.jsx)(d.E,{className:ed(e.level),children:e.level.replace("_"," ")})}),(0,a.jsxs)(x.nA,{children:[(0,a.jsx)(d.E,{className:ex(e.paymentStatus),children:e.paymentStatus}),"UNPAID"===e.paymentStatus&&e.unpaidAmount>0&&(0,a.jsxs)("div",{className:"text-xs text-red-600 mt-1",children:["Unpaid: $",e.unpaidAmount.toLocaleString()]})]}),(0,a.jsx)(x.nA,{children:e.branch}),(0,a.jsx)(x.nA,{children:(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)(o.lG,{children:[(0,a.jsx)(o.zM,{asChild:!0,children:(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>z(e),children:"View Details"})}),(0,a.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(o.c7,{children:[(0,a.jsx)(o.L3,{children:"Student Details"}),(0,a.jsxs)(o.rr,{children:["Complete information about ",null==H?void 0:H.user.name]})]}),H&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Name"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:H.user.name})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Phone"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:H.user.phone})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Email"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:H.user.email||"Not provided"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Level"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:H.level})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Branch"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:H.branch})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Emergency Contact"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:H.emergencyContact||"Not provided"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Current Enrollments"}),(0,a.jsx)("div",{className:"mt-2 space-y-2",children:H.enrollments.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-2 border rounded",children:[(0,a.jsxs)("span",{children:[e.group.course.name," - ",e.group.name]}),(0,a.jsx)(d.E,{className:ei(e.status),children:e.status})]},e.id))})]})]})]})]}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>{J(e),$(!0)},children:(0,a.jsx)(S.A,{className:"h-4 w-4"})}),(0,a.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>em(e.id),className:"text-red-600 hover:text-red-700",children:(0,a.jsx)(C.A,{className:"h-4 w-4"})})]})})]},e.id))})]}),0===es.length&&(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsx)("p",{className:"text-gray-500",children:"No students found matching your search."})})]})]}),(0,a.jsx)(o.lG,{open:W,onOpenChange:$,children:(0,a.jsxs)(o.Cf,{className:"max-w-2xl",children:[(0,a.jsxs)(o.c7,{children:[(0,a.jsx)(o.L3,{children:"Edit Student"}),(0,a.jsx)(o.rr,{children:"Update student information and academic details."})]}),G&&(0,a.jsx)(E.A,{initialData:{name:G.user.name,phone:G.user.phone,email:G.user.email||"",level:G.level,branch:G.branch,emergencyContact:G.emergencyContact||"",dateOfBirth:G.dateOfBirth?new Date(G.dateOfBirth).toISOString().split("T")[0]:"",address:G.address||""},onSubmit:eh,onCancel:()=>{$(!1),J(null)},isEditing:!0})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,2356,7968,8443,8441,1684,7358],()=>s(8082)),_N_E=e.O()}]);