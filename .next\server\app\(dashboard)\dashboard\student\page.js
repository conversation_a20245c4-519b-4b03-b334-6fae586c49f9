(()=>{var e={};e.id=317,e.ids=[317],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},41001:(e,s,t)=>{Promise.resolve().then(t.bind(t,43448))},43410:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\student\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\page.tsx","default")},43448:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var a=t(60687),r=t(43210),n=t(55192),l=t(59821),d=t(24934),i=t(72730),c=t(28947),o=t(23689),x=t(40228),m=t(82080),h=t(25541),p=t(85778);function u(){let[e,s]=(0,r.useState)(null),[t,u]=(0,r.useState)(!0),[f,j]=(0,r.useState)(null),g=async()=>{try{u(!0);let e=await fetch("/api/students/current/dashboard");if(!e.ok)throw Error("Failed to fetch dashboard data");let t=await e.json();s(t),j(null)}catch(e){console.error("Error fetching dashboard data:",e),j("Failed to load dashboard data")}finally{u(!1)}};return t?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsx)(i.A,{className:"h-8 w-8 animate-spin text-blue-600"})}):f?(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:f}),(0,a.jsx)(d.$,{onClick:g,children:"Try Again"})]})}):e?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Student Dashboard"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Welcome back, ",e.student.name,"!"]}),e.currentEnrollment&&(0,a.jsxs)("p",{className:"text-sm text-gray-500",children:["Currently enrolled in ",e.currentEnrollment.courseName," - ",e.currentEnrollment.groupName]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Current Level"}),(0,a.jsx)(c.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)(l.E,{className:"bg-yellow-100 text-yellow-800",children:["Level ",e.student.level]}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:["→ ",e.student.nextLevel]})]}),(0,a.jsxs)("div",{className:"mt-2",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between text-xs",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:["Progress to ",e.student.nextLevel]}),(0,a.jsxs)("span",{className:"font-medium",children:[e.progress.overall,"%"]})]}),(0,a.jsx)("div",{className:"mt-1 w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-blue-500 h-2 rounded-full",style:{width:`${e.progress.overall}%`}})})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Attendance"}),(0,a.jsx)(o.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsxs)("div",{className:"text-2xl font-bold",children:[e.progress.attendance,"%"]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.stats.attendedClasses," of ",e.stats.totalClasses," classes"]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Upcoming Classes"}),(0,a.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.stats.upcomingClasses}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"This week"})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(n.ZB,{className:"text-sm font-medium",children:"Assignments"}),(0,a.jsx)(m.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(n.Wu,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.stats.pendingAssignments}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:"Pending completion"})]})]})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Quick Actions"}),(0,a.jsx)(n.BT,{children:"Access your most used features"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[(0,a.jsxs)(d.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,a.jsx)(x.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"View Schedule"})]}),(0,a.jsxs)(d.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,a.jsx)(m.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Assignments"})]}),(0,a.jsxs)(d.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,a.jsx)(h.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Progress Report"})]}),(0,a.jsxs)(d.$,{variant:"outline",className:"h-20 flex flex-col space-y-2",children:[(0,a.jsx)(p.A,{className:"h-6 w-6"}),(0,a.jsx)("span",{className:"text-sm",children:"Payment History"})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Recent Classes"}),(0,a.jsx)(n.BT,{children:"Your latest class attendance"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsx)("div",{className:"space-y-4",children:e.recentClasses.length>0?e.recentClasses.map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium",children:e.topic}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:e.date})]}),(0,a.jsx)(l.E,{className:"present"===e.status?"bg-green-100 text-green-800":"bg-red-100 text-red-800",children:e.status})]},s)):(0,a.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,a.jsx)(x.A,{className:"h-8 w-8 mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"No recent classes"})]})})})]}),(0,a.jsxs)(n.Zp,{children:[(0,a.jsxs)(n.aR,{children:[(0,a.jsx)(n.ZB,{children:"Payment Status"}),(0,a.jsx)(n.BT,{children:"Your payment information"})]}),(0,a.jsx)(n.Wu,{children:(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Total Amount"}),(0,a.jsxs)("span",{className:"font-medium",children:[e.payments.totalPayments.toLocaleString()," UZS"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Paid Amount"}),(0,a.jsxs)("span",{className:"font-medium text-green-600",children:[e.payments.paidAmount.toLocaleString()," UZS"]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"Pending Amount"}),(0,a.jsxs)("span",{className:"font-medium text-orange-600",children:[e.payments.pendingAmount.toLocaleString()," UZS"]})]}),(0,a.jsxs)("div",{className:"pt-2 border-t",children:[(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,a.jsx)("div",{className:"bg-green-500 h-2 rounded-full",style:{width:`${e.payments.totalPayments>0?e.payments.paidAmount/e.payments.totalPayments*100:0}%`}})}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mt-1",children:[e.payments.totalPayments>0?Math.round(e.payments.paidAmount/e.payments.totalPayments*100):0,"% paid"]})]})]})})]})]})]}):(0,a.jsx)("div",{className:"flex items-center justify-center min-h-[400px]",children:(0,a.jsx)("p",{className:"text-gray-600",children:"No dashboard data available"})})}},55192:(e,s,t)=>{"use strict";t.d(s,{BT:()=>c,Wu:()=>o,ZB:()=>i,Zp:()=>l,aR:()=>d});var a=t(60687),r=t(43210),n=t(96241);let l=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));l.displayName="Card";let d=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));d.displayName="CardHeader";let i=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));i.displayName="CardTitle";let c=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));c.displayName="CardDescription";let o=r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",e),...s}));o.displayName="CardContent",r.forwardRef(({className:e,...s},t)=>(0,a.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},72730:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},98143:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var a=t(65239),r=t(48088),n=t(88170),l=t.n(n),d=t(30893),i={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>d[e]);t.d(s,i);let c={children:["",{children:["(dashboard)",{children:["dashboard",{children:["student",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,43410)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\student\\page.tsx"],x={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/student/page",pathname:"/dashboard/student",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},99145:(e,s,t)=>{Promise.resolve().then(t.bind(t,43410))}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[4243,7615,2918,8887,3039],()=>t(98143));module.exports=a})();