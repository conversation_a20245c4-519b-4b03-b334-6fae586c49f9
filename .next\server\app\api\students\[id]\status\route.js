"use strict";(()=>{var e={};e.id=7561,e.ids=[7561],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},84832:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>q,serverHooks:()=>E,workAsyncStorage:()=>w,workUnitAsyncStorage:()=>A});var s={};t.r(s),t.d(s,{PATCH:()=>m});var n=t(96559),u=t(48088),o=t(37719),a=t(32190),i=t(19854),p=t(41098),d=t(79464),l=t(99326),c=t(45697);let x=c.Ik({status:c.k5(["ACTIVE","DROPPED","PAUSED","COMPLETED"]),currentGroupId:c.Yj().optional(),reEnrollmentNotes:c.Yj().optional(),reason:c.Yj().optional()});async function m(e,{params:r}){try{let t=await (0,i.getServerSession)(p.N);if(!t?.user)return a.NextResponse.json({error:"Unauthorized"},{status:401});if(!t.user.role||!["ADMIN","MANAGER","RECEPTION"].includes(t.user.role))return a.NextResponse.json({error:"Forbidden"},{status:403});let{id:s}=await r,n=await e.json(),u=x.parse(n),o=await d.z.student.findUnique({where:{id:s},include:{user:{select:{name:!0}}}});if(!o)return a.NextResponse.json({error:"Student not found"},{status:404});let c={status:u.status,updatedAt:new Date},m=new Date;switch(u.status){case"DROPPED":c.droppedAt=m,c.currentGroupId=null,c.reEnrollmentNotes=u.reEnrollmentNotes;break;case"PAUSED":c.pausedAt=m;break;case"ACTIVE":c.resumedAt=m,c.droppedAt=null,c.pausedAt=null,u.currentGroupId&&(c.currentGroupId=u.currentGroupId);break;case"COMPLETED":c.currentGroupId=null}let q=await d.z.student.update({where:{id:s},data:c,include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}});return await l._.logStudentStatusChanged(t.user.id,t.user.role,s,{studentName:o.user.name,oldStatus:o.status,newStatus:u.status,reason:u.reason,groupName:"N/A"},e),a.NextResponse.json(q)}catch(e){if(e instanceof c.G)return a.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating student status:",e),a.NextResponse.json({error:"Internal server error"},{status:500})}}let q=new n.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/students/[id]/status/route",pathname:"/api/students/[id]/status",filename:"route",bundlePath:"app/api/students/[id]/status/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\students\\[id]\\status\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:w,workUnitAsyncStorage:A,serverHooks:E}=q;function f(){return(0,o.patchFetch)({workAsyncStorage:w,workUnitAsyncStorage:A})}},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(84832));module.exports=s})();