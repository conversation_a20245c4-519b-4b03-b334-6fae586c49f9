(()=>{var e={};e.id=4639,e.ids=[4639],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,r,t)=>{"use strict";function s(e){let r=e.headers.get("X-Inter-Server-Secret"),t=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!r||!s||r!==s)return!1;if(t){let e=parseInt(t),r=Date.now();if(isNaN(e)||r-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function n(e,r){try{let t="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!t)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${t}${r.endpoint}`,n={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let r=Date.now().toString(),t=o.getServerConfig(),s=`${t.serverType}-${r}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":t.serverType,"X-Request-ID":s,"X-Timestamp":r,"User-Agent":`${t.serverType}-server`}}(),...r.headers},a=await fetch(s,{method:r.method,headers:n,body:r.data?JSON.stringify(r.data):void 0}),i=await a.json();return{success:a.ok,data:i,status:a.status,error:a.ok?void 0:i.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}t.d(r,{LQ:()=>o,cU:()=>a,g2:()=>s});class a{static async authenticateUser(e,r){return n("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:r}})}static async getUserData(e){return n("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,r){return n("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:r}})}}class o{static logRequest(e,r,t,s){let n=new Date().toISOString(),a=process.env.SERVER_TYPE||"unknown";console.log(`[${n}] Inter-Server ${e.toUpperCase()}: ${r}`,{server:a,success:t,details:s})}static async healthCheck(e){try{return(await n(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41098:(e,r,t)=>{"use strict";t.d(r,{N:()=>a});var s=t(13581),n=t(7786);let a={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let r=await n.cU.authenticateUser(e.phone,e.password);if(!r.success)return console.error("Authentication failed:",r.error),null;let t=r.data.user;if(!t)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(t.role))return console.error("User role not allowed on staff server:",t.role),null;return{id:t.id,phone:t.phone,name:t.name,email:t.email,role:t.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:r})=>(r&&(e.role=r.role||null),e),session:async({session:e,token:r})=>(r&&(e.user.id=r.sub,e.user.role=r.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},62834:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>h,serverHooks:()=>E,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>p});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),u=t(19854),c=t(41098),l=t(79464);async function p(e,{params:r}){try{let{id:t}=await r,s=await (0,u.getServerSession)(c.N);if(!s?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if(!s.user.role||!["ADMIN","MANAGER"].includes(s.user.role)&&s.user.id!==t)return i.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:n}=new URL(e.url),a=n.get("period")||"30",o=new Date;o.setDate(o.getDate()-parseInt(a));let p=await l.z.teacher.findUnique({where:{id:t},include:{user:!0}});if(!p)return i.NextResponse.json({error:"Teacher not found"},{status:404});let h=await l.z.group.findMany({where:{teacherId:t},include:{enrollments:{include:{student:{include:{user:!0,payments:!0}}}}}}),m=await d(t,o,h);return i.NextResponse.json({teacher:{id:p.id,name:p.user.name,email:p.user.email},period:`${a} days`,kpis:m})}catch(e){return console.error("Error fetching teacher KPIs:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e,r,t){let s=await l.z.enrollment.count({where:{group:{teacherId:e},createdAt:{gte:r}}}),n=await l.z.payment.aggregate({where:{student:{enrollments:{some:{group:{teacherId:e}}}},createdAt:{gte:r},status:"PAID"},_sum:{amount:!0},_count:!0}),a=await l.z.enrollment.count({where:{group:{teacherId:e},status:{in:["ACTIVE","COMPLETED","DROPPED"]}}}),o=await l.z.enrollment.count({where:{group:{teacherId:e},status:"ACTIVE"}}),i=(await l.z.student.findMany({where:{enrollments:{some:{group:{teacherId:e}}}},include:{assessments:{where:{completedAt:{gte:r},passed:!0,type:{in:["LEVEL_TEST","FINAL_EXAM"]}},orderBy:{completedAt:"desc"}}}})).filter(e=>e.assessments.length>0).length,u=await l.z.assessment.aggregate({where:{student:{enrollments:{some:{group:{teacherId:e}}}},completedAt:{gte:r},score:{not:null}},_avg:{score:!0},_count:!0}),c=await l.z.class.aggregate({where:{group:{teacherId:e},date:{gte:r}},_count:!0});return{newStudents:{count:s,label:"New Students Acquired"},payments:{totalAmount:n._sum.amount||0,count:n._count,label:"Payments Collected"},retention:{rate:Math.round(100*(a>0?o/a*100:0))/100,activeStudents:o,totalStudents:a,label:"Retention Rate (%)"},progress:{rate:Math.round(100*(a>0?i/a*100:0))/100,studentsWithProgress:i,totalStudents:a,label:"Progress Rate (%)"},testPerformance:{averageScore:u._avg.score?Math.round(100*u._avg.score)/100:0,testsCompleted:u._count,label:"Average Test Score"},classActivity:{classesHeld:c._count,label:"Classes Held"}}}let h=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/teachers/[id]/kpis/route",pathname:"/api/teachers/[id]/kpis",filename:"route",bundlePath:"app/api/teachers/[id]/kpis/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\teachers\\[id]\\kpis\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:E}=h;function v(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3412],()=>t(62834));module.exports=s})();