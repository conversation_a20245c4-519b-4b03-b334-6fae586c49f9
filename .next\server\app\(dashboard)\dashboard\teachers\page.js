(()=>{var e={};e.id=1145,e.ids=[1145],e.modules={3018:(e,s,a)=>{"use strict";a.d(s,{Fc:()=>d,TN:()=>o});var t=a(60687),r=a(43210),n=a(24224),l=a(96241);let i=(0,n.F)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),d=r.forwardRef(({className:e,variant:s,...a},r)=>(0,t.jsx)("div",{ref:r,role:"alert",className:(0,l.cn)(i({variant:s}),e),...a}));d.displayName="Alert",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h5",{ref:a,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...s})).displayName="AlertTitle";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...s}));o.displayName="AlertDescription"},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},9923:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11190:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>F});var t=a(60687),r=a(43210),n=a(55192),l=a(24934),i=a(59821),d=a(68988),o=a(96752),c=a(37826),m=a(3018),u=a(72730),h=a(96474),x=a(58869),p=a(27351),f=a(41312),j=a(99270),g=a(48340),b=a(41550),N=a(82080),y=a(9923),v=a(88233),w=a(27605),C=a(63442),A=a(9275),E=a(39390),R=a(63974),k=a(86561),T=a(96545);let L=A.Ik({name:A.Yj().min(2,"Name must be at least 2 characters").optional(),phone:A.Yj().min(9,"Phone number must be at least 9 characters").optional(),email:A.Yj().email("Invalid email address").optional().or(A.eu("")),userId:A.Yj().optional(),subject:A.Yj().min(1,"Subject is required"),experience:A.ai().min(0,"Experience cannot be negative").optional(),branch:A.Yj().min(1,"Branch is required"),tier:A.k5(["A_LEVEL","B_LEVEL","C_LEVEL","NEW"]).default("NEW")}),_=["English Language","IELTS Preparation","SAT Preparation","Mathematics","Kids English","Business English","Academic English","Conversation English","Grammar & Writing","Speaking & Listening"],P=["Main Branch","Branch"],D=[{value:"A_LEVEL",label:"A-level Teacher",description:"Highest priority, experienced teacher"},{value:"B_LEVEL",label:"B-level Teacher",description:"Experienced teacher"},{value:"C_LEVEL",label:"C-level Teacher",description:"Developing teacher"},{value:"NEW",label:"New Teacher",description:"New or trainee teacher"}];function S({initialData:e,onSubmit:s,onCancel:a,isEditing:i=!1,existingUsers:o=[]}){let{currentBranch:c}=(0,T.O)(),[h,p]=(0,r.useState)(!1),[f,j]=(0,r.useState)(null),[y,v]=(0,r.useState)(!!e?.userId),{register:A,handleSubmit:S,setValue:F,watch:I,formState:{errors:B}}=(0,w.mN)({resolver:(0,C.u)(L),defaultValues:{name:e?.name||"",phone:e?.phone||"",email:e?.email||"",userId:e?.userId||"",subject:e?.subject||"",experience:e?.experience||0,branch:e?.branch||c.name,tier:e?.tier||"NEW"}}),M=I("userId"),O=I("subject"),V=I("branch"),W=I("tier"),U=async e=>{p(!0),j(null);try{await s(e)}catch(e){j(e instanceof Error?e.message:"An error occurred")}finally{p(!1)}};return(0,t.jsxs)(n.Zp,{className:"w-full max-w-2xl mx-auto",children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{className:"flex items-center",children:[(0,t.jsx)(x.A,{className:"h-5 w-5 mr-2"}),i?"Edit Teacher":"Add New Teacher"]}),(0,t.jsx)(n.BT,{children:i?"Update teacher information":"Enter teacher details to create a new profile"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("form",{onSubmit:S(U),className:"space-y-6",children:[f&&(0,t.jsx)(m.Fc,{variant:"destructive",children:(0,t.jsx)(m.TN,{children:f})}),!i&&(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"User Account"})]}),(0,t.jsxs)("div",{className:"flex space-x-4 mb-4",children:[(0,t.jsx)(l.$,{type:"button",variant:y?"outline":"default",onClick:()=>v(!1),children:"Create New User"}),(0,t.jsx)(l.$,{type:"button",variant:y?"default":"outline",onClick:()=>v(!0),children:"Use Existing User"})]}),y?(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"userId",children:"Select User *"}),(0,t.jsxs)(R.l6,{value:M,onValueChange:e=>F("userId",e),children:[(0,t.jsx)(R.bq,{children:(0,t.jsx)(R.yv,{placeholder:"Select an existing user"})}),(0,t.jsx)(R.gC,{children:o.map(e=>(0,t.jsxs)(R.eb,{value:e.id,children:[e.name," - ",e.phone]},e.id))})]})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"name",children:"Full Name *"}),(0,t.jsx)(d.p,{id:"name",...A("name"),placeholder:"Enter full name",className:B.name?"border-red-500":""}),B.name&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"phone",children:"Phone Number *"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(g.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(d.p,{id:"phone",...A("phone"),placeholder:"+998 90 123 45 67",className:`pl-10 ${B.phone?"border-red-500":""}`})]}),B.phone&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.phone.message})]}),(0,t.jsxs)("div",{className:"space-y-2 md:col-span-2",children:[(0,t.jsx)(E.J,{htmlFor:"email",children:"Email Address"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(b.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(d.p,{id:"email",type:"email",...A("email"),placeholder:"<EMAIL>",className:`pl-10 ${B.email?"border-red-500":""}`})]}),B.email&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.email.message})]})]})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-4",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 text-gray-500"}),(0,t.jsx)("h3",{className:"text-lg font-medium",children:"Professional Information"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"subject",children:"Subject/Specialization *"}),(0,t.jsxs)(R.l6,{value:O,onValueChange:e=>F("subject",e),children:[(0,t.jsx)(R.bq,{className:B.subject?"border-red-500":"",children:(0,t.jsx)(R.yv,{placeholder:"Select subject"})}),(0,t.jsx)(R.gC,{children:_.map(e=>(0,t.jsx)(R.eb,{value:e,children:e},e))})]}),B.subject&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.subject.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"branch",children:"Branch *"}),(0,t.jsxs)(R.l6,{value:V,onValueChange:e=>F("branch",e),children:[(0,t.jsx)(R.bq,{className:B.branch?"border-red-500":"",children:(0,t.jsx)(R.yv,{placeholder:"Select branch"})}),(0,t.jsx)(R.gC,{children:P.map(e=>(0,t.jsx)(R.eb,{value:e,children:e},e))})]}),B.branch&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.branch.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"experience",children:"Years of Experience"}),(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(k.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(d.p,{id:"experience",type:"number",min:"0",step:"0.5",...A("experience",{valueAsNumber:!0}),placeholder:"0",className:`pl-10 ${B.experience?"border-red-500":""}`})]}),B.experience&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.experience.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)(E.J,{htmlFor:"tier",children:"Teacher Tier *"}),(0,t.jsxs)(R.l6,{value:W,onValueChange:e=>F("tier",e),children:[(0,t.jsx)(R.bq,{className:B.tier?"border-red-500":"",children:(0,t.jsx)(R.yv,{placeholder:"Select tier"})}),(0,t.jsx)(R.gC,{children:D.map(e=>(0,t.jsx)(R.eb,{value:e.value,children:(0,t.jsxs)("div",{className:"flex flex-col",children:[(0,t.jsx)("span",{className:"font-medium",children:e.label}),(0,t.jsx)("span",{className:"text-xs text-gray-500",children:e.description})]})},e.value))})]}),B.tier&&(0,t.jsx)("p",{className:"text-sm text-red-500",children:B.tier.message})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4 pt-6 border-t",children:[a&&(0,t.jsx)(l.$,{type:"button",variant:"outline",onClick:a,children:"Cancel"}),(0,t.jsxs)(l.$,{type:"submit",disabled:h,children:[h&&(0,t.jsx)(u.A,{className:"mr-2 h-4 w-4 animate-spin"}),i?"Update Teacher":"Create Teacher"]})]})]})})]})}function F(){let{currentBranch:e}=(0,T.O)(),[s,a]=(0,r.useState)([]),[w,C]=(0,r.useState)({totalTeachers:0,totalGroups:0,totalClasses:0,totalStudents:0}),[A,E]=(0,r.useState)(!0),[R,k]=(0,r.useState)(""),[L,_]=(0,r.useState)(!1),[P,D]=(0,r.useState)(!1),[F,I]=(0,r.useState)(null),[B,M]=(0,r.useState)(!1),[O,V]=(0,r.useState)(null),W=async()=>{if(e?.id)try{E(!0);let s=await fetch(`/api/teachers?branch=${e.id}`),t=await s.json();a(t.teachers||[]),V(null)}catch(e){console.error("Error fetching teachers:",e),V("Failed to fetch teachers")}finally{E(!1)}},U=async s=>{M(!0),V(null);try{let a=s.userId;if(!a){let e=await fetch("/api/users",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:s.name,phone:s.phone,email:s.email||null,role:"TEACHER",password:"defaultPassword123"})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to create user")}a=(await e.json()).id}let t=await fetch("/api/teachers",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({userId:a,subject:s.subject,experience:s.experience,branch:e.id,tier:s.tier})});if(!t.ok){let e=await t.json();throw Error(e.error||"Failed to create teacher")}_(!1),W()}catch(e){V(e instanceof Error?e.message:"An error occurred")}finally{M(!1)}},q=async e=>{if(F){M(!0),V(null);try{let s=await fetch(`/api/teachers/${F.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({subject:e.subject,experience:e.experience,branch:e.branch,tier:e.tier})});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to update teacher")}D(!1),I(null),W()}catch(e){V(e instanceof Error?e.message:"An error occurred")}finally{M(!1)}}},Z=async e=>{if(confirm("Are you sure you want to delete this teacher? This action cannot be undone."))try{let s=await fetch(`/api/teachers/${e}`,{method:"DELETE"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to delete teacher")}W()}catch(e){V(e instanceof Error?e.message:"An error occurred")}},$=s.filter(e=>e.user.name.toLowerCase().includes(R.toLowerCase())||e.user.phone.includes(R)||e.user.email?.toLowerCase().includes(R.toLowerCase())||e.subject.toLowerCase().includes(R.toLowerCase())||e.branch.toLowerCase().includes(R.toLowerCase())),J=e=>e?e<2?"bg-yellow-100 text-yellow-800":e<5?"bg-blue-100 text-blue-800":"bg-green-100 text-green-800":"bg-gray-100 text-gray-800",G=e=>e?e<2?"Junior":e<5?"Mid-level":"Senior":"New",z=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},H=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";default:return"New"}};return A?(0,t.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,t.jsx)(u.A,{className:"h-8 w-8 animate-spin"}),(0,t.jsx)("span",{className:"ml-2",children:"Loading teachers..."})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[O&&(0,t.jsx)(m.Fc,{variant:"destructive",children:(0,t.jsx)(m.TN,{children:O})}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Teachers Management - ",e?.name]}),(0,t.jsxs)("p",{className:"text-gray-600",children:["Manage teaching staff for ",e?.name]})]}),(0,t.jsxs)(c.lG,{open:L,onOpenChange:_,children:[(0,t.jsx)(c.zM,{asChild:!0,children:(0,t.jsxs)(l.$,{children:[(0,t.jsx)(h.A,{className:"h-4 w-4 mr-2"}),"Add Teacher"]})}),(0,t.jsxs)(c.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(c.c7,{children:[(0,t.jsx)(c.L3,{children:"Add New Teacher"}),(0,t.jsx)(c.rr,{children:"Create a new teacher profile with their professional information."})]}),(0,t.jsx)(S,{onSubmit:U,onCancel:()=>_(!1),isEditing:!1})]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Teachers"}),(0,t.jsx)(x.A,{className:"h-4 w-4 text-muted-foreground"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:w.totalTeachers}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active teaching staff"})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Groups"}),(0,t.jsx)(p.A,{className:"h-4 w-4 text-green-600"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:w.totalGroups}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Active teaching groups"})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,t.jsx)(n.ZB,{className:"text-sm font-medium",children:"Total Classes"}),(0,t.jsx)(f.A,{className:"h-4 w-4 text-purple-600"})]}),(0,t.jsxs)(n.Wu,{children:[(0,t.jsx)("div",{className:"text-2xl font-bold",children:w.totalClasses}),(0,t.jsx)("p",{className:"text-xs text-muted-foreground",children:"Classes conducted"})]})]})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsx)(n.aR,{children:(0,t.jsx)(n.ZB,{children:"Search Teachers"})}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,t.jsx)(d.p,{placeholder:"Search by name, phone, email, subject, or branch...",value:R,onChange:e=>k(e.target.value),className:"pl-10"})]})})]}),(0,t.jsxs)(n.Zp,{children:[(0,t.jsxs)(n.aR,{children:[(0,t.jsxs)(n.ZB,{children:["Teachers (",$.length,")"]}),(0,t.jsx)(n.BT,{children:"Complete list of teaching staff"})]}),(0,t.jsx)(n.Wu,{children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"Teacher"}),(0,t.jsx)(o.nd,{children:"Subject"}),(0,t.jsx)(o.nd,{children:"Tier"}),(0,t.jsx)(o.nd,{children:"Experience"}),(0,t.jsx)(o.nd,{children:"Branch"}),(0,t.jsx)(o.nd,{children:"Groups"}),(0,t.jsx)(o.nd,{children:"Classes"}),(0,t.jsx)(o.nd,{children:"Actions"})]})}),(0,t.jsx)(o.BF,{children:$.map(e=>(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"flex-shrink-0",children:(0,t.jsx)("div",{className:"h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center",children:(0,t.jsx)(x.A,{className:"h-5 w-5 text-gray-600"})})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.user.name}),(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,t.jsx)(g.A,{className:"h-3 w-3 mr-1"}),e.user.phone]}),e.user.email&&(0,t.jsxs)("div",{className:"text-sm text-gray-500 flex items-center",children:[(0,t.jsx)(b.A,{className:"h-3 w-3 mr-1"}),e.user.email]})]})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"h-4 w-4 mr-2 text-blue-600"}),(0,t.jsx)("span",{className:"font-medium",children:e.subject})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsx)(i.E,{className:z(e.tier),children:H(e.tier)})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)(i.E,{className:J(e.experience),children:[e.experience?`${e.experience}y`:"0y"," - ",G(e.experience)]})}),(0,t.jsx)(o.nA,{children:(0,t.jsx)("span",{className:"text-sm font-medium",children:e.branch})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(p.A,{className:"h-4 w-4 mr-1 text-green-600"}),(0,t.jsx)("span",{className:"font-medium",children:e._count.groups})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(f.A,{className:"h-4 w-4 mr-1 text-purple-600"}),(0,t.jsx)("span",{className:"font-medium",children:e._count.classes})]})}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>{I(e),D(!0)},children:(0,t.jsx)(y.A,{className:"h-4 w-4"})}),(0,t.jsx)(l.$,{variant:"outline",size:"sm",onClick:()=>Z(e.id),className:"text-red-600 hover:text-red-700",children:(0,t.jsx)(v.A,{className:"h-4 w-4"})})]})})]},e.id))})]})})]}),(0,t.jsx)(c.lG,{open:P,onOpenChange:D,children:(0,t.jsxs)(c.Cf,{className:"max-w-2xl",children:[(0,t.jsxs)(c.c7,{children:[(0,t.jsx)(c.L3,{children:"Edit Teacher"}),(0,t.jsx)(c.rr,{children:"Update teacher information and professional details."})]}),F&&(0,t.jsx)(S,{initialData:{name:F.user.name,phone:F.user.phone,email:F.user.email||"",userId:F.userId,subject:F.subject,experience:F.experience||0,branch:F.branch,tier:F.tier||"NEW"},onSubmit:q,onCancel:()=>{D(!1),I(null)},isEditing:!0})]})})]})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26134:(e,s,a)=>{"use strict";a.d(s,{G$:()=>z,Hs:()=>y,UC:()=>ea,VY:()=>er,ZL:()=>ee,bL:()=>X,bm:()=>en,hE:()=>et,hJ:()=>es,l9:()=>Q});var t=a(43210),r=a(70569),n=a(98599),l=a(11273),i=a(96963),d=a(65551),o=a(31355),c=a(32547),m=a(25028),u=a(46059),h=a(14163),x=a(1359),p=a(42247),f=a(63376),j=a(8730),g=a(60687),b="Dialog",[N,y]=(0,l.A)(b),[v,w]=N(b),C=e=>{let{__scopeDialog:s,children:a,open:r,defaultOpen:n,onOpenChange:l,modal:o=!0}=e,c=t.useRef(null),m=t.useRef(null),[u,h]=(0,d.i)({prop:r,defaultProp:n??!1,onChange:l,caller:b});return(0,g.jsx)(v,{scope:s,triggerRef:c,contentRef:m,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:u,onOpenChange:h,onOpenToggle:t.useCallback(()=>h(e=>!e),[h]),modal:o,children:a})};C.displayName=b;var A="DialogTrigger",E=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,l=w(A,a),i=(0,n.s)(s,l.triggerRef);return(0,g.jsx)(h.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":J(l.open),...t,ref:i,onClick:(0,r.m)(e.onClick,l.onOpenToggle)})});E.displayName=A;var R="DialogPortal",[k,T]=N(R,{forceMount:void 0}),L=e=>{let{__scopeDialog:s,forceMount:a,children:r,container:n}=e,l=w(R,s);return(0,g.jsx)(k,{scope:s,forceMount:a,children:t.Children.map(r,e=>(0,g.jsx)(u.C,{present:a||l.open,children:(0,g.jsx)(m.Z,{asChild:!0,container:n,children:e})}))})};L.displayName=R;var _="DialogOverlay",P=t.forwardRef((e,s)=>{let a=T(_,e.__scopeDialog),{forceMount:t=a.forceMount,...r}=e,n=w(_,e.__scopeDialog);return n.modal?(0,g.jsx)(u.C,{present:t||n.open,children:(0,g.jsx)(S,{...r,ref:s})}):null});P.displayName=_;var D=(0,j.TL)("DialogOverlay.RemoveScroll"),S=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,r=w(_,a);return(0,g.jsx)(p.A,{as:D,allowPinchZoom:!0,shards:[r.contentRef],children:(0,g.jsx)(h.sG.div,{"data-state":J(r.open),...t,ref:s,style:{pointerEvents:"auto",...t.style}})})}),F="DialogContent",I=t.forwardRef((e,s)=>{let a=T(F,e.__scopeDialog),{forceMount:t=a.forceMount,...r}=e,n=w(F,e.__scopeDialog);return(0,g.jsx)(u.C,{present:t||n.open,children:n.modal?(0,g.jsx)(B,{...r,ref:s}):(0,g.jsx)(M,{...r,ref:s})})});I.displayName=F;var B=t.forwardRef((e,s)=>{let a=w(F,e.__scopeDialog),l=t.useRef(null),i=(0,n.s)(s,a.contentRef,l);return t.useEffect(()=>{let e=l.current;if(e)return(0,f.Eq)(e)},[]),(0,g.jsx)(O,{...e,ref:i,trapFocus:a.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,r.m)(e.onCloseAutoFocus,e=>{e.preventDefault(),a.triggerRef.current?.focus()}),onPointerDownOutside:(0,r.m)(e.onPointerDownOutside,e=>{let s=e.detail.originalEvent,a=0===s.button&&!0===s.ctrlKey;(2===s.button||a)&&e.preventDefault()}),onFocusOutside:(0,r.m)(e.onFocusOutside,e=>e.preventDefault())})}),M=t.forwardRef((e,s)=>{let a=w(F,e.__scopeDialog),r=t.useRef(!1),n=t.useRef(!1);return(0,g.jsx)(O,{...e,ref:s,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:s=>{e.onCloseAutoFocus?.(s),s.defaultPrevented||(r.current||a.triggerRef.current?.focus(),s.preventDefault()),r.current=!1,n.current=!1},onInteractOutside:s=>{e.onInteractOutside?.(s),s.defaultPrevented||(r.current=!0,"pointerdown"===s.detail.originalEvent.type&&(n.current=!0));let t=s.target;a.triggerRef.current?.contains(t)&&s.preventDefault(),"focusin"===s.detail.originalEvent.type&&n.current&&s.preventDefault()}})}),O=t.forwardRef((e,s)=>{let{__scopeDialog:a,trapFocus:r,onOpenAutoFocus:l,onCloseAutoFocus:i,...d}=e,m=w(F,a),u=t.useRef(null),h=(0,n.s)(s,u);return(0,x.Oh)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(c.n,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,g.jsx)(o.qW,{role:"dialog",id:m.contentId,"aria-describedby":m.descriptionId,"aria-labelledby":m.titleId,"data-state":J(m.open),...d,ref:h,onDismiss:()=>m.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(Y,{titleId:m.titleId}),(0,g.jsx)(K,{contentRef:u,descriptionId:m.descriptionId})]})]})}),V="DialogTitle",W=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,r=w(V,a);return(0,g.jsx)(h.sG.h2,{id:r.titleId,...t,ref:s})});W.displayName=V;var U="DialogDescription",q=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,r=w(U,a);return(0,g.jsx)(h.sG.p,{id:r.descriptionId,...t,ref:s})});q.displayName=U;var Z="DialogClose",$=t.forwardRef((e,s)=>{let{__scopeDialog:a,...t}=e,n=w(Z,a);return(0,g.jsx)(h.sG.button,{type:"button",...t,ref:s,onClick:(0,r.m)(e.onClick,()=>n.onOpenChange(!1))})});function J(e){return e?"open":"closed"}$.displayName=Z;var G="DialogTitleWarning",[z,H]=(0,l.q)(G,{contentName:F,titleName:V,docsSlug:"dialog"}),Y=({titleId:e})=>{let s=H(G),a=`\`${s.contentName}\` requires a \`${s.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${s.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${s.docsSlug}`;return t.useEffect(()=>{e&&(document.getElementById(e)||console.error(a))},[a,e]),null},K=({contentRef:e,descriptionId:s})=>{let a=H("DialogDescriptionWarning"),r=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${a.contentName}}.`;return t.useEffect(()=>{let a=e.current?.getAttribute("aria-describedby");s&&a&&(document.getElementById(s)||console.warn(r))},[r,e,s]),null},X=C,Q=E,ee=L,es=P,ea=I,et=W,er=q,en=$},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},37826:(e,s,a)=>{"use strict";a.d(s,{Cf:()=>u,L3:()=>x,c7:()=>h,lG:()=>d,rr:()=>p,zM:()=>o});var t=a(60687),r=a(43210),n=a(26134),l=a(11860),i=a(96241);let d=n.bL,o=n.l9,c=n.ZL;n.bm;let m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.hJ,{ref:a,className:(0,i.cn)("fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...s}));m.displayName=n.hJ.displayName;let u=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(c,{children:[(0,t.jsx)(m,{}),(0,t.jsx)(n.UC,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg max-h-[90vh] overflow-y-auto",e),...a,children:(0,t.jsxs)("div",{className:"relative",children:[s,(0,t.jsxs)(n.bm,{className:"absolute right-0 top-0 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,t.jsx)(l.A,{className:"h-4 w-4"}),(0,t.jsx)("span",{className:"sr-only",children:"Close"})]})]})})]}));u.displayName=n.UC.displayName;let h=({className:e,...s})=>(0,t.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...s});h.displayName="DialogHeader";let x=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.hE,{ref:a,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...s}));x.displayName=n.hE.displayName;let p=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.VY,{ref:a,className:(0,i.cn)("text-sm text-muted-foreground",e),...s}));p.displayName=n.VY.displayName},39390:(e,s,a)=>{"use strict";a.d(s,{J:()=>o});var t=a(60687),r=a(43210),n=a(78148),l=a(24224),i=a(96241);let d=(0,l.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.b,{ref:a,className:(0,i.cn)(d(),e),...s}));o.displayName=n.b.displayName},40391:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>o});var t=a(65239),r=a(48088),n=a(88170),l=a.n(n),i=a(30893),d={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);a.d(s,d);let o={children:["",{children:["(dashboard)",{children:["dashboard",{children:["teachers",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,61782)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(a.bind(a,71934)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]},{layout:[()=>Promise.resolve().then(a.bind(a,58014)),"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},u=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/(dashboard)/dashboard/teachers/page",pathname:"/dashboard/teachers",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},41550:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},51639:(e,s,a)=>{Promise.resolve().then(a.bind(a,11190))},55192:(e,s,a)=>{"use strict";a.d(s,{BT:()=>o,Wu:()=>c,ZB:()=>d,Zp:()=>l,aR:()=>i});var t=a(60687),r=a(43210),n=a(96241);let l=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("rounded-xl border bg-card text-card-foreground shadow-sm hover:shadow-md transition-all duration-200 backdrop-blur-sm",e),...s}));l.displayName="Card";let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",e),...s}));i.displayName="CardHeader";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("h3",{ref:a,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",e),...s}));d.displayName="CardTitle";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("p",{ref:a,className:(0,n.cn)("text-sm text-muted-foreground",e),...s}));o.displayName="CardDescription";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("p-6 pt-0",e),...s}));c.displayName="CardContent",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{ref:a,className:(0,n.cn)("flex items-center p-6 pt-0",e),...s})).displayName="CardFooter"},58869:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},61782:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\codes\\\\inno-crm-staff\\\\app\\\\(dashboard)\\\\dashboard\\\\teachers\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\(dashboard)\\dashboard\\teachers\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63974:(e,s,a)=>{"use strict";a.d(s,{bq:()=>u,eb:()=>f,gC:()=>p,l6:()=>c,yv:()=>m});var t=a(60687),r=a(43210),n=a(22670),l=a(78272),i=a(3589),d=a(13964),o=a(96241);let c=n.bL;n.YJ;let m=n.WT,u=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(n.l9,{ref:r,className:(0,o.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",e),...a,children:[s,(0,t.jsx)(n.In,{asChild:!0,children:(0,t.jsx)(l.A,{className:"h-4 w-4 opacity-50"})})]}));u.displayName=n.l9.displayName;let h=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.PP,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(i.A,{className:"h-4 w-4"})}));h.displayName=n.PP.displayName;let x=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.wn,{ref:a,className:(0,o.cn)("flex cursor-default items-center justify-center py-1",e),...s,children:(0,t.jsx)(l.A,{className:"h-4 w-4"})}));x.displayName=n.wn.displayName;let p=r.forwardRef(({className:e,children:s,position:a="popper",...r},l)=>(0,t.jsx)(n.ZL,{children:(0,t.jsxs)(n.UC,{ref:l,className:(0,o.cn)("relative z-50 max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2","popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",e),position:a,...r,children:[(0,t.jsx)(h,{}),(0,t.jsx)(n.LM,{className:(0,o.cn)("p-1","popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:s}),(0,t.jsx)(x,{})]})}));p.displayName=n.UC.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.JU,{ref:a,className:(0,o.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",e),...s})).displayName=n.JU.displayName;let f=r.forwardRef(({className:e,children:s,...a},r)=>(0,t.jsxs)(n.q7,{ref:r,className:(0,o.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...a,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(n.VF,{children:(0,t.jsx)(d.A,{className:"h-4 w-4"})})}),(0,t.jsx)(n.p4,{children:s})]}));f.displayName=n.q7.displayName,r.forwardRef(({className:e,...s},a)=>(0,t.jsx)(n.wv,{ref:a,className:(0,o.cn)("-mx-1 my-1 h-px bg-muted",e),...s})).displayName=n.wv.displayName},68988:(e,s,a)=>{"use strict";a.d(s,{p:()=>l});var t=a(60687),r=a(43210),n=a(96241);let l=r.forwardRef(({className:e,type:s,...a},r)=>(0,t.jsx)("input",{type:s,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:r,...a}));l.displayName="Input"},72730:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},88233:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},91391:(e,s,a)=>{Promise.resolve().then(a.bind(a,61782))},96474:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(18962).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96752:(e,s,a)=>{"use strict";a.d(s,{A0:()=>i,BF:()=>d,Hj:()=>o,XI:()=>l,nA:()=>m,nd:()=>c});var t=a(60687),r=a(43210),n=a(96241);let l=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("div",{className:"relative w-full overflow-auto",children:(0,t.jsx)("table",{ref:a,className:(0,n.cn)("w-full caption-bottom text-sm",e),...s})}));l.displayName="Table";let i=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("thead",{ref:a,className:(0,n.cn)("[&_tr]:border-b",e),...s}));i.displayName="TableHeader";let d=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tbody",{ref:a,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...s}));d.displayName="TableBody",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tfoot",{ref:a,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...s})).displayName="TableFooter";let o=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("tr",{ref:a,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...s}));o.displayName="TableRow";let c=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("th",{ref:a,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...s}));c.displayName="TableHead";let m=r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("td",{ref:a,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...s}));m.displayName="TableCell",r.forwardRef(({className:e,...s},a)=>(0,t.jsx)("caption",{ref:a,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...s})).displayName="TableCaption"}};var s=require("../../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[4243,7615,2918,8887,8706,7825,3039],()=>a(40391));module.exports=t})();