(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8497],{2714:(e,s,a)=>{"use strict";a.d(s,{J:()=>d});var l=a(5155),t=a(2115),r=a(968),n=a(2085),i=a(3999);let c=(0,n.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.b,{ref:s,className:(0,i.cn)(c(),a),...t})});d.displayName=r.b.displayName},4964:(e,s,a)=>{"use strict";a.d(s,{Xi:()=>d,av:()=>o,j7:()=>c,tU:()=>i});var l=a(5155),t=a(2115),r=a(704),n=a(3999);let i=r.bL,c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.B8,{ref:s,className:(0,n.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...t})});c.displayName=r.B8.displayName;let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.l9,{ref:s,className:(0,n.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...t})});d.displayName=r.l9.displayName;let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)(r.UC,{ref:s,className:(0,n.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...t})});o.displayName=r.UC.displayName},5990:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>z});var l=a(5155),t=a(2115),r=a(7705),n=a(7168),i=a(4964),c=a(9026),d=a(3904),o=a(8533),x=a(9022),h=a(9852),m=a(2714),u=a(8482),j=a(9074),g=a(6474),f=a(4416),p=a(3999);let v=[{value:"today",label:"Today"},{value:"yesterday",label:"Yesterday"},{value:"last7days",label:"Last 7 days"},{value:"last30days",label:"Last 30 days"},{value:"custom",label:"Custom range"}];function N(e){let{onFilterChange:s,currentFilter:a}=e,[r,i]=(0,t.useState)(!1),[c,d]=(0,t.useState)(a||""),[o,x]=(0,t.useState)(""),[N,b]=(0,t.useState)(""),[y,w]=(0,t.useState)(!1),A=e=>{if(d(e),"custom"===e)return void w(!0);w(!1),s({dateFilter:e}),i(!1)};return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsxs)(n.$,{variant:"outline",onClick:()=>i(!r),className:"flex items-center gap-2 min-w-[200px] justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(j.A,{className:"h-4 w-4"}),(0,l.jsx)("span",{className:"truncate",children:(()=>{if(!c)return"All time";let e=v.find(e=>e.value===c);return e&&"custom"!==e.value?e.label:o&&N?"".concat((0,p.Yq)(o)," - ").concat((0,p.Yq)(N)):"Select date range"})()})]}),(0,l.jsx)(g.A,{className:"h-4 w-4"})]}),r&&(0,l.jsx)(u.Zp,{className:"absolute top-full left-0 mt-1 z-50 min-w-[300px]",children:(0,l.jsx)(u.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsx)("h4",{className:"font-medium",children:"Filter by date"}),(0,l.jsx)(n.$,{variant:"ghost",size:"sm",onClick:()=>i(!1),className:"h-6 w-6 p-0",children:(0,l.jsx)(f.A,{className:"h-4 w-4"})})]}),(0,l.jsx)("div",{className:"space-y-2",children:v.map(e=>(0,l.jsx)(n.$,{variant:c===e.value?"default":"ghost",className:"w-full justify-start",onClick:()=>A(e.value),children:e.label},e.value))}),y&&(0,l.jsxs)("div",{className:"space-y-3 pt-3 border-t",children:[(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-2",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"start-date",className:"text-xs",children:"Start Date"}),(0,l.jsx)(h.p,{id:"start-date",type:"date",value:o,onChange:e=>x(e.target.value),className:"text-sm"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"end-date",className:"text-xs",children:"End Date"}),(0,l.jsx)(h.p,{id:"end-date",type:"date",value:N,onChange:e=>b(e.target.value),className:"text-sm"})]})]}),(0,l.jsx)(n.$,{onClick:()=>{o&&N&&(s({startDate:o,endDate:N}),i(!1),w(!1))},disabled:!o||!N,className:"w-full",size:"sm",children:"Apply Custom Range"})]}),c&&(0,l.jsx)("div",{className:"pt-3 border-t",children:(0,l.jsx)(n.$,{variant:"outline",onClick:()=>{d(""),x(""),b(""),w(!1),s({}),i(!1)},className:"w-full",size:"sm",children:"Clear Filter"})})]})})})]})}var b=a(8145),y=a(9840),w=a(8524),A=a(1007),C=a(9420),E=a(4186),S=a(2178),L=a(5690),k=a(1788),F=a(7580),T=a(9474),R=a(6076),_=a(8979),D=a(9588);function O(e){let{leadId:s,leadName:a,leadPhone:r,onCallComplete:i,onError:c}=e,[d,o]=(0,t.useState)(!1),[x,h]=(0,t.useState)(0),[j,g]=(0,t.useState)(""),[f,p]=(0,t.useState)(!1),[v,N]=(0,t.useState)(!1),y=(0,t.useRef)(null),w=()=>{y.current=setInterval(()=>{h(e=>e+1)},1e3)},A=()=>{y.current&&(clearInterval(y.current),y.current=null)},S=(0,t.useCallback)(async()=>{p(!0),A(),N(!1);try{let e=v?"https://example.com/recordings/call-".concat(s,"-").concat(Date.now(),".mp3"):void 0,a=await fetch("/api/leads/".concat(s,"/call"),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({duration:x,notes:j,recordingUrl:e})});if(!a.ok){let e=await a.json();throw Error(e.error||"Failed to end call")}o(!1),h(0),i()}catch(e){c(e instanceof Error?e.message:"Failed to end call"),d&&w()}finally{p(!1)}},[s,x,j,i,c,d,v]);(0,t.useEffect)(()=>{d&&x>=300&&S()},[x,d,300,S]),(0,t.useEffect)(()=>()=>{y.current&&clearInterval(y.current)},[]);let L=async()=>{p(!0);try{let e=await fetch("/api/leads/".concat(s,"/call"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({notes:j})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to start call")}o(!0),h(0),N(!0),w()}catch(e){c(e instanceof Error?e.message:"Failed to start call")}finally{p(!1)}},k=e=>{let s=Math.floor(e/60);return"".concat(s,":").concat((e%60).toString().padStart(2,"0"))};return(0,l.jsxs)(u.Zp,{className:"w-full max-w-md",children:[(0,l.jsx)(u.aR,{children:(0,l.jsxs)(u.ZB,{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(C.A,{className:"h-5 w-5"}),"Call Management"]}),d&&(0,l.jsx)(b.E,{className:x>=270?"bg-red-100 text-red-800":x>=240?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800",children:(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-current rounded-full animate-pulse"}),v?"Recording":"Active"]})})]})}),(0,l.jsxs)(u.Wu,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"font-semibold text-lg",children:a}),(0,l.jsx)("p",{className:"text-gray-600",children:r})]}),d&&(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2 text-2xl font-mono",children:[(0,l.jsx)(E.A,{className:"h-6 w-6"}),k(x)]}),(0,l.jsxs)("p",{className:"text-sm text-gray-500 mt-1",children:["Auto-end in ",k(300-x)]})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(m.J,{htmlFor:"call-notes",children:"Call Notes"}),(0,l.jsx)(T.T,{id:"call-notes",placeholder:"Add notes about the call...",value:j,onChange:e=>g(e.target.value),disabled:f,rows:3})]}),(0,l.jsx)("div",{className:"flex gap-2",children:d?(0,l.jsx)(n.$,{onClick:S,disabled:f,className:"flex-1 bg-red-600 hover:bg-red-700",children:f?(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Ending..."]}):(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(R.A,{className:"h-4 w-4"}),"End Call"]})}):(0,l.jsx)(n.$,{onClick:L,disabled:f,className:"flex-1 bg-green-600 hover:bg-green-700",children:f?(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Starting..."]}):(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(C.A,{className:"h-4 w-4"}),"Start Call"]})})}),d&&(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsx)(n.$,{variant:"outline",size:"sm",onClick:()=>N(!v),className:v?"bg-red-50 border-red-200":"",children:v?(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(_.A,{className:"h-3 w-3 text-red-600"}),"Stop Recording"]}):(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(D.A,{className:"h-3 w-3"}),"Start Recording"]})})}),v&&(0,l.jsx)("div",{className:"text-xs text-gray-500 text-center",children:(0,l.jsxs)("div",{className:"flex items-center justify-center gap-1",children:[(0,l.jsx)("div",{className:"w-2 h-2 bg-red-500 rounded-full animate-pulse"}),"Call is being recorded"]})})]})]})]})}var G=a(5784),B=a(7924),I=a(4516);let U=e=>{switch(e){case"A_LEVEL":return"bg-gradient-to-r from-yellow-400 to-yellow-600 text-white font-bold";case"B_LEVEL":return"bg-gradient-to-r from-blue-400 to-blue-600 text-white font-medium";case"C_LEVEL":return"bg-gradient-to-r from-green-400 to-green-600 text-white";case"NEW":return"bg-gradient-to-r from-gray-400 to-gray-600 text-white";default:return"bg-gray-100 text-gray-800"}},$=e=>{switch(e){case"A_LEVEL":return"A-Level";case"B_LEVEL":return"B-Level";case"C_LEVEL":return"C-Level";case"NEW":return"New";default:return"Unknown"}};function P(e){let{isOpen:s,onClose:a,leadId:i,leadName:c,onAssignmentComplete:d,onError:o}=e,{currentBranch:x}=(0,r.O)(),[g,f]=(0,t.useState)([]),[p,v]=(0,t.useState)([]),[N,w]=(0,t.useState)([]),[C,S]=(0,t.useState)(""),[L,k]=(0,t.useState)(""),[R,_]=(0,t.useState)(""),[D,O]=(0,t.useState)(""),[P,V]=(0,t.useState)(""),[z,J]=(0,t.useState)(""),[W,M]=(0,t.useState)(!1),[q,H]=(0,t.useState)(!1),[X,Z]=(0,t.useState)(!1),Y=Array.from(new Set(g.map(e=>e.teacher.user.name))).sort(),K=Array.from(new Set(g.map(e=>e.course.level))).sort(),Q=(0,t.useCallback)(async()=>{M(!0);try{let e=await fetch("/api/leads/".concat(i,"/assign-group?branch=").concat(x.id));if(!e.ok)throw Error("Failed to fetch available groups");let s=await e.json();f(s.groups),w(s.slotAnalysis||[])}catch(e){o(e instanceof Error?e.message:"Failed to fetch groups")}finally{M(!1)}},[i,x.id,o]),ee=(0,t.useCallback)(()=>{let e=g;L&&(e=e.filter(e=>e.name.toLowerCase().includes(L.toLowerCase())||e.course.name.toLowerCase().includes(L.toLowerCase())||e.teacher.user.name.toLowerCase().includes(L.toLowerCase()))),R&&(e=e.filter(e=>e.teacher.user.name===R)),D&&(e=e.filter(e=>e.course.level===D)),P&&(e=e.filter(e=>e.teacher.tier===P)),v(e)},[g,L,R,D,P]);(0,t.useEffect)(()=>{Z(!0)},[]),(0,t.useEffect)(()=>{s&&X&&Q()},[s,Q,X]),(0,t.useEffect)(()=>{X&&ee()},[g,L,R,D,P,ee,X]);let es=async()=>{if(C){H(!0);try{let e=await fetch("/api/leads/".concat(i,"/assign-group"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({groupId:C,notes:z})});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to assign group")}d(),a()}catch(e){o(e instanceof Error?e.message:"Failed to assign group")}finally{H(!1)}}},ea=e=>{try{let s=JSON.parse(e);return Array.isArray(s)?s.join(", "):e}catch(s){return e}},el=(e,s)=>{let a=e/s*100;return a>=90?"bg-red-100 text-red-800":a>=70?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"};return X?(0,l.jsx)(y.lG,{open:s,onOpenChange:a,children:(0,l.jsxs)(y.Cf,{className:"max-w-4xl max-h-[90vh] overflow-y-auto",children:[(0,l.jsx)(y.c7,{children:(0,l.jsxs)(y.L3,{children:["Assign Group to ",c]})}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"search",children:"Search Groups"}),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(B.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,l.jsx)(h.p,{id:"search",placeholder:"Search by name, course, teacher...",value:L,onChange:e=>k(e.target.value),className:"pl-10"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"teacher-filter",children:"Filter by Teacher"}),(0,l.jsxs)(G.l6,{value:R||"all",onValueChange:e=>_("all"===e?"":e),children:[(0,l.jsx)(G.bq,{children:(0,l.jsx)(G.yv,{placeholder:"All teachers"})}),(0,l.jsxs)(G.gC,{children:[(0,l.jsx)(G.eb,{value:"all",children:"All teachers"}),Y.map(e=>(0,l.jsx)(G.eb,{value:e,children:e},e))]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"level-filter",children:"Filter by Level"}),(0,l.jsxs)(G.l6,{value:D||"all",onValueChange:e=>O("all"===e?"":e),children:[(0,l.jsx)(G.bq,{children:(0,l.jsx)(G.yv,{placeholder:"All levels"})}),(0,l.jsxs)(G.gC,{children:[(0,l.jsx)(G.eb,{value:"all",children:"All levels"}),K.map(e=>(0,l.jsx)(G.eb,{value:e,children:e},e))]})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"tier-filter",children:"Filter by Teacher Tier"}),(0,l.jsxs)(G.l6,{value:P||"all",onValueChange:e=>V("all"===e?"":e),children:[(0,l.jsx)(G.bq,{children:(0,l.jsx)(G.yv,{placeholder:"All tiers"})}),(0,l.jsxs)(G.gC,{children:[(0,l.jsx)(G.eb,{value:"all",children:"All tiers"}),(0,l.jsx)(G.eb,{value:"A_LEVEL",children:"A-Level Teachers"}),(0,l.jsx)(G.eb,{value:"B_LEVEL",children:"B-Level Teachers"}),(0,l.jsx)(G.eb,{value:"C_LEVEL",children:"C-Level Teachers"}),(0,l.jsx)(G.eb,{value:"NEW",children:"New Teachers"})]})]})]}),(0,l.jsx)("div",{className:"flex items-end",children:(0,l.jsx)(n.$,{variant:"outline",onClick:()=>{k(""),_(""),O(""),V("")},className:"w-full",children:"Clear Filters"})})]}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("h3",{className:"font-medium",children:["Available Groups (",p.length,")"]}),W?(0,l.jsxs)("div",{className:"text-center py-8",children:[(0,l.jsx)("div",{className:"inline-block w-6 h-6 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin"}),(0,l.jsx)("p",{className:"mt-2 text-gray-600",children:"Loading groups..."})]}):0===p.length?(0,l.jsx)("div",{className:"text-center py-8 text-gray-500",children:"No available groups found"}):(0,l.jsx)("div",{className:"grid gap-3 max-h-60 overflow-y-auto",children:p.map(e=>(0,l.jsx)(u.Zp,{className:"cursor-pointer transition-colors ".concat(C===e.id?"ring-2 ring-blue-500 bg-blue-50":"hover:bg-gray-50"),onClick:()=>S(e.id),children:(0,l.jsx)(u.Wu,{className:"p-4",children:(0,l.jsxs)("div",{className:"flex justify-between items-start",children:[(0,l.jsxs)("div",{className:"space-y-2 flex-1",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("h4",{className:"font-medium",children:e.name}),(0,l.jsxs)(b.E,{variant:"outline",children:[e.course.name," - ",e.course.level]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 gap-4 text-sm text-gray-600",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(A.A,{className:"h-3 w-3"}),e.teacher.user.name]}),(0,l.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat(U(e.teacher.tier||"NEW")),children:$(e.teacher.tier||"NEW")})]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(I.A,{className:"h-3 w-3"}),e.room||"TBA"," - ",e.branch]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(E.A,{className:"h-3 w-3"}),ea(e.schedule)]}),(0,l.jsxs)("div",{className:"flex items-center gap-1",children:[(0,l.jsx)(j.A,{className:"h-3 w-3"}),new Date(e.startDate).toLocaleDateString()]})]})]}),(0,l.jsxs)(b.E,{className:el(e._count.enrollments,e.capacity),children:[(0,l.jsx)(F.A,{className:"h-3 w-3 mr-1"}),e._count.enrollments,"/",e.capacity]})]})})},e.id))})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)(m.J,{htmlFor:"assignment-notes",children:"Assignment Notes (Optional)"}),(0,l.jsx)(T.T,{id:"assignment-notes",placeholder:"Add any notes about this group assignment...",value:z,onChange:e=>J(e.target.value),rows:3})]}),(0,l.jsxs)("div",{className:"flex justify-end gap-2",children:[(0,l.jsx)(n.$,{variant:"outline",onClick:a,children:"Cancel"}),(0,l.jsx)(n.$,{onClick:es,disabled:!C||q,children:q?(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"}),"Assigning..."]}):"Assign Group"})]})]})]})}):null}function V(e){let{leads:s,onLeadUpdate:a,onError:r,isArchiveView:i=!1}=e,[c,d]=(0,t.useState)(null),[o,h]=(0,t.useState)(null),[m,u]=(0,t.useState)(!1),[g,f]=(0,t.useState)(null),[v,N]=(0,t.useState)(null);(0,t.useEffect)(()=>(u(!0),()=>{g&&(g.pause(),g.src="")}),[g]);let T=e=>{switch(e){case"NEW":return"bg-blue-100 text-blue-800";case"CALLING":return"bg-yellow-100 text-yellow-800";case"CALL_COMPLETED":return"bg-green-100 text-green-800";case"GROUP_ASSIGNED":return"bg-purple-100 text-purple-800";case"ARCHIVED":default:return"bg-gray-100 text-gray-800";case"NOT_INTERESTED":return"bg-red-100 text-red-800"}},R=e=>e.replace("_"," ").toLowerCase().replace(/\b\w/g,e=>e.toUpperCase()),_=e=>"NEW"===e.status&&!i,D=e=>"CALL_COMPLETED"===e.status&&!i,G=async e=>{try{let s=await fetch("/api/leads/".concat(e,"/archive"),{method:"POST"});if(!s.ok){let e=await s.json();throw Error(e.error||"Failed to archive lead")}a()}catch(e){r(e instanceof Error?e.message:"Failed to archive lead")}},B=e=>{if(!e)return"N/A";let s=Math.floor(e/60);return"".concat(s,"m ").concat(e%60,"s")},I=e=>{if(v===e&&g){g.pause(),f(null),N(null);return}g&&(g.pause(),g.currentTime=0);let s=new Audio(e);f(s),N(e),s.play().catch(e=>{console.error("Error playing audio:",e),r("Failed to play recording: "+e.message),f(null),N(null)}),s.addEventListener("ended",()=>{f(null),N(null)}),s.addEventListener("error",()=>{f(null),N(null),r("Error loading audio file")})},U=(e,s,a)=>{let l=document.createElement("a");l.href=e,l.download="call-recording-".concat(s,"-").concat(a,".mp3"),document.body.appendChild(l),l.click(),document.body.removeChild(l)};return m?0===s.length?(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("div",{className:"text-gray-500",children:i?"No archived leads found":"No leads found"})}):(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,l.jsxs)(w.XI,{children:[(0,l.jsx)(w.A0,{children:(0,l.jsxs)(w.Hj,{children:[(0,l.jsx)(w.nd,{children:"Lead Info"}),(0,l.jsx)(w.nd,{children:"Status"}),(0,l.jsx)(w.nd,{children:"Call Info"}),(0,l.jsx)(w.nd,{children:"Recordings"}),(0,l.jsx)(w.nd,{children:"Assigned Group"}),(0,l.jsx)(w.nd,{children:"Actions"})]})}),(0,l.jsx)(w.BF,{children:s.map(e=>(0,l.jsxs)(w.Hj,{className:"hover:bg-gray-50",children:[(0,l.jsx)(w.nA,{children:(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsxs)("div",{className:"font-medium flex items-center gap-2",children:[(0,l.jsx)(A.A,{className:"h-4 w-4"}),e.name]}),(0,l.jsxs)("div",{className:"text-sm text-gray-600 flex items-center gap-1",children:[(0,l.jsx)(C.A,{className:"h-3 w-3"}),e.phone]}),(0,l.jsxs)("div",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,l.jsx)(j.A,{className:"h-3 w-3"}),(0,p.Yq)(e.createdAt)]}),e.source&&(0,l.jsxs)("div",{className:"text-xs text-gray-500",children:["Source: ",e.source]})]})}),(0,l.jsx)(w.nA,{children:(0,l.jsx)(b.E,{className:T(e.status),children:R(e.status)})}),(0,l.jsx)(w.nA,{children:e.callDuration?(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsxs)("div",{className:"text-sm flex items-center gap-1",children:[(0,l.jsx)(E.A,{className:"h-3 w-3"}),B(e.callDuration)]}),e.callStartedAt&&(0,l.jsx)("div",{className:"text-xs text-gray-500",children:(0,p.r6)(e.callStartedAt)})]}):(0,l.jsx)("span",{className:"text-gray-400 text-sm",children:"No calls"})}),(0,l.jsx)(w.nA,{children:e.callRecords&&e.callRecords.length>0?(0,l.jsx)("div",{className:"space-y-2",children:e.callRecords.map(s=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[s.recordingUrl?(0,l.jsxs)("div",{className:"flex gap-1",children:[(0,l.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>I(s.recordingUrl),className:"h-6 w-6 p-0 ".concat(v===s.recordingUrl?"bg-green-100 border-green-300":""),children:v===s.recordingUrl?(0,l.jsx)(S.A,{className:"h-3 w-3"}):(0,l.jsx)(L.A,{className:"h-3 w-3"})}),(0,l.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>U(s.recordingUrl,e.name,s.id),className:"h-6 w-6 p-0",children:(0,l.jsx)(k.A,{className:"h-3 w-3"})})]}):(0,l.jsx)("span",{className:"text-xs text-gray-400",children:"No recording"}),(0,l.jsx)("div",{className:"text-xs text-gray-500",children:B(s.duration)})]},s.id))}):(0,l.jsx)("span",{className:"text-gray-400 text-sm",children:"No recordings"})}),(0,l.jsx)(w.nA,{children:e.assignedGroup?(0,l.jsxs)("div",{className:"space-y-1",children:[(0,l.jsx)("div",{className:"text-sm font-medium",children:e.assignedGroup.name}),(0,l.jsxs)("div",{className:"text-xs text-gray-600",children:[e.assignedGroup.course.name," - ",e.assignedGroup.course.level]}),(0,l.jsxs)("div",{className:"text-xs text-gray-600 flex items-center gap-1",children:[(0,l.jsx)(A.A,{className:"h-3 w-3"}),e.assignedGroup.teacher.user.name]}),e.assignedAt&&(0,l.jsx)("div",{className:"text-xs text-gray-500",children:(0,p.r6)(e.assignedAt)})]}):(0,l.jsx)("span",{className:"text-gray-400 text-sm",children:"Not assigned"})}),(0,l.jsx)(w.nA,{children:(0,l.jsxs)("div",{className:"flex gap-1",children:[_(e)&&(0,l.jsxs)(y.lG,{children:[(0,l.jsx)(y.zM,{asChild:!0,children:(0,l.jsx)(n.$,{size:"sm",className:"bg-green-600 hover:bg-green-700",children:(0,l.jsx)(C.A,{className:"h-3 w-3"})})}),(0,l.jsx)(y.Cf,{children:(0,l.jsx)(O,{leadId:e.id,leadName:e.name,leadPhone:e.phone,onCallComplete:()=>{a(),d(null)},onError:r})})]}),D(e)&&(0,l.jsx)(n.$,{size:"sm",onClick:()=>h(e),className:"bg-purple-600 hover:bg-purple-700",children:(0,l.jsx)(F.A,{className:"h-3 w-3"})}),"GROUP_ASSIGNED"===e.status&&!i&&(0,l.jsx)(n.$,{size:"sm",variant:"outline",onClick:()=>G(e.id),children:(0,l.jsx)(x.A,{className:"h-3 w-3"})})]})})]},e.id))})]})}),o&&(0,l.jsx)(P,{isOpen:!!o,onClose:()=>h(null),leadId:o.id,leadName:o.name,onAssignmentComplete:a,onError:r})]}):(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsx)("div",{className:"border rounded-lg overflow-hidden",children:(0,l.jsx)("div",{className:"p-8 text-center text-gray-500",children:"Loading leads..."})})})}function z(){let{currentBranch:e}=(0,r.O)(),[s,a]=(0,t.useState)([]),[h,m]=(0,t.useState)([]),[u,j]=(0,t.useState)(!0),[g,f]=(0,t.useState)("ALL"),[p,v]=(0,t.useState)({}),[b,y]=(0,t.useState)(null),[w,A]=(0,t.useState)("active"),C=(0,t.useCallback)(async function(){let s=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{j(!0);let l=new URLSearchParams;"ALL"!==g&&l.append("status",g),p.dateFilter?l.append("dateFilter",p.dateFilter):p.startDate&&p.endDate&&(l.append("startDate",p.startDate),l.append("endDate",p.endDate)),l.append("archived",s.toString()),l.append("branch",e.id);let t="/api/leads?".concat(l.toString()),r=await fetch(t);if(!r.ok)throw Error("Failed to fetch leads");let n=await r.json();s?m(n.leads||[]):a(n.leads||[])}catch(e){console.error("Error fetching leads:",e),y(e instanceof Error?e.message:"Failed to fetch leads")}finally{j(!1)}},[g,p,e.id]);(0,t.useEffect)(()=>{C("archived"===w)},[C,w]);let E=()=>{C("archived"===w),y(null)},S=e=>{y(e)};return(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center",children:[(0,l.jsxs)("div",{children:[(0,l.jsxs)("h1",{className:"text-3xl font-bold text-gray-900",children:["Leads Management - ",e.name]}),(0,l.jsx)("p",{className:"text-gray-600",children:"Comprehensive lead tracking with call management and group assignment"})]}),(0,l.jsxs)(n.$,{onClick:()=>{C("archived"===w)},variant:"outline",size:"sm",children:[(0,l.jsx)(d.A,{className:"h-4 w-4 mr-2"}),"Refresh"]})]}),b&&(0,l.jsxs)(c.Fc,{variant:"destructive",children:[(0,l.jsx)(o.A,{className:"h-4 w-4"}),(0,l.jsx)(c.TN,{children:b})]}),(0,l.jsxs)(i.tU,{value:w,onValueChange:A,className:"space-y-6",children:[(0,l.jsxs)(i.j7,{children:[(0,l.jsx)(i.Xi,{value:"active",children:"Active Leads"}),(0,l.jsxs)(i.Xi,{value:"archived",className:"flex items-center gap-2",children:[(0,l.jsx)(x.A,{className:"h-4 w-4"}),"Archive"]})]}),(0,l.jsxs)(i.av,{value:"active",className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,l.jsx)(N,{onFilterChange:e=>{v(e)},currentFilter:p.dateFilter}),(0,l.jsx)("div",{className:"flex gap-2",children:["ALL","NEW","CALLING","CALL_COMPLETED","GROUP_ASSIGNED","NOT_INTERESTED"].map(e=>(0,l.jsx)(n.$,{variant:g===e?"default":"outline",onClick:()=>f(e),size:"sm",children:e.replace("_"," ")},e))})]}),u?(0,l.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Loading leads..."})]})}):(0,l.jsx)(V,{leads:s,onLeadUpdate:E,onError:S,isArchiveView:!1})]}),(0,l.jsxs)(i.av,{value:"archived",className:"space-y-6",children:[(0,l.jsx)("div",{className:"flex justify-between items-center",children:(0,l.jsxs)("div",{children:[(0,l.jsx)("h2",{className:"text-xl font-semibold",children:"Archived Leads"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Leads that have been successfully assigned to groups"})]})}),u?(0,l.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"inline-block w-8 h-8 border-4 border-gray-300 border-t-blue-600 rounded-full animate-spin mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Loading archived leads..."})]})}):(0,l.jsx)(V,{leads:h,onLeadUpdate:E,onError:S,isArchiveView:!0})]})]})]})}},7705:(e,s,a)=>{"use strict";a.d(s,{BranchProvider:()=>i,O:()=>c,Z:()=>d});var l=a(5155),t=a(2115);let r=(0,t.createContext)(void 0),n=[{id:"main",name:"Main Branch",address:"Gagarin 95A, Samarkand",phone:"+998712345678",isActive:!0},{id:"branch",name:"Branch",address:"Mirzo Ulug'bek 34, Samarkand",phone:"+998712345679",isActive:!0}];function i(e){let{children:s}=e,[a,i]=(0,t.useState)(n[0]),[c]=(0,t.useState)(n),[d,o]=(0,t.useState)(!0);return(0,t.useEffect)(()=>{let e=localStorage.getItem("selectedBranch");if(e){let s=c.find(s=>s.id===e);s&&i(s)}o(!1)},[c]),(0,l.jsx)(r.Provider,{value:{currentBranch:a,branches:c,switchBranch:e=>{let s=c.find(s=>s.id===e);s&&(i(s),localStorage.setItem("selectedBranch",e))},isLoading:d},children:s})}function c(){let e=(0,t.useContext)(r);if(void 0===e)throw Error("useBranch must be used within a BranchProvider");return e}function d(){return(0,t.useContext)(r)||null}},8447:(e,s,a)=>{Promise.resolve().then(a.bind(a,5990))},8524:(e,s,a)=>{"use strict";a.d(s,{A0:()=>i,BF:()=>c,Hj:()=>d,XI:()=>n,nA:()=>x,nd:()=>o});var l=a(5155),t=a(2115),r=a(3999);let n=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("div",{className:"relative w-full overflow-auto",children:(0,l.jsx)("table",{ref:s,className:(0,r.cn)("w-full caption-bottom text-sm",a),...t})})});n.displayName="Table";let i=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("thead",{ref:s,className:(0,r.cn)("[&_tr]:border-b",a),...t})});i.displayName="TableHeader";let c=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("tbody",{ref:s,className:(0,r.cn)("[&_tr:last-child]:border-0",a),...t})});c.displayName="TableBody",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("tfoot",{ref:s,className:(0,r.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",a),...t})}).displayName="TableFooter";let d=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("tr",{ref:s,className:(0,r.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",a),...t})});d.displayName="TableRow";let o=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("th",{ref:s,className:(0,r.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",a),...t})});o.displayName="TableHead";let x=t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("td",{ref:s,className:(0,r.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",a),...t})});x.displayName="TableCell",t.forwardRef((e,s)=>{let{className:a,...t}=e;return(0,l.jsx)("caption",{ref:s,className:(0,r.cn)("mt-4 text-sm text-muted-foreground",a),...t})}).displayName="TableCaption"}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,4771,7968,8441,1684,7358],()=>s(8447)),_N_E=e.O()}]);