(()=>{var e={};e.id=5540,e.ids=[5540],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},36722:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>w,routeModule:()=>m,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};r.r(s),r.d(s,{DELETE:()=>h,GET:()=>c,PUT:()=>p});var n=r(96559),o=r(48088),a=r(37719),i=r(32190),u=r(79464),d=r(45697);let l=d.Ik({name:d.Yj().min(1).optional(),courseId:d.Yj().optional(),teacherId:d.Yj().optional(),capacity:d.ai().min(1).max(50).optional(),schedule:d.Yj().optional(),room:d.Yj().optional(),cabinetId:d.Yj().optional(),branch:d.Yj().optional(),startDate:d.Yj().optional(),endDate:d.Yj().optional(),isActive:d.zM().optional()});async function c(e,{params:t}){try{let{id:e}=await t,r=await u.z.group.findUnique({where:{id:e},include:{course:{select:{id:!0,name:!0,level:!0,description:!0,duration:!0,price:!0}},teacher:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}},enrollments:{include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0,email:!0}}}}},orderBy:{createdAt:"desc"}},classes:{include:{teacher:{include:{user:{select:{name:!0}}}},_count:{select:{attendances:!0}}},orderBy:{date:"desc"},take:10},_count:{select:{enrollments:!0,classes:!0}}}});if(!r)return i.NextResponse.json({error:"Group not found"},{status:404});return i.NextResponse.json(r)}catch(e){return console.error("Error fetching group:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,{params:t}){try{let{id:r}=await t,s=await e.json(),n=l.parse(s);if(!await u.z.group.findUnique({where:{id:r}}))return i.NextResponse.json({error:"Group not found"},{status:404});if(n.courseId&&!await u.z.course.findUnique({where:{id:n.courseId}}))return i.NextResponse.json({error:"Course not found"},{status:400});if(n.teacherId&&!await u.z.teacher.findUnique({where:{id:n.teacherId}}))return i.NextResponse.json({error:"Teacher not found"},{status:400});let o=await u.z.group.update({where:{id:r},data:{...n,startDate:n.startDate?new Date(n.startDate):void 0,endDate:n.endDate?new Date(n.endDate):void 0,updatedAt:new Date},include:{course:{select:{name:!0,level:!0}},teacher:{select:{id:!0,tier:!0,subject:!0,user:{select:{name:!0}}}},_count:{select:{enrollments:!0}}}});return i.NextResponse.json(o)}catch(e){if(e instanceof d.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error updating group:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:t}){try{let{id:e}=await t,r=await u.z.group.findUnique({where:{id:e},include:{enrollments:!0}});if(!r)return i.NextResponse.json({error:"Group not found"},{status:404});let s=r.enrollments.filter(e=>"ACTIVE"===e.status);if(s.length>0)return i.NextResponse.json({error:"Cannot delete group with active enrollments",details:`Group has ${s.length} active enrollment(s)`},{status:400});return await u.z.group.delete({where:{id:e}}),i.NextResponse.json({message:"Group deleted successfully",deletedId:e})}catch(e){return console.error("Error deleting group:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/groups/[id]/route",pathname:"/api/groups/[id]",filename:"route",bundlePath:"app/api/groups/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\groups\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:j}=m;function w(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,5697],()=>r(36722));module.exports=s})();