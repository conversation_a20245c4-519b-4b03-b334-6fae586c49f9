(()=>{var e={};e.id=2772,e.ids=[2772],e.modules={1530:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>h,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>p});var n=r(96559),a=r(48088),o=r(37719),i=r(32190),u=r(79464);async function p(){try{await u.z.$queryRaw`SELECT 1`;let[e,t,r]=await Promise.all([u.z.user.count(),u.z.student.count(),u.z.group.count()]);return i.NextResponse.json({status:"healthy",timestamp:new Date().toISOString(),database:"connected",environment:"production",stats:{users:e,students:t,groups:r},version:process.env.npm_package_version||"1.0.0"})}catch(e){return console.error("Health check failed:",e),i.NextResponse.json({status:"unhealthy",timestamp:new Date().toISOString(),database:"disconnected",environment:"production",error:e instanceof Error?e.message:"Unknown error"},{status:500})}}let c=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/health/route",pathname:"/api/health",filename:"route",bundlePath:"app/api/health/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\health\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:h}=c;function m(){return(0,o.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var s=r(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580],()=>r(1530));module.exports=s})();