(()=>{var e={};e.id=590,e.ids=[590],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7786:(e,t,r)=>{"use strict";function s(e){let t=e.headers.get("X-Inter-Server-Secret"),r=e.headers.get("X-Timestamp"),s=process.env.INTER_SERVER_SECRET;if(!t||!s||t!==s)return!1;if(r){let e=parseInt(r),t=Date.now();if(isNaN(e)||t-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function a(e,t){try{let r="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!r)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let s=`${r}${t.endpoint}`,a={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let t=Date.now().toString(),r=i.getServerConfig(),s=`${r.serverType}-${t}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":r.serverType,"X-Request-ID":s,"X-Timestamp":t,"User-Agent":`${r.serverType}-server`}}(),...t.headers},n=await fetch(s,{method:t.method,headers:a,body:t.data?JSON.stringify(t.data):void 0}),o=await n.json();return{success:n.ok,data:o,status:n.status,error:n.ok?void 0:o.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}r.d(t,{LQ:()=>i,cU:()=>n,g2:()=>s});class n{static async authenticateUser(e,t){return a("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:t}})}static async getUserData(e){return a("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,t){return a("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:t}})}}class i{static logRequest(e,t,r,s){let a=new Date().toISOString(),n=process.env.SERVER_TYPE||"unknown";console.log(`[${a}] Inter-Server ${e.toUpperCase()}: ${t}`,{server:n,success:r,details:s})}static async healthCheck(e){try{return(await a(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{"use strict";e.exports=require("querystring")},12412:e=>{"use strict";e.exports=require("assert")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},41098:(e,t,r)=>{"use strict";r.d(t,{N:()=>n});var s=r(13581),a=r(7786);let n={providers:[(0,s.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let t=await a.cU.authenticateUser(e.phone,e.password);if(!t.success)return console.error("Authentication failed:",t.error),null;let r=t.data.user;if(!r)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(r.role))return console.error("User role not allowed on staff server:",r.role),null;return{id:r.id,phone:r.phone,name:r.name,email:r.email,role:r.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role||null),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role||null),e)},pages:{signIn:"/auth/signin"}}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71347:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>S,routeModule:()=>m,serverHooks:()=>E,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var s={};r.r(s),r.d(s,{GET:()=>d});var a=r(96559),n=r(48088),i=r(37719),o=r(32190),c=r(19854),l=r(41098),u=r(79464);async function d(e){try{let t,r=await (0,c.getServerSession)(l.N);if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER","RECEPTION"].includes(r.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let{searchParams:s}=new URL(e.url),a=s.get("period")||"today",n=new Date;switch(a){case"week":(t=new Date).setDate(t.getDate()-7);break;case"month":(t=new Date).setMonth(t.getMonth()-1);break;default:(t=new Date).setHours(0,0,0,0)}let i=await u.z.callRecord.findMany({where:{createdAt:{gte:t,lte:n}},include:{user:{select:{name:!0}},lead:{select:{name:!0,phone:!0}}},orderBy:{createdAt:"desc"}}),d=await u.z.activityLog.findMany({where:{createdAt:{gte:t,lte:n},OR:[{action:"SEND_SMS"},{action:"SEND_EMAIL"},{action:"SEND_NOTIFICATION"}]},include:{user:{select:{name:!0}}},orderBy:{createdAt:"desc"}}),m={calls:{total:i.length,successful:i.filter(e=>e.duration&&e.duration>30).length,averageDuration:i.length>0?i.reduce((e,t)=>e+(t.duration||0),0)/i.length:0},sms:{total:d.filter(e=>"SEND_SMS"===e.action).length,delivered:Math.floor(.95*d.filter(e=>"SEND_SMS"===e.action).length)},email:{total:d.filter(e=>"SEND_EMAIL"===e.action).length,delivered:Math.floor(.92*d.filter(e=>"SEND_EMAIL"===e.action).length)},notifications:{total:d.filter(e=>"SEND_NOTIFICATION"===e.action).length,active:d.filter(e=>"SEND_NOTIFICATION"===e.action&&new Date(e.createdAt)>new Date(Date.now()-864e5)).length}},h=[];for(let e of i.slice(0,5))h.push({id:e.id,type:"CALL",recipient:e.lead?.name||"Unknown",message:`Call duration: ${e.duration?Math.floor(e.duration/60):0} minutes`,status:e.duration&&e.duration>30?"Completed":"Missed",timestamp:e.createdAt,user:e.user.name});for(let e of d.slice(0,5))h.push({id:e.id,type:"SEND_SMS"===e.action?"SMS":"SEND_EMAIL"===e.action?"EMAIL":"NOTIFICATION",recipient:"string"==typeof e.details&&e.details.includes("to:")?e.details.split("to:")[1]?.split(" ")[0]:"Multiple recipients",message:"string"==typeof e.details?e.details:"Communication sent",status:"Delivered",timestamp:e.createdAt,user:e.user.name});h.sort((e,t)=>new Date(t.timestamp).getTime()-new Date(e.timestamp).getTime());let g=new Map;i.forEach(e=>{let t=e.userId;g.has(t)||g.set(t,{name:e.user.name,calls:0,messages:0,totalActivities:0});let r=g.get(t);r.calls++,r.totalActivities++}),d.forEach(e=>{let t=e.userId;g.has(t)||g.set(t,{name:e.user.name,calls:0,messages:0,totalActivities:0});let r=g.get(t);r.messages++,r.totalActivities++});let E=Array.from(g.values()).sort((e,t)=>t.totalActivities-e.totalActivities).slice(0,5),S={period:a,dateRange:{start:t.toISOString(),end:n.toISOString()},stats:m,recentActivities:h.slice(0,10),topPerformers:E,trends:await p(t,n)};return o.NextResponse.json(S)}catch(e){return console.error("Error fetching communication stats:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function p(e,t){let r=t.getTime()-e.getTime(),s=new Date(e.getTime()-r),a=new Date(e.getTime()),[n,i,o]=await Promise.all([u.z.callRecord.count({where:{createdAt:{gte:e,lte:t}}}),u.z.activityLog.count({where:{action:"SEND_SMS",createdAt:{gte:e,lte:t}}}),u.z.activityLog.count({where:{action:"SEND_EMAIL",createdAt:{gte:e,lte:t}}})]),[c,l,d]=await Promise.all([u.z.callRecord.count({where:{createdAt:{gte:s,lte:a}}}),u.z.activityLog.count({where:{action:"SEND_SMS",createdAt:{gte:s,lte:a}}}),u.z.activityLog.count({where:{action:"SEND_EMAIL",createdAt:{gte:s,lte:a}}})]),p=(e,t)=>0===t?100*(e>0):Math.round((e-t)/t*100);return{callsGrowth:p(n,c),smsGrowth:p(i,l),emailGrowth:p(o,d)}}let m=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/communication/stats/route",pathname:"/api/communication/stats",filename:"route",bundlePath:"app/api/communication/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\communication\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:E}=m;function S(){return(0,i.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},74075:e=>{"use strict";e.exports=require("zlib")},78335:()=>{},79428:e=>{"use strict";e.exports=require("buffer")},79464:(e,t,r)=>{"use strict";r.d(t,{z:()=>a});var s=r(96330);let a=globalThis.prisma??new s.PrismaClient},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},94735:e=>{"use strict";e.exports=require("events")},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[4243,580,3412],()=>r(71347));module.exports=s})();