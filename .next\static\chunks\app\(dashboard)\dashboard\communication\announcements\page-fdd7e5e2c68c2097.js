(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2489],{2525:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(2895).A)("Trash2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},3861:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(2895).A)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]])},4616:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(2895).A)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},4621:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(2895).A)("PenSquare",[["path",{d:"M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1qinfi"}],["path",{d:"M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4Z",key:"w2jsv5"}]])},7624:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(2895).A)("Loader2",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},7924:(e,s,t)=>{"use strict";t.d(s,{A:()=>n});let n=(0,t(2895).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},7968:(e,s,t)=>{Promise.resolve().then(t.bind(t,9514))},9514:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>b});var n=t(5155),a=t(2115),l=t(8482),r=t(7168),i=t(8145),c=t(9852),d=t(9474),h=t(5784),x=t(9840),u=t(9026),o=t(3999),j=t(7624),m=t(4616),v=t(7924);let g=(0,t(2895).A)("Megaphone",[["path",{d:"m3 11 18-5v12L3 14v-3z",key:"n962bs"}],["path",{d:"M11.6 16.8a3 3 0 1 1-5.8-1.6",key:"1yl0tm"}]]);var y=t(3861),p=t(4621),A=t(2525);function b(){let[e,s]=(0,a.useState)([]),[t,b]=(0,a.useState)(!0),[N,f]=(0,a.useState)(""),[C,S]=(0,a.useState)("ALL"),[w,E]=(0,a.useState)("ALL"),[L,T]=(0,a.useState)(!1),[k,M]=(0,a.useState)(!1),[U,H]=(0,a.useState)(null),[F,P]=(0,a.useState)(!1),[R,q]=(0,a.useState)(null),[D,I]=(0,a.useState)({title:"",content:"",priority:"MEDIUM",targetAudience:"ALL",isActive:!0}),O=(0,a.useCallback)(async()=>{try{b(!0);let e=new URLSearchParams({page:"1",limit:"20",..."ALL"!==C&&{priority:C},..."ALL"!==w&&{targetAudience:w}}),t=await fetch("/api/announcements?".concat(e));if(!t.ok)throw Error("Failed to fetch announcements");let n=await t.json();s(n.announcements),q(null)}catch(e){console.error("Error fetching announcements:",e),q("Failed to fetch announcements")}finally{b(!1)}},[C,w]);(0,a.useEffect)(()=>{O()},[O]);let G=async()=>{P(!0),q(null);try{let e=await fetch("/api/announcements",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(D)});if(!e.ok){let s=await e.json();throw Error(s.error||"Failed to create announcement")}let t=await e.json();s(e=>[t,...e]),T(!1),$()}catch(e){q(e instanceof Error?e.message:"An error occurred")}finally{P(!1)}},V=async()=>{if(U){P(!0),q(null);try{s(e=>e.map(e=>e.id===U.id?{...e,...D,updatedAt:new Date().toISOString()}:e)),M(!1),H(null),$()}catch(e){q(e instanceof Error?e.message:"An error occurred")}finally{P(!1)}}},W=async e=>{if(confirm("Are you sure you want to delete this announcement?"))try{s(s=>s.filter(s=>s.id!==e))}catch(e){q(e instanceof Error?e.message:"An error occurred")}},$=()=>{I({title:"",content:"",priority:"MEDIUM",targetAudience:"ALL",isActive:!0})},Z=e=>{H(e),I({title:e.title,content:e.content,priority:e.priority,targetAudience:e.targetAudience,isActive:e.isActive}),M(!0)},_=e.filter(e=>{let s=e.title.toLowerCase().includes(N.toLowerCase())||e.content.toLowerCase().includes(N.toLowerCase()),t="ALL"===C||e.priority===C,n="ALL"===w||e.targetAudience===w;return s&&t&&n}),z=e=>{switch(e){case"URGENT":return"bg-red-100 text-red-800";case"HIGH":return"bg-orange-100 text-orange-800";case"MEDIUM":return"bg-yellow-100 text-yellow-800";case"LOW":return"bg-green-100 text-green-800";default:return"bg-gray-100 text-gray-800"}},B=e=>{switch(e){case"STUDENTS":return"bg-blue-100 text-blue-800";case"TEACHERS":return"bg-purple-100 text-purple-800";case"PARENTS":return"bg-pink-100 text-pink-800";case"STAFF":return"bg-indigo-100 text-indigo-800";default:return"bg-gray-100 text-gray-800"}};return t?(0,n.jsxs)("div",{className:"flex items-center justify-center h-64",children:[(0,n.jsx)(j.A,{className:"h-8 w-8 animate-spin"}),(0,n.jsx)("span",{className:"ml-2",children:"Loading announcements..."})]}):(0,n.jsxs)("div",{className:"space-y-6",children:[R&&(0,n.jsx)(u.Fc,{variant:"destructive",children:(0,n.jsx)(u.TN,{children:R})}),(0,n.jsxs)("div",{className:"flex justify-between items-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"Announcements"}),(0,n.jsx)("p",{className:"text-gray-600",children:"Manage and broadcast important announcements"})]}),(0,n.jsxs)(x.lG,{open:L,onOpenChange:T,children:[(0,n.jsx)(x.zM,{asChild:!0,children:(0,n.jsxs)(r.$,{onClick:$,children:[(0,n.jsx)(m.A,{className:"h-4 w-4 mr-2"}),"Create Announcement"]})}),(0,n.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,n.jsxs)(x.c7,{children:[(0,n.jsx)(x.L3,{children:"Create New Announcement"}),(0,n.jsx)(x.rr,{children:"Create and publish a new announcement for your target audience."})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Title *"}),(0,n.jsx)(c.p,{value:D.title,onChange:e=>I(s=>({...s,title:e.target.value})),placeholder:"Enter announcement title"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Content *"}),(0,n.jsx)(d.T,{value:D.content,onChange:e=>I(s=>({...s,content:e.target.value})),placeholder:"Enter announcement content",rows:4})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Priority"}),(0,n.jsxs)(h.l6,{value:D.priority,onValueChange:e=>I(s=>({...s,priority:e})),children:[(0,n.jsx)(h.bq,{children:(0,n.jsx)(h.yv,{})}),(0,n.jsxs)(h.gC,{children:[(0,n.jsx)(h.eb,{value:"LOW",children:"Low"}),(0,n.jsx)(h.eb,{value:"MEDIUM",children:"Medium"}),(0,n.jsx)(h.eb,{value:"HIGH",children:"High"}),(0,n.jsx)(h.eb,{value:"URGENT",children:"Urgent"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Target Audience"}),(0,n.jsxs)(h.l6,{value:D.targetAudience,onValueChange:e=>I(s=>({...s,targetAudience:e})),children:[(0,n.jsx)(h.bq,{children:(0,n.jsx)(h.yv,{})}),(0,n.jsxs)(h.gC,{children:[(0,n.jsx)(h.eb,{value:"ALL",children:"All"}),(0,n.jsx)(h.eb,{value:"STUDENTS",children:"Students"}),(0,n.jsx)(h.eb,{value:"TEACHERS",children:"Teachers"}),(0,n.jsx)(h.eb,{value:"PARENTS",children:"Parents"}),(0,n.jsx)(h.eb,{value:"STAFF",children:"Staff"})]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>T(!1),children:"Cancel"}),(0,n.jsxs)(r.$,{onClick:G,disabled:F||!D.title||!D.content,children:[F&&(0,n.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Create Announcement"]})]})]})]})]})]}),(0,n.jsxs)(l.Zp,{children:[(0,n.jsx)(l.aR,{children:(0,n.jsx)(l.ZB,{children:"Search & Filter"})}),(0,n.jsx)(l.Wu,{children:(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)(v.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"}),(0,n.jsx)(c.p,{placeholder:"Search announcements...",value:N,onChange:e=>f(e.target.value),className:"pl-10"})]}),(0,n.jsxs)(h.l6,{value:C,onValueChange:S,children:[(0,n.jsx)(h.bq,{children:(0,n.jsx)(h.yv,{placeholder:"Filter by priority"})}),(0,n.jsxs)(h.gC,{children:[(0,n.jsx)(h.eb,{value:"ALL",children:"All Priorities"}),(0,n.jsx)(h.eb,{value:"URGENT",children:"Urgent"}),(0,n.jsx)(h.eb,{value:"HIGH",children:"High"}),(0,n.jsx)(h.eb,{value:"MEDIUM",children:"Medium"}),(0,n.jsx)(h.eb,{value:"LOW",children:"Low"})]})]}),(0,n.jsxs)(h.l6,{value:w,onValueChange:E,children:[(0,n.jsx)(h.bq,{children:(0,n.jsx)(h.yv,{placeholder:"Filter by audience"})}),(0,n.jsxs)(h.gC,{children:[(0,n.jsx)(h.eb,{value:"ALL",children:"All Audiences"}),(0,n.jsx)(h.eb,{value:"STUDENTS",children:"Students"}),(0,n.jsx)(h.eb,{value:"TEACHERS",children:"Teachers"}),(0,n.jsx)(h.eb,{value:"PARENTS",children:"Parents"}),(0,n.jsx)(h.eb,{value:"STAFF",children:"Staff"})]})]})]})})]}),(0,n.jsx)("div",{className:"space-y-4",children:_.map(e=>(0,n.jsxs)(l.Zp,{className:"hover:shadow-md transition-shadow",children:[(0,n.jsx)(l.aR,{children:(0,n.jsxs)("div",{className:"flex justify-between items-start",children:[(0,n.jsxs)("div",{className:"flex-1",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,n.jsx)(g,{className:"h-5 w-5 text-blue-600"}),(0,n.jsx)(l.ZB,{className:"text-lg",children:e.title}),(0,n.jsx)(i.E,{className:z(e.priority),children:e.priority}),(0,n.jsx)(i.E,{className:B(e.targetAudience),children:e.targetAudience}),e.isActive&&(0,n.jsxs)(i.E,{className:"bg-green-100 text-green-800",children:[(0,n.jsx)(y.A,{className:"h-3 w-3 mr-1"}),"Active"]})]}),(0,n.jsxs)(l.BT,{className:"text-sm text-gray-500",children:["By ",e.author.name," • ",(0,o.Yq)(e.createdAt)]})]}),(0,n.jsxs)("div",{className:"flex space-x-2",children:[(0,n.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>Z(e),children:(0,n.jsx)(p.A,{className:"h-4 w-4"})}),(0,n.jsx)(r.$,{variant:"outline",size:"sm",onClick:()=>W(e.id),className:"text-red-600 hover:text-red-700",children:(0,n.jsx)(A.A,{className:"h-4 w-4"})})]})]})}),(0,n.jsx)(l.Wu,{children:(0,n.jsx)("p",{className:"text-gray-700",children:e.content})})]},e.id))}),0===_.length&&(0,n.jsx)(l.Zp,{children:(0,n.jsxs)(l.Wu,{className:"text-center py-12",children:[(0,n.jsx)(g,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,n.jsx)("p",{className:"text-gray-500",children:"No announcements found matching your criteria."})]})}),(0,n.jsx)(x.lG,{open:k,onOpenChange:M,children:(0,n.jsxs)(x.Cf,{className:"max-w-2xl",children:[(0,n.jsxs)(x.c7,{children:[(0,n.jsx)(x.L3,{children:"Edit Announcement"}),(0,n.jsx)(x.rr,{children:"Update the announcement details."})]}),(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Title *"}),(0,n.jsx)(c.p,{value:D.title,onChange:e=>I(s=>({...s,title:e.target.value})),placeholder:"Enter announcement title"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Content *"}),(0,n.jsx)(d.T,{value:D.content,onChange:e=>I(s=>({...s,content:e.target.value})),placeholder:"Enter announcement content",rows:4})]}),(0,n.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Priority"}),(0,n.jsxs)(h.l6,{value:D.priority,onValueChange:e=>I(s=>({...s,priority:e})),children:[(0,n.jsx)(h.bq,{children:(0,n.jsx)(h.yv,{})}),(0,n.jsxs)(h.gC,{children:[(0,n.jsx)(h.eb,{value:"LOW",children:"Low"}),(0,n.jsx)(h.eb,{value:"MEDIUM",children:"Medium"}),(0,n.jsx)(h.eb,{value:"HIGH",children:"High"}),(0,n.jsx)(h.eb,{value:"URGENT",children:"Urgent"})]})]})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"text-sm font-medium",children:"Target Audience"}),(0,n.jsxs)(h.l6,{value:D.targetAudience,onValueChange:e=>I(s=>({...s,targetAudience:e})),children:[(0,n.jsx)(h.bq,{children:(0,n.jsx)(h.yv,{})}),(0,n.jsxs)(h.gC,{children:[(0,n.jsx)(h.eb,{value:"ALL",children:"All"}),(0,n.jsx)(h.eb,{value:"STUDENTS",children:"Students"}),(0,n.jsx)(h.eb,{value:"TEACHERS",children:"Teachers"}),(0,n.jsx)(h.eb,{value:"PARENTS",children:"Parents"}),(0,n.jsx)(h.eb,{value:"STAFF",children:"Staff"})]})]})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-2",children:[(0,n.jsx)(r.$,{variant:"outline",onClick:()=>M(!1),children:"Cancel"}),(0,n.jsxs)(r.$,{onClick:V,disabled:F||!D.title||!D.content,children:[F&&(0,n.jsx)(j.A,{className:"mr-2 h-4 w-4 animate-spin"}),"Update Announcement"]})]})]})]})})]})}}},e=>{var s=s=>e(e.s=s);e.O(0,[5003,6221,4358,1071,7968,8441,1684,7358],()=>s(7968)),_N_E=e.O()}]);