"use strict";(()=>{var e={};e.id=3904,e.ids=[3904],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},58246:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>w,routeModule:()=>x,serverHooks:()=>g,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{DELETE:()=>h,POST:()=>p});var a=t(96559),n=t(48088),o=t(37719),i=t(32190),u=t(19854),d=t(41098),l=t(79464),c=t(99326);async function p(e,{params:r}){try{let{id:t}=await r,s=await (0,u.getServerSession)(d.N);if(!s?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});let a=await l.z.lead.findUnique({where:{id:t},include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}}}});if(!a)return i.NextResponse.json({error:"Lead not found"},{status:404});if("GROUP_ASSIGNED"!==a.status)return i.NextResponse.json({error:"Only leads with assigned groups can be archived"},{status:400});if(a.archivedAt)return i.NextResponse.json({error:"Lead is already archived"},{status:400});let n=new Date,o=await l.z.lead.update({where:{id:t},data:{status:"ARCHIVED",archivedAt:n,updatedAt:n},include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},assignedTeacher:{include:{user:{select:{name:!0}}}},callRecords:{orderBy:{createdAt:"desc"},take:1}}});return await c._.logLeadContacted(s.user.id,s.user.role,a.id,{leadName:a.name,leadPhone:a.phone,previousStatus:a.status,newStatus:"ARCHIVED",notes:`Lead archived after group assignment to: ${a.assignedGroup?.name}`},e),i.NextResponse.json({lead:o})}catch(e){return console.error("Error archiving lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e,{params:r}){try{let{id:t}=await r,s=await (0,u.getServerSession)(d.N);if(!s?.user)return i.NextResponse.json({error:"Unauthorized"},{status:401});if("ADMIN"!==s.user.role)return i.NextResponse.json({error:"Only admins can unarchive leads"},{status:403});let a=await l.z.lead.findUnique({where:{id:t}});if(!a)return i.NextResponse.json({error:"Lead not found"},{status:404});if(!a.archivedAt)return i.NextResponse.json({error:"Lead is not archived"},{status:400});let n=new Date,o=await l.z.lead.update({where:{id:t},data:{status:"GROUP_ASSIGNED",archivedAt:null,updatedAt:n},include:{assignedGroup:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},assignedTeacher:{include:{user:{select:{name:!0}}}}}});return await c._.logLeadContacted(s.user.id,s.user.role,a.id,{leadName:a.name,leadPhone:a.phone,previousStatus:"ARCHIVED",newStatus:"GROUP_ASSIGNED",notes:"Lead unarchived by admin"},e),i.NextResponse.json({lead:o})}catch(e){return console.error("Error unarchiving lead:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let x=new a.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/leads/[id]/archive/route",pathname:"/api/leads/[id]/archive",filename:"route",bundlePath:"app/api/leads/[id]/archive/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\leads\\[id]\\archive\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:m,serverHooks:g}=x;function w(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:m})}},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,3412,1971],()=>t(58246));module.exports=s})();