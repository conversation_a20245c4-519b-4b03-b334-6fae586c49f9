(()=>{var e={};e.id=558,e.ids=[558],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},51938:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>m,serverHooks:()=>y,workAsyncStorage:()=>h,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>p,POST:()=>d});var n=t(96559),a=t(48088),o=t(37719),i=t(32190),u=t(79464),c=t(45697);let l=c.Ik({name:c.Yj().min(1),courseId:c.Yj(),teacherId:c.Yj(),capacity:c.ai().min(1).max(50),schedule:c.Yj(),room:c.Yj().optional(),cabinetId:c.Yj().optional(),branch:c.Yj(),startDate:c.Yj(),endDate:c.Yj()});async function p(e){try{let{searchParams:r}=new URL(e.url),t=parseInt(r.get("page")||"1"),s=parseInt(r.get("limit")||"20"),n=r.get("search"),a=r.get("branch"),o=r.get("isActive"),c={};n&&(c.OR=[{name:{contains:n,mode:"insensitive"}},{course:{name:{contains:n,mode:"insensitive"}}},{teacher:{user:{name:{contains:n,mode:"insensitive"}}}}]),a&&(c.branch="main"===a?"Main Branch":"Branch"),null!==o&&(c.isActive="true"===o);let[l,p]=await Promise.all([u.z.group.findMany({where:c,include:{course:{select:{name:!0,level:!0}},teacher:{select:{id:!0,tier:!0,subject:!0,user:{select:{name:!0}}}},enrollments:{where:{status:"ACTIVE"},include:{student:{include:{user:{select:{id:!0,name:!0,phone:!0}}}}}},_count:{select:{enrollments:!0}}},orderBy:{createdAt:"desc"},skip:(t-1)*s,take:s}),u.z.group.count({where:c})]),d={A_LEVEL:1,B_LEVEL:2,C_LEVEL:3,NEW:4},m=e=>{try{let r=e;try{let t=JSON.parse(e);r=Array.isArray(t)?t.join(" "):e}catch{r=e}let t=r.match(/(\d{1,2}):(\d{2})-(\d{1,2}):(\d{2})/),s=t?`${t[1]}:${t[2]}-${t[3]}:${t[4]}`:"Unknown",n=r.toLowerCase(),a=n.includes("monday")&&n.includes("wednesday")&&n.includes("friday")?"MWF":n.includes("tuesday")&&n.includes("thursday")&&n.includes("saturday")?"TTS":"Other";return{time:s,days:a}}catch(r){return console.error("Error parsing schedule:",r,"Schedule:",e),{time:"Unknown",days:"Unknown"}}},h=l.reduce((e,r)=>{let{time:t,days:s}=m(r.schedule),n=`${r.course.level}-${s}-${t}`;return e[n]||(e[n]={courseLevel:r.course.level,days:s,time:t,groups:[]}),e[n].groups.push(r),e},{}),g=Object.entries(h).map(([e,r])=>{let t=r.groups.reduce((e,r)=>{let t=r.teacher.tier||"NEW";return e[t]||(e[t]=[]),e[t].push(r),e},{}),s=Object.entries(t).map(([e,r])=>{let t=r.reduce((e,r)=>e+r.capacity,0),s=r.reduce((e,r)=>e+r._count.enrollments,0);return{tier:e,groupCount:r.length,utilizationRate:Math.round(100*(t>0?s/t*100:0))/100,priority:d[e]||4,groups:r}}).sort((e,r)=>e.priority-r.priority),n=[];for(let e of s)if("A_LEVEL"===e.tier)n.push(e.tier);else{let r=s.filter(r=>r.priority<e.priority);(r.every(e=>e.utilizationRate>=80)||0===r.length)&&n.push(e.tier)}return{slotKey:e,courseLevel:r.courseLevel,days:r.days,time:r.time,tierUtilization:s,availableTiers:n,totalGroups:r.groups.length}});return i.NextResponse.json({groups:l,pagination:{page:t,limit:s,total:p,pages:Math.ceil(p/s)},slotTierAnalysis:g})}catch(e){return console.error("Error fetching groups:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}async function d(e){try{let r=await e.json(),t=l.parse(r),s=await u.z.group.create({data:{...t,startDate:new Date(t.startDate),endDate:new Date(t.endDate)},include:{course:{select:{name:!0,level:!0}},teacher:{select:{id:!0,tier:!0,subject:!0,user:{select:{name:!0}}}},_count:{select:{enrollments:!0}}}});return i.NextResponse.json(s,{status:201})}catch(e){if(e instanceof c.G)return i.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating group:",e),i.NextResponse.json({error:"Internal server error"},{status:500})}}let m=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/groups/route",pathname:"/api/groups",filename:"route",bundlePath:"app/api/groups/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\groups\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:h,workUnitAsyncStorage:g,serverHooks:y}=m;function v(){return(0,o.patchFetch)({workAsyncStorage:h,workUnitAsyncStorage:g})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},78335:()=>{},79464:(e,r,t)=>{"use strict";t.d(r,{z:()=>n});var s=t(96330);let n=globalThis.prisma??new s.PrismaClient},96330:e=>{"use strict";e.exports=require("@prisma/client")},96487:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697],()=>t(51938));module.exports=s})();