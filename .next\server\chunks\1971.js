exports.id=1971,exports.ids=[1971],exports.modules={7786:(e,t,s)=>{"use strict";function r(e){let t=e.headers.get("X-Inter-Server-Secret"),s=e.headers.get("X-Timestamp"),r=process.env.INTER_SERVER_SECRET;if(!t||!r||t!==r)return!1;if(s){let e=parseInt(s),t=Date.now();if(isNaN(e)||t-e>3e5)return console.warn("Inter-server request rejected: timestamp too old or invalid"),!1}return!0}async function a(e,t){try{let s="admin"===e?process.env.ADMIN_SERVER_URL:process.env.STAFF_SERVER_URL;if(!s)throw Error(`${e.toUpperCase()}_SERVER_URL not configured`);let r=`${s}${t.endpoint}`,a={...function(){let e=process.env.INTER_SERVER_SECRET;if(!e)throw Error("INTER_SERVER_SECRET not configured");let t=Date.now().toString(),s=i.getServerConfig(),r=`${s.serverType}-${t}-${Math.random().toString(36).substr(2,9)}`;return{"Content-Type":"application/json","X-Inter-Server-Secret":e,"X-Source-Server":s.serverType,"X-Request-ID":r,"X-Timestamp":t,"User-Agent":`${s.serverType}-server`}}(),...t.headers},n=await fetch(r,{method:t.method,headers:a,body:t.data?JSON.stringify(t.data):void 0}),o=await n.json();return{success:n.ok,data:o,status:n.status,error:n.ok?void 0:o.error||"Request failed"}}catch(e){return{success:!1,status:500,error:e instanceof Error?e.message:"Unknown error"}}}s.d(t,{LQ:()=>i,cU:()=>n,g2:()=>r});class n{static async authenticateUser(e,t){return a("admin",{endpoint:"/api/inter-server/auth/validate",method:"POST",data:{phone:e,password:t}})}static async getUserData(e){return a("admin",{endpoint:`/api/inter-server/users/${e}`,method:"GET"})}static async syncData(e,t){return a("admin",{endpoint:"/api/inter-server/sync",method:"POST",data:{type:e,data:t}})}}class i{static logRequest(e,t,s,r){let a=new Date().toISOString(),n=process.env.SERVER_TYPE||"unknown";console.log(`[${a}] Inter-Server ${e.toUpperCase()}: ${t}`,{server:n,success:s,details:r})}static async healthCheck(e){try{return(await a(e,{endpoint:"/api/health",method:"GET"})).success}catch(e){return!1}}static getServerConfig(){return{serverType:process.env.SERVER_TYPE||"staff",adminUrl:process.env.ADMIN_SERVER_URL||"http://localhost:3001",staffUrl:process.env.STAFF_SERVER_URL||"http://localhost:3000",hasInterServerSecret:!!process.env.INTER_SERVER_SECRET}}}},41098:(e,t,s)=>{"use strict";s.d(t,{N:()=>n});var r=s(13581),a=s(7786);let n={providers:[(0,r.A)({name:"credentials",credentials:{phone:{label:"Phone",type:"text"},password:{label:"Password",type:"password"}},async authorize(e){if(!e?.phone||!e?.password)return null;try{let t=await a.cU.authenticateUser(e.phone,e.password);if(!t.success)return console.error("Authentication failed:",t.error),null;let s=t.data.user;if(!s)return console.error("No user data returned from admin server"),null;if(!["RECEPTION","ACADEMIC_MANAGER","TEACHER","MANAGER"].includes(s.role))return console.error("User role not allowed on staff server:",s.role),null;return{id:s.id,phone:s.phone,name:s.name,email:s.email,role:s.role}}catch(e){return console.error("Error authenticating user via inter-server:",e),null}}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:e,user:t})=>(t&&(e.role=t.role||null),e),session:async({session:e,token:t})=>(t&&(e.user.id=t.sub,e.user.role=t.role||null),e)},pages:{signIn:"/auth/signin"}}},78335:()=>{},79464:(e,t,s)=>{"use strict";s.d(t,{z:()=>a});var r=s(96330);let a=globalThis.prisma??new r.PrismaClient},96487:()=>{},99326:(e,t,s)=>{"use strict";s.d(t,{_:()=>a});var r=s(79464);class a{static async log(e){try{await r.z.activityLog.create({data:{userId:e.userId,userRole:e.userRole,action:e.action,resource:e.resource,resourceId:e.resourceId,details:e.details,ipAddress:e.ipAddress,userAgent:e.userAgent}})}catch(e){console.error("Failed to log activity:",e)}}static async logStudentCreated(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"CREATE",resource:"student",resourceId:s,details:{studentData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logStudentUpdated(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"UPDATE",resource:"student",resourceId:s,details:{changes:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logStudentDeleted(e,t,s,r){await this.log({userId:e,userRole:t,action:"DELETE",resource:"student",resourceId:s,details:{},ipAddress:this.getIpAddress(r),userAgent:this.getUserAgent(r)})}static async logStudentStatusChanged(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"STATUS_CHANGE",resource:"student",resourceId:s,details:{statusData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logDroppedStudentContacted(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"CONTACT",resource:"dropped_student",resourceId:s,details:{contactData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logPaymentCreated(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"CREATE",resource:"payment",resourceId:s,details:{paymentData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logPaymentUpdated(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"UPDATE",resource:"payment",resourceId:s,details:{changes:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logGroupCreated(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"CREATE",resource:"group",resourceId:s,details:{groupData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logEnrollmentCreated(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"CREATE",resource:"enrollment",resourceId:s,details:{enrollmentData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logLeadContacted(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"CONTACT",resource:"lead",resourceId:s,details:{contactDetails:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logAssessmentCompleted(e,t,s,r,a){await this.log({userId:e,userRole:t,action:"COMPLETE",resource:"assessment",resourceId:s,details:{assessmentData:r},ipAddress:this.getIpAddress(a),userAgent:this.getUserAgent(a)})}static async logLogin(e,t,s){await this.log({userId:e,userRole:t,action:"LOGIN",resource:"auth",details:{},ipAddress:this.getIpAddress(s),userAgent:this.getUserAgent(s)})}static async logLogout(e,t,s){await this.log({userId:e,userRole:t,action:"LOGOUT",resource:"auth",details:{},ipAddress:this.getIpAddress(s),userAgent:this.getUserAgent(s)})}static getIpAddress(e){if(!e)return;let t=e.headers.get("x-forwarded-for"),s=e.headers.get("x-real-ip");return t?t.split(",")[0].trim():s||void 0}static getUserAgent(e){if(e)return e.headers.get("user-agent")||void 0}static async getActivityLogs(e){let{userId:t,userRole:s,action:a,resource:n,startDate:i,endDate:o,page:d=1,limit:c=50}=e,l={};t&&(l.userId=t),s&&(l.userRole=s),a&&(l.action=a),n&&(l.resource=n),(i||o)&&(l.createdAt={},i&&(l.createdAt.gte=i),o&&(l.createdAt.lte=o));let[u,g]=await Promise.all([r.z.activityLog.findMany({where:l,include:{user:{select:{id:!0,name:!0,email:!0,role:!0}}},orderBy:{createdAt:"desc"},skip:(d-1)*c,take:c}),r.z.activityLog.count({where:l})]);return{logs:u,total:g,page:d,limit:c,totalPages:Math.ceil(g/c)}}static async getReceptionKPIs(e,t){return Object.values((await r.z.activityLog.findMany({where:{action:"CREATE",resource:"student",userRole:"RECEPTION",createdAt:{gte:e,lte:t}},include:{user:{select:{id:!0,name:!0}}}})).reduce((e,t)=>{let s=t.userId,r=t.user.name;return e[s]||(e[s]={userId:s,userName:r,studentsAdded:0}),e[s].studentsAdded++,e},{}))}static async getCallCentreKPIs(e,t){return Object.values((await r.z.activityLog.findMany({where:{action:"CONTACT",resource:"lead",userRole:{in:["RECEPTION","MANAGER"]},createdAt:{gte:e,lte:t}},include:{user:{select:{id:!0,name:!0}}}})).reduce((e,t)=>{let s=t.userId,r=t.user.name;return e[s]||(e[s]={userId:s,userName:r,leadsContacted:0}),e[s].leadsContacted++,e},{}))}}}};