"use strict";(()=>{var e={};e.id=2381,e.ids=[2381],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:e=>{e.exports=require("querystring")},12412:e=>{e.exports=require("assert")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},94735:e=>{e.exports=require("events")},96330:e=>{e.exports=require("@prisma/client")},98837:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>q,routeModule:()=>g,serverHooks:()=>j,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>b,POST:()=>h});var n=t(96559),a=t(48088),i=t(37719),o=t(32190),u=t(19854),c=t(41098),p=t(79464),l=t(99326),d=t(96330),x=t(45697);let m=x.Ik({name:x.Yj().min(1,"Cabinet name is required"),number:x.Yj().min(1,"Cabinet number is required"),capacity:x.ai().min(1,"Capacity must be at least 1").max(100,"Capacity cannot exceed 100"),floor:x.ai().optional(),building:x.Yj().optional(),branch:x.Yj().min(1,"Branch is required"),equipment:x.Yj().optional(),notes:x.Yj().optional(),isActive:x.zM().default(!0)});async function b(e){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:t}=new URL(e.url),s=parseInt(t.get("page")||"1"),n=parseInt(t.get("limit")||"20"),a=t.get("search"),i=t.get("branch"),l=t.get("isActive"),d={};a&&(d.OR=[{name:{contains:a,mode:"insensitive"}},{number:{contains:a,mode:"insensitive"}},{building:{contains:a,mode:"insensitive"}}]),i&&(d.branch="main"===i?"Main Branch":"Branch"),null!==l&&(d.isActive="true"===l);let x=(s-1)*n,[m,b]=await Promise.all([p.z.cabinet.findMany({where:d,include:{groups:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},_count:{select:{groups:!0,schedules:!0}}},skip:x,take:n,orderBy:{createdAt:"desc"}}),p.z.cabinet.count({where:d})]);return o.NextResponse.json({cabinets:m,pagination:{page:s,limit:n,total:b,pages:Math.ceil(b/n)}})}catch(e){return console.error("Error fetching cabinets:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}async function h(e){try{let r=await (0,u.getServerSession)(c.N);if(!r?.user)return o.NextResponse.json({error:"Unauthorized"},{status:401});if(!r.user.role||!["ADMIN","MANAGER"].includes(r.user.role))return o.NextResponse.json({error:"Forbidden"},{status:403});let t=await e.json(),s=m.parse(t);if(await p.z.cabinet.findFirst({where:{number:s.number,branch:s.branch}}))return o.NextResponse.json({error:"Cabinet number already exists in this branch"},{status:400});let n=await p.z.cabinet.create({data:s,include:{groups:{include:{course:{select:{name:!0,level:!0}},teacher:{include:{user:{select:{name:!0}}}}}},_count:{select:{groups:!0,schedules:!0}}}});return await l._.log({userId:r.user.id,userRole:r.user.role||d.Role.ADMIN,action:"CREATE",resource:"CABINET",resourceId:n.id,details:`Created cabinet ${n.name} (${n.number})`}),o.NextResponse.json(n,{status:201})}catch(e){if(e instanceof x.G)return o.NextResponse.json({error:"Invalid data",details:e.errors},{status:400});return console.error("Error creating cabinet:",e),o.NextResponse.json({error:"Internal server error"},{status:500})}}let g=new n.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/cabinets/route",pathname:"/api/cabinets",filename:"route",bundlePath:"app/api/cabinets/route"},resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\codes\\inno-crm-staff\\app\\api\\cabinets\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:v,workUnitAsyncStorage:f,serverHooks:j}=g;function q(){return(0,i.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:f})}}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[4243,580,5697,3412,1971],()=>t(98837));module.exports=s})();